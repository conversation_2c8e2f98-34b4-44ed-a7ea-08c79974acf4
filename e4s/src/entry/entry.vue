<template>
  <div>
    <div class="col s12 m12">
      <ul id="topCollapsible" class="collapsible">
        <li id="topCollapsibleItem" class="">
          <div
            id="competitionContainer"
            class="collapsible-header white-text competition-collapsible-header"
            :class="'e4s-primary-color-bg-' + configApp.theme"
          >
            <div class="row e4s-collapsible-row">
              <div class="col s10 m10 l10">
                <span v-text="getCompName"></span>

                <div class="e4s-force-inline-block comp-close-date">
                  <span v-text="competitionEntryCloseUserString"></span>
                </div>
                <loading-spinner v-if="isLoadingClubs"></loading-spinner>
                <ButtonGotoBuilderV2
                  v-if="hasBuilderPermissionForComp"
                  :comp-id="selectedCompetition.id"
                  button-type="secondary"
                  button-text="Builder"
                />

                <div v-show="false">
                  <club-dropdown
                    class="e4s-force-inline-block e4s-select e4s-select-eighty"
                    :autoSelectId="autoSelect.compOrgId"
                    :clubs="compOrgs"
                    :is-loading="isLoadingClubs"
                    v-on:ON_SELECT="onSelectedClub"
                  >
                  </club-dropdown>
                  <competition-dropdown
                    class="e4s-force-inline-block e4s-select e4s-select-eighty"
                    v-show="showCompDropDown"
                    :autoSelectId="autoSelect.compId"
                    :id="compOrgId"
                    :competitions="competitions"
                    :isLoading="competitionsLoading"
                    v-on:onSelected="onSelectedCompetition"
                  >
                  </competition-dropdown>
                </div>
              </div>

              <div class="col s10 m10 l3" v-if="false">
                <div class="e4s-force-inline-block">
                  <i class="material-icons e4s-force-inline-block"
                    >location_on</i
                  >
                </div>

                <span v-text="selectedCompetition.name"></span>
                <ButtonGotoBuilderV2 :comp-id="selectedCompetition.id" />

                <div>
                  <club-dropdown
                    class="e4s-force-inline-block e4s-select e4s-select-eighty"
                    :autoSelectId="autoSelect.compOrgId"
                    :clubs="compOrgs"
                    :is-loading="isLoadingClubs"
                    v-on:ON_SELECT="onSelectedClub"
                  >
                  </club-dropdown>
                  <loading-spinner v-if="isLoadingClubs"></loading-spinner>
                  <div class="section hide-on-large-only">
                    <i class="material-icons">location_on</i>
                    <competition-dropdown
                      class="
                        e4s-force-inline-block e4s-select e4s-select-eighty
                      "
                      v-show="showCompDropDown"
                      :autoSelectId="autoSelect.compId"
                      :id="compOrgId"
                      :competitions="competitions"
                      :isLoading="competitionsLoading"
                      v-on:onSelected="onSelectedCompetition"
                    >
                    </competition-dropdown>

                    <EntryBuilderLink
                      v-if="hasBuilderPermissionForComp"
                      :comp-id="selectedCompetition.id"
                    ></EntryBuilderLink>
                    <div class="e4s-force-inline-block comp-close-date">
                      <span v-text="competitionEntryCloseUserString"></span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col l4 hide-on-med-and-down" v-if="false">
                <div v-show="showCompDropDown">
                  <competition-dropdown
                    class="e4s-force-inline-block e4s-select e4s-select-eighty"
                    v-show="showCompDropDown"
                    :autoSelectId="autoSelect.compId"
                    :id="compOrgId"
                    :competitions="competitions"
                    :isLoading="competitionsLoading"
                    v-on:onSelected="onSelectedCompetition"
                  >
                  </competition-dropdown>
                </div>
              </div>

              <div class="col s2 m2 l2 right-align">
                <div class="right"></div>
              </div>
            </div>
          </div>
          <div v-show="competitionInfoExpanded">
            <div class="competition-info">
              <!--              <entry-info :competition="competition"></entry-info>-->
            </div>
          </div>
        </li>
      </ul>
    </div>

    <div class="col s12 m5">
      <ul id="leftCollapsible" class="collapsible athlete-team-section">
        <li
          class="active"
          id="teamContainer"
          v-show="isSelectedCompetitionTeam"
          v-if="showThisSection('TEAMS')"
        >
          <div
            id="athleteHeader"
            class="collapsible-header white-text"
            :class="'e4s-primary-color-bg-' + configApp.theme"
          >
            <div class="pull-left">
              <div>
                <i class="material-icons">group</i>&nbsp;Teams&nbsp;&nbsp;
                <span
                  v-if="selectedTeam.teamName"
                  v-html="selectedTeam.teamName"
                ></span>
                <loading-spinner v-show="isTeamsLoading"></loading-spinner>
              </div>
            </div>
            <div class="pull-right">
              <div class="col s10 m10">
                <a
                  v-on:click="openTeamsModal()"
                  class="
                    btn
                    waves-effect waves
                    btn-secondary
                    header-action
                    scale-transition scale-out
                  "
                  v-bind:class="{
                    'scale-in': isSelectedCompetitionTeam,
                    'scale-out': !isSelectedCompetitionTeam,
                  }"
                  href="#"
                >
                  <i class="material-icons left">add</i>
                  Add
                </a>
              </div>
              <div class="col s2 m2">
                <a
                  v-if="!teamsExpanded"
                  @click.prevent="openTeams()"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">add</i>
                </a>
                <a
                  v-if="teamsExpanded"
                  @click.prevent="closeTeams()"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">remove</i>
                </a>
              </div>
            </div>
          </div>
          <div class="collapsible-body team-body">
            <team-select v-bind:competition="competition"></team-select>
          </div>
        </li>

        <li v-show="showTeamEvents" v-if="showThisSection('TEAMS')">
          <div
            class="collapsible-header white-text"
            :class="'e4s-primary-color-bg-' + configApp.theme"
            v-on:click.prevent="
              eventTeamExpanded
                ? openEventTeamPanel(false)
                : openEventTeamPanel(true)
            "
          >
            <div class="row e4s-collapsible-row">
              <div class="col s10 m10 l10">
                <div>
                  <i class="material-icons">group</i>
                  Team Events<span v-text="getEventTeamHeadersMessage"></span>
                </div>
              </div>

              <div class="col s2 m2 l2 right-align">
                <a
                  v-if="!eventTeamExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">add</i>
                </a>
                <a
                  v-if="eventTeamExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">remove</i>
                </a>
              </div>
            </div>
          </div>
          <div v-show="eventTeamExpanded">
            <event-teams-panel class="e4s-section-wrapper"></event-teams-panel>
          </div>
        </li>

        <li
          id="athleteContainer"
          v-show="!isHideAtheletes"
          v-if="showThisSection('ATHLETES')"
        >
          <div
            class="collapsible-header white-text"
            :class="'e4s-primary-color-bg-' + configApp.theme"
            v-on:click.prevent="
              athletesExpanded ? closeAthletes() : openAthletes()
            "
          >
            <div class="row e4s-collapsible-row">
              <div class="col s10 m10 l10">
                <div>
                  <i class="material-icons">directions_run</i>
                  Athletes
                </div>
              </div>

              <div class="col s2 m2 l2 right-align">
                <a
                  v-if="!athletesExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">add</i>
                </a>
                <a
                  v-if="athletesExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">remove</i>
                </a>
              </div>
            </div>
          </div>
          <div v-show="athletesExpanded">
            <athlete-grid
              class="e4s-section-wrapper"
              v-bind:competition="competition"
              v-bind:selectedTeam="selectedTeam"
              v-bind:athletesVisible="athletesExpanded"
              v-bind:triggerRefresh="triggerRefreshAthlete"
              v-on:onSelected="onSelectedAthlete"
            >
            </athlete-grid>
          </div>
        </li>
      </ul>
    </div>
    <div class="col s12 m7">
      <ul id="rightCollapsible" class="collapsible">
        <li
          class="active"
          id="scheduleContainer"
          v-show="!isHideSchedule"
          v-if="showThisSection('SCHEDULE')"
        >
          <div
            class="collapsible-header white-text"
            :class="'e4s-primary-color-bg-' + configApp.theme"
            v-on:click.prevent="
              scheduleExpanded ? openSchedule(false) : openSchedule(true)
            "
          >
            <div class="row e4s-collapsible-row">
              <div class="col s10 m10 l10">
                <div>
                  <i class="material-icons">access_time</i>&nbsp;
                  <span v-text="$t('entry.scheduleHeader')"></span>
                  <div v-if="athlete.surName" class="e4s-force-inline-block">
                    <span v-text="athlete.firstName"></span>
                    <span v-text="athlete.surName"></span>
                    (<span v-text="athleteClubName"></span>)
                    <ageinfo
                      class="e4s-force-inline-block"
                      v-bind:ageInfo="getAgeInfo"
                    >
                    </ageinfo>
                  </div>
                  <div
                    v-if="showSelectAthleteMessage"
                    class="e4s-force-inline-block"
                  >
                    Select an athlete
                  </div>
                </div>
              </div>

              <div class="col s2 m2 l2 right-align">
                <a
                  v-if="!scheduleExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">add</i>
                </a>
                <a
                  v-if="scheduleExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">remove</i>
                </a>
              </div>
            </div>
          </div>
          <div v-show="scheduleExpanded">
            <!--                        <athlete-comp-sched-grid-->
            <!--                                class="e4s-section-wrapper"-->
            <!--                                v-bind:competition="competition"-->
            <!--                                v-bind:athlete="athlete"-->
            <!--                                v-on:openShop="openSecondary(true)"-->
            <!--                        >-->
            <!--                        </athlete-comp-sched-grid>-->
            <EventsCardGrid
              class="e4s-section-wrapper"
              v-on:openShop="openSecondary(true)"
            ></EventsCardGrid>
          </div>
        </li>

        <!--EntityEntriesLoader-->
        <li id="entity-entries-loader" v-if="getCompHasClubCompInfoCompetition">
          <!--EntityEntriesLoader--Header-->
          <div
            class="collapsible-header white-text"
            :class="'e4s-primary-color-bg-' + configApp.theme"
            v-on:click.prevent="openEntityEntries(!entityEntriesExpanded)"
          >
            <!--EntityEntriesLoader--HeaderContent-->
            <div class="row e4s-collapsible-row">
              <div class="col s10 m10 l5">
                <div>
                  <i class="material-icons">shop</i>
                  Your Entries
                </div>
                <div class="hide-on-large-only">
                  <!--                                    <ordersummary v-bind:orderSummary="getOrderSummary"></ordersummary>-->
                </div>
              </div>

              <div class="col l5 hide-on-med-and-down">
                <!--                                <ordersummary v-bind:orderSummary="getOrderSummary"></ordersummary>-->
              </div>

              <div class="col s2 m2 l2 right-align">
                <a
                  v-if="!entityEntriesExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">add</i>
                </a>
                <a
                  v-if="entityEntriesExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">remove</i>
                </a>
              </div>
            </div>
            <!--/EntityEntriesLoader--HeaderContent-->
          </div>

          <!--EntityEntriesLoader--Body-->
          <EntityEntriesLoader
            style="padding: 8px"
            v-if="entityEntriesExpanded"
            :comp-id="selectedCompetition.id"
            :club-comp-info="clubCompInfo"
          />
        </li>
        <!--/EntityEntriesLoader-->

        <!--Secondary-->
        <li id="secondaryContainer" v-if="showThisSection('SHOP')">
          <!--SecondaryHeader-->
          <div
            class="collapsible-header white-text"
            :class="'e4s-primary-color-bg-' + configApp.theme"
            v-on:click.prevent="openSecondary(!secondaryExpanded)"
          >
            <!--SecondaryHeaderContent-->
            <div class="row e4s-collapsible-row">
              <div class="col s10 m10 l5">
                <div>
                  <i class="material-icons">shop</i>
                  Shop
                </div>
                <div class="hide-on-large-only">
                  <!--                                    <ordersummary v-bind:orderSummary="getOrderSummary"></ordersummary>-->
                </div>
              </div>

              <div class="col l5 hide-on-med-and-down">
                <!--                                <ordersummary v-bind:orderSummary="getOrderSummary"></ordersummary>-->
              </div>

              <div class="col s2 m2 l2 right-align">
                <a
                  v-if="!secondaryExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">add</i>
                </a>
                <a
                  v-if="secondaryExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">remove</i>
                </a>
              </div>
            </div>
            <!--/SecondaryHeaderContent-->
          </div>

          <!--SecondaryBody-->
          <div v-show="secondaryExpanded">
            <SecondaryCustFormGrid
              :secondary-ref-obj="getSecondaryRefObj"
            ></SecondaryCustFormGrid>

            <!--                        <div class="row">-->
            <!--                            <div class="col s12 m12 l12">-->
            <!--                                <button class="btn waves-effect waves green right" style="margin: 5px"-->
            <!--                                        v-on:click.stop="openWooBasket()">-->
            <!--                                    <span>Proceed to checkout</span>-->
            <!--                                </button>-->
            <!--                            </div>-->
            <!--                        </div>-->
          </div>
          <!--/SecondaryBody-->
        </li>
        <!--/Secondary-->

        <!--Cart-->
        <!--                summaryExpanded ? openSummary(false) : openSummary(true)"-->
        <li id="orderSummaryContainer" v-show="false">
          <div
            class="collapsible-header white-text"
            :class="'e4s-primary-color-bg-' + configApp.theme"
            v-on:click.prevent="openCart"
          >
            <div class="row e4s-collapsible-row">
              <div class="col s10 m10 l5">
                <div>
                  <i class="material-icons">shopping_cart</i>
                  Order Summary
                </div>
                <div class="hide-on-large-only">
                  <ordersummary
                    v-bind:orderSummary="getOrderSummary"
                  ></ordersummary>
                </div>
              </div>

              <div class="col l5 hide-on-med-and-down">
                <ordersummary
                  v-bind:orderSummary="getOrderSummary"
                ></ordersummary>
              </div>

              <div class="col s2 m2 l2 right-align">
                <a
                  v-if="!summaryExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">add</i>
                </a>
                <a
                  v-if="summaryExpanded"
                  class="collapsible-toggle-custom e4s-expander"
                  href="#"
                >
                  <i class="material-icons">remove</i>
                </a>
              </div>
            </div>
          </div>
          <div v-show="false">
            <!--                        <div v-show="summaryExpanded">-->
            <cart
              class="e4s-section-wrapper"
              v-bind:athlete="athlete"
              v-bind:competition="competition"
              v-bind:eventsSelected="eventsSelected"
            >
            </cart>
          </div>
        </li>
        <!--/Cart-->
      </ul>
    </div>
    <!--        <div id="createAthleteModal" class="modal" v-bind:class="athleteShowModal ? 'modal-open' : ''">-->
    <!--            <athlete-modal-mat></athlete-modal-mat>-->
    <!--        </div>-->

    <div
      id="createTeamModal"
      class="modal"
      v-bind:class="createTeamShowModal ? 'modal-open' : ''"
    >
      <team-entry-modal-mat></team-entry-modal-mat>
    </div>

    <ModalV2
      v-if="showAthleteGenericEntityPicker"
      :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
      :always-show-header-blank="true"
    >
      <div
        slot="body"
        style="
          padding: var(--e4s-gap--standard);
          height: 500px;
          width: 500px;
          overflow-x: hidden;
        "
      >
        <GenericClubEntityPicker
          :builder-competition="competition"
          :athlete="athlete"
          @input="saveGenericEntityPicker"
          @onCancel="hideGenericEntityPicker"
        />
      </div>
    </ModalV2>

    <LoadingSpinnerV2
      v-if="isSelectedAthleteLoading"
      loading-message="Loading selected athlete..."
    />

    <!--        <event-switch-modal></event-switch-modal>-->
  </div>
</template>

<script lang="ts">
import Component from "vue-class-component";
import { Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import AthleteGrid from "../athlete/athlete-grid.vue";
import { IAthleteSummary } from "../athlete/athlete-models";
import AgeInfo from "../athletecompsched/ageinfo.vue";
import EventTeamsPanel from "../athletecompsched/comp-event-teams/event-teams-panel.vue";
import OrderSummary from "../athletecompsched/order-summary.vue";
import ShowCompSched from "../athletecompsched/showcompsched.vue";
import {
  ATH_COMP_SCHED_STORE_CONST,
  IAthCompSchedStoreState,
} from "../athletecompsched/store/athletecompsched-store";
import Cart from "../cart/cart.vue";
import ClubDropDown from "../club/club-dropdown.vue";
import { CLUB_STORE_CONST, IClubStoreState } from "../club/club-store";
import LoadingSpinner from "../common/ui/loading-spinner.vue";
import CompetitionDropDown from "../competition/competition-dropdown.vue";
import { ICompetitionInfo } from "../competition/competition-models";
import { CONFIG_STORE_CONST, IConfigStoreState } from "../config/config-store";
import Flyer from "../flyer/flyer.vue";
import Location from "../location/location.vue";
import Schedule from "../schedule/schedule.vue";
import ShowEntries from "../showentries/showentries.vue";
import TeamEntryModalMat from "../team/entry/team-entry-modal-mat.vue";
import TeamSelect from "../team/select/team-select.vue";
import { ITeam } from "../team/team-models";
import { ITeamStoreState, TEAM_STORE_CONST } from "../team/team-store";
import UserMessagesTable from "../user-message/user-message-table.vue";
import { ENTRY_STORE_CONST, IEntryStoreState } from "./entry-store";
import EntryTabs from "./entry-tabs.vue";
import { IClub } from "../club/club-models";
import EntryBase from "./entry-base";
import EntryInfo from "./entry-info.vue";
import EntryBuilderLink from "./entry-builder-link.vue";
import { ConfigService } from "../config/config-service";
import { IConfigApp } from "../config/config-app-models";
import { parse, format } from "date-fns";
import { IAthleteCompSchedRuleEvent } from "../athletecompsched/athletecompsched-models";
import { ISecondaryRefObj } from "../secondary/secondary-models";
import { SecondaryService } from "../secondary/secondary-service";
import { CONFIG } from "../common/config";
import EventsCardGrid from "../athletecompsched/events-card-grid.vue";
import SecondaryCustFormGrid from "../secondary/cust/secondary-cust-form-grid.vue";
import { hasClubCompInfoCompetition } from "./v2/schools/clubCompInfoService";
import EntityEntriesLoader from "./v2/entity-entries/EntityEntriesLoader.vue";
import { IClubCompInfo } from "./v2/schools/clubCompInfo-models";
import { ENTRY_SECTION } from "../builder/builder-models";
import ButtonGotoBuilderV2 from "../common/ui/layoutV2/buttons/ButtonGotoBuilderV2.vue";
import ModalV2 from "../common/ui/layoutV2/modal/modal-v2.vue";
import { VUE_MQ_SIZES } from "../index";
import GenericClubEntityPicker from "./v2/GenericClubEntityPicker.vue";
import LoadingSpinnerV2 from "../common/ui/loading-spinner-v2.vue";

const configService: ConfigService = new ConfigService();

// "athlete-comp-sched-grid": AthleteCompSchedGrid,

@Component({
  name: "entry",
  components: {
    LoadingSpinnerV2,
    GenericClubEntityPicker,
    ModalV2,
    ButtonGotoBuilderV2,
    EntityEntriesLoader,
    SecondaryCustFormGrid,
    EventsCardGrid,
    "club-dropdown": ClubDropDown,
    "competition-dropdown": CompetitionDropDown,
    "athlete-grid": AthleteGrid,
    "user-messages-table": UserMessagesTable,
    cart: Cart,
    ageinfo: AgeInfo,
    location: Location,
    flyer: Flyer,
    "show-comp-sched": ShowCompSched,
    "show-entries": ShowEntries,
    ordersummary: OrderSummary,
    "team-select": TeamSelect,
    "team-entry-modal-mat": TeamEntryModalMat,
    "loading-spinner": LoadingSpinner,
    schedule: Schedule,
    "entry-tabs": EntryTabs,
    "event-teams-panel": EventTeamsPanel,
    "entry-info": EntryInfo,
    EntryBuilderLink,
  },
  computed: {
    VUE_MQ_SIZES() {
      return VUE_MQ_SIZES;
    },
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapState(CLUB_STORE_CONST.CLUB_STORE_CONST_MODULE_NAME, {
      compOrgs: (state: IClubStoreState) => state.clubDropDown.clubs,
      isLoadingClubs: (state: IClubStoreState) =>
        state.clubDropDown.clubsLoading,
    }),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      competitions: (state: IEntryStoreState) => state.entryForm.competitions,
      competitionsLoading: (state: IEntryStoreState) =>
        state.entryForm.competitionsLoading,
      athlete: (state: IEntryStoreState) => state.entryForm.selectedAthlete,
      selectedClub: (state: IEntryStoreState) => state.entryForm.club,
      selectedCompetition: (state: IEntryStoreState) =>
        state.entryForm.selectedCompetition,
      athleteMultiPb: (state: IEntryStoreState) =>
        state.entryForm.athleteMultiPb,
      athleteMultiPbModalShow: (state: IEntryStoreState) =>
        state.entryForm.athleteMultiPbModalShow,
      selectedAthleteLoading: (state: IEntryStoreState) =>
        state.entryForm.selectedAthleteLoading,
      triggerRefreshAthlete: (state: IEntryStoreState) =>
        state.entryForm.triggerRefreshAthlete,
      autoSelect: (state: IEntryStoreState) => state.entryForm.autoSelect,
      clubCompInfo: (state: IEntryStoreState) => state.entryForm.clubCompInfo,
      isSelectedAthleteLoading: (state: IEntryStoreState) =>
        state.entryForm.selectedAthleteLoading,
    }),
    ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME, {
      eventsSelected: (state: IAthCompSchedStoreState) => state.eventsSelected,
      ageInfo: (state: IAthCompSchedStoreState) =>
        state.eventsServerResponse.ageInfo,
    }),
    ...mapState(TEAM_STORE_CONST.TEAM_STORE_CONST_MODULE_NAME, {
      selectedTeam: (state: ITeamStoreState) => state.selectedTeam,
      isTeamsLoading: (state: ITeamStoreState) => state.isLoading,
      createTeamShowModal: (state: ITeamStoreState) =>
        state.createTeamShowModal,
    }),
    ...mapGetters({
      getAgeInfo:
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_GETTERS_AGE_INFO,
      getOrderSummary:
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_GETTERS_ORDER_SUMMARY_TOTALS,
      isAthleteFilterOn:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_IS_ATHLETE_SEARCH_FILTER_ON,
      isSelectedCompetitionTeam:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_SELECTED_COMP_IS_TEAM,
      showAddAthleteButton:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_SHOW_ADD_ATHLETE,
      athleteClubName:
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_GETTERS_GET_ATHLETE_CLUB_NAME,
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class Entry extends EntryBase {
  public readonly selectedClub!: IClub;
  public readonly isLoadingClubs!: boolean;
  public readonly configApp!: IConfigApp;
  public readonly selectedCompetition!: ICompetitionInfo;
  public readonly eventsSelected!: IAthleteCompSchedRuleEvent[];
  public readonly clubCompInfo: IClubCompInfo;
  public readonly isSelectedAthleteLoading!: boolean;

  public compOrgId: number = 0;
  public userConfirmedBrowseAway: boolean = false;
  public showRequiresConfirmBrowseAway: boolean = false;

  public secondsRemaining: string = "";
  public minutesRemaining: string = "";
  public hoursRemaining: string = "";
  public daysRemaining: string = "";

  public competitionInfoExpanded = false;
  public scheduleExpanded = false;
  public summaryExpanded = false;
  public teamsExpanded = false;
  public athletesExpanded = false;
  public isLoadingCompetitions = false;
  public secondaryExpanded = false;

  public eventTeamExpanded: boolean = false;
  public competitionSelected: boolean;

  public isHideAtheletes: boolean = false;
  public isHideSchedule: boolean = false;

  public entityEntriesExpanded = false;

  //  see mapstate
  public athlete: IAthleteSummary;

  public mounted() {
    console.log("Entry.vue mounted() A");
    this.openSchedule(true);

    // this.openTeams();
    console.log("Entry.vue mounted() B");
    this.hideShowsections({} as ICompetitionInfo);
    console.log("Entry.vue mounted() C");

    this.resetStore({ resetTeams: true, resetEvents: true });

    this.$store.dispatch(
      CLUB_STORE_CONST.CLUB_STORE_CONST_MODULE_NAME +
        "/" +
        CLUB_STORE_CONST.CLUB_STORE_ACTIONS_GET_COMP_ORGS,
      {
        public: false,
        orgId: this.compOrgId,
      }
    );

    // this.$store.dispatch(
    //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
    //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
    // );
    const dropdown = document.querySelectorAll(".dropdown-trigger");
    (window as any).M.Dropdown.init(dropdown, { constrainWidth: false });
  }

  //
  @Watch("competition")
  public onCompetitionSelectedChangedEntry(newValue: ICompetitionInfo) {
    console.log("Entry.vue onCompetitionSelectedChangedEntry");
    this.hideShowsections(newValue);
  }

  @Watch("selectedTeam")
  public onTeamSelectedChanged(newValue: ITeam) {
    if (newValue && newValue.teamId) {
      console.log("Entry.vue onTeamSelectedChangedEntry");
      this.openAthletes();
    }
  }

  @Watch("athlete")
  public onAthleteSelectedChanged(newValue: IAthleteSummary) {
    if (newValue && newValue.id) {
      if (
        this.competitionService.getEntryDefaultPanel(
          this.selectedCompetition.options
        ) === "SHOP"
      ) {
        console.log("Entry.vue onAthleteSelectedChangedEntry SHOP");
        this.openSecondary(true);
        return;
      }
      console.log("Entry.vue onAthleteSelectedChangedEntry schedule");
      this.openSchedule(true);
    }
  }

  @Watch("eventsSelected")
  public onEventsSelectedChanged() {
    this.userConfirmedBrowseAway = false;
    this.showRequiresConfirmBrowseAway = false;
  }

  public get getCompName() {
    if (
      !this.selectedClub ||
      !this.selectedCompetition ||
      this.selectedClub.id === 0 ||
      this.selectedCompetition.id === 0
    ) {
      return "";
    }
    const compDate =
      this.selectedCompetition.date && this.selectedCompetition.date.length > 0
        ? format(parse(this.selectedCompetition.date), "Do MMM YYYY")
        : "";
    return (
      (this.selectedCompetition.id ? this.selectedCompetition.id : "") +
      ": " +
      this.selectedClub.club +
      " - " +
      (this.selectedCompetition.name ? this.selectedCompetition.name : "") +
      " - " +
      compDate
    );
  }

  public onSelectedClub(club: IClub) {
    this.compOrgId = club.clubid;

    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ENTRYFORM_SET_CLUB,
      club
    );

    this.$store.dispatch(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_COMPETITIONS_BY_ID,
      {
        orgId: this.compOrgId,
        compId: this.compId,
      }
    );
  }

  public hideShowsections(competitionSummary: ICompetitionInfo) {
    console.warn("Entry.vue hideShowsections() A");
    this.hideAthletes(false);
    this.hideSchedule(false);

    // xxxx
    // get cookie value entityEntriesExpanded, if it is true then open that section
    const entityEntriesExpanded = this.windowController.getCookie(
      "entityEntriesExpanded"
    );
    console.warn(
      "* * * * * * * * * Entry.vue entityEntriesExpanded: " +
        entityEntriesExpanded
    );

    if (competitionSummary && competitionSummary.id === 0) {
      return;
    }

    //  This bit bugs me
    if (!competitionSummary) {
      this.closeTeams();
      this.openEventTeamPanel(false);
      this.openAthletes();
      console.warn("Entry.vue hideShowsections() return 1");
      return;
    }

    if (
      this.competitionService.getEntryDefaultPanel(
        this.selectedCompetition.options
      ) === "SHOP"
    ) {
      this.closeTeams();
      this.openAthletes();
      this.hideSchedule(false);
      this.openSecondary(true);
      console.warn("Entry.vue hideShowsections() return 2");
      return;
    }

    if (competitionSummary.teamid > 0) {
      this.openTeams();
      this.openEventTeamPanel(false);
      this.closeAthletes();
      console.warn("Entry.vue hideShowsections() return 3");
      return;
    }

    if (!competitionSummary.indivEvents) {
      this.closeAthletes();
      this.openEventTeamPanel(true);
      this.openSchedule(false);
      this.openSummary(true);
      this.hideAthletes(true);
      this.hideSchedule(true);
      console.warn("Entry.vue hideShowsections() return 4");
      return;
    } else {
      this.hideAthletes(false);
      this.hideSchedule(false);
    }

    if (!competitionSummary || !competitionSummary.teamid) {
      this.closeTeams();
      this.openAthletes();

      this.openSummary(false);

      console.warn(
        "* * * * * * * * * Entry.vue entityEntriesExpanded : " +
          entityEntriesExpanded +
          ", openEntityEntries(true)"
      );
      // If this is school type comp
      if (this.getCompHasClubCompInfoCompetition) {
        if (entityEntriesExpanded === "true") {
          this.openEntityEntries(true);
          return;
        }
      }

      // if (entityEntriesExpanded === "true") {
      //   this.openEntityEntries(true);
      //   return;
      // }
      this.openSchedule(true);

      console.warn("Entry.vue hideShowsections() return 5");
      return;
    }

    console.warn("Entry.vue hideShowsections() B");
    //  this.closeTeams();
    console.warn("Entry.vue hideShowsections() C");
    // this.openAthletes();
    console.warn("Entry.vue hideShowsections() D");

    if (entityEntriesExpanded === "true") {
      console.warn(
        "* * * * * * * * * Entry.vue entityEntriesExpanded : " +
          entityEntriesExpanded +
          ", openEntityEntries(true)"
      );
      this.openEntityEntries(true);
    } else {
      this.athletesExpanded = true;
      this.teamsExpanded = false;
      this.competitionInfoExpanded = false;
      this.eventTeamExpanded = false;
    }
  }

  public openCompetitionInfo(open: boolean) {
    this.competitionInfoExpanded = open;
  }

  public openSchedule(open: boolean) {
    console.warn("Entry.vue openSchedule: " + open);
    this.scheduleExpanded = open;
    this.summaryExpanded = !open;
    this.secondaryExpanded = false;
    this.entityEntriesExpanded = false;
  }

  public openSummary(open: boolean) {
    this.summaryExpanded = open;
    this.scheduleExpanded = !open;
    // this.secondaryExpanded = !open;
  }

  public openCart() {
    console.warn("Entry.vue openCart()");
    // this.$store.dispatch(
    //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
    //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SEND_TO_BASKET
    // );
    window.location.href = CONFIG.WP_BASKET;
  }

  public openSecondary(open: boolean) {
    console.warn("Entry.vue openSecondary: " + open);
    this.secondaryExpanded = open;
    this.scheduleExpanded = !open;
    this.summaryExpanded = !open;
  }

  public openAthletes() {
    console.warn("Entry.vue openAthletes()");
    this.athletesExpanded = true;
    this.teamsExpanded = false;
    this.competitionInfoExpanded = false;
    this.eventTeamExpanded = false;
  }

  public openEntityEntries(open: boolean) {
    console.warn("Entry.vue openEntityEntries: " + open);
    this.entityEntriesExpanded = open;
    this.scheduleExpanded = !open;
    this.summaryExpanded = !open;

    // set a cookie with entityEntriesExpanded state
    this.windowController.setCookie("entityEntriesExpanded", open.toString());
  }

  public closeAthletes() {
    console.warn("Entry.vue closeAthletes()");
    this.athletesExpanded = false;
    this.teamsExpanded = false;
    this.competitionInfoExpanded = false;
    this.eventTeamExpanded = false;
  }

  public hideAthletes(hideIt: boolean) {
    console.warn("Entry.vue hideAthletes: " + hideIt);
    this.isHideAtheletes = hideIt;
  }

  public hideSchedule(hideIt: boolean) {
    console.warn("Entry.vue hideSchedule: " + hideIt);
    this.isHideSchedule = hideIt;
  }

  public openTeams() {
    console.warn("Entry.vue openTeams()");
    this.athletesExpanded = false;
    this.teamsExpanded = true;
    this.competitionInfoExpanded = false;
    this.eventTeamExpanded = false;
  }

  public closeTeams() {
    console.warn("Entry.vue closeTeams()");
    this.athletesExpanded = true;
    this.teamsExpanded = false;
    this.competitionInfoExpanded = false;
    this.eventTeamExpanded = false;
  }

  public openTeamsModal() {
    this.$store.commit(
      TEAM_STORE_CONST.TEAM_STORE_CONST_MODULE_NAME +
        "/" +
        TEAM_STORE_CONST.TEAM_STORE_MUTATIONS_CREATE_TEAM_SHOW_MODAL,
      true
    );
  }

  public beforeRouteLeave(to: any, from: any, next: any) {
    next();
    // const selectedEvents = appStore.state[ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME].eventsSelected;
    // if (selectedEvents.length > 0) {
    //   if (this.userConfirmedBrowseAway) {
    //     next();
    //   } else {
    //     this.showRequiresConfirmBrowseAway = true;
    //     next(false);
    //   }
    // } else {
    //   next();
    // }
  }

  public userCoinfirmBrowseAway() {
    this.userConfirmedBrowseAway = true;
    this.showRequiresConfirmBrowseAway = false;
  }

  public get showSelectAthleteMessage() {
    return this.competitionSelected && !(this.athlete && this.athlete.surName);
  }

  public openEventTeamPanel(openPanel: boolean) {
    console.warn("Entry.vue openEventTeamPanel: " + openPanel);
    this.eventTeamExpanded = openPanel;
    this.athletesExpanded = !openPanel;
  }

  public get hasBuilderPermissionForComp(): boolean {
    return configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.selectedCompetition.compOrgId,
      this.selectedCompetition.id
    );
  }

  public get getSecondaryRefObj(): ISecondaryRefObj {
    const secondaryRefObj = new SecondaryService().factorySecondaryRefObj();
    if (
      this.selectedCompetition &&
      this.selectedCompetition.id &&
      this.selectedCompetition.id > 0
    ) {
      secondaryRefObj.objId = this.selectedCompetition.id;
      secondaryRefObj.objType = "COMP";
      secondaryRefObj.compId = this.selectedCompetition.id;
      secondaryRefObj.objName = this.selectedCompetition.name;
    }
    return secondaryRefObj;
  }

  public get getCompHasClubCompInfoCompetition(): boolean {
    return hasClubCompInfoCompetition(
      this.selectedCompetition.clubCompInfo as IClubCompInfo
    );
  }

  public showThisSection(sectionName: ENTRY_SECTION): boolean {
    console.log("Entry.vue showThisSection: " + sectionName);
    const selectedCompetition = this.selectedCompetition;
    if (!(selectedCompetition && selectedCompetition.id > 0)) {
      return true;
    }
    if (!this.selectedCompetition.options.ui.sectionsToHide) {
      //  A legacy comp and this is not set in the config.
      return true;
    }
    const hideSection =
      this.selectedCompetition.options.ui.sectionsToHide[sectionName];
    return !hideSection;
  }
}
</script>
