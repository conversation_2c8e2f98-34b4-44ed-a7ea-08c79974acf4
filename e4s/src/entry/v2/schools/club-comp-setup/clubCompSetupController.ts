import { reactive } from "@vue/composition-api";
import {
  IClubCompCategory,
  IClubCompDefinition,
} from "./club-comp-setup-models";
import { ClubCompInfoData } from "../clubCompInfoData";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import {IServerResponse} from "../../../../common/common-models"

export interface IFactoryClubCompSetupControllerState {
  compId: number;
  isLoading: boolean;
  isReady: boolean;
  clubCompDefinitions: IClubCompDefinition[];
  categories: IClubCompCategory[];
}

export function factoryClubCompSetupController() {
  const state: IFactoryClubCompSetupControllerState = {
    compId: 0,
    isLoading: false,
    isReady: false,
    clubCompDefinitions: [],
    categories: [],
  };

  const clubCompInfoData = new ClubCompInfoData();

  const categoryDefault: IClubCompCategory = {
    id: 0,
    categoryName: "",
    maxEntries: 0,
    maxRelays: 0
  }

  function init(compId: number) {
    state.compId = compId;
    state.isLoading = true;
    Promise.all([getDefinitions(), getCategories()]).finally( () => {
      state.isLoading = false;
      state.isReady = true;
    })
  }

  function getDefinitions(): Promise<void> {
    const prom = clubCompInfoData.getDefinitions(state.compId);
    handleResponseMessages(prom);
    return prom.then((resp) => {
      if (resp.errNo === 0) {
        state.clubCompDefinitions = resp.data;
      }
    });
  }

  function getCategories(): Promise<void> {
    const prom = clubCompInfoData.getCategories(state.compId);
    handleResponseMessages(prom);
    return prom.then((resp) => {
      if (resp.errNo === 0) {
        state.categories = [categoryDefault, ...resp.data];
      }
    });
  }

  function onDefinitionChanged(clubCompDefinition: IClubCompDefinition) {
    const prom = clubCompInfoData.updateDefinition(clubCompDefinition);
    handleResponseMessages(prom);
    // return prom.then((resp) => {
    //   if (resp.errNo === 0) {
    //     state.categories = [categoryDefault, ...resp.data];
    //   }
    // });
  }

  function submitCategory(clubCompCategory: IClubCompCategory) {
    const prom = clubCompInfoData.updateCatregory(clubCompCategory, state.compId);
    handleResponseMessages(prom);
    return prom.then( (resp: IServerResponse<unknown>) => {
      if (resp.errNo === 0) {
        return getDefinitions();
      }
      return;
    })
  }

  return {
    state,
    init,
    getDefinitions,
    getCategories,
    onDefinitionChanged,
    submitCategory
  };
}

export function useClubCompSetupController() {
  const controller = factoryClubCompSetupController();
  const state = reactive(controller.state);

  return {
    state,
    controller,
  };
}
