<template>
  <FormGenericFieldGridV2
    style="margin: 0"
    :style="isDirty ? 'background-color: pink;' : ''"
  >
    <template slot="content">
      <div
        class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
      >
        <div v-text="clubCompDefinitionInternal.id"></div>
        <div v-text="clubCompDefinitionInternal.club.name"></div>
        <AdminIdV2 :obj="clubCompDefinitionInternal.club" />
      </div>

      <div
        class="
          e4s-flex-row
          e4s-gap--standard
          e4s-justify-flex-row-vert-center e4s-justify-flex-end
        "
      >
        <FieldTextV2
          style="width: 200px"
          :value="clubCompDefinitionInternal.bibNos"
          v-on:input="onBibNosChanged"
        />
        <FieldSelectV2
          style="width: 200px"
          :data-array="clubCompCategories"
          :value="getCategoryValueForDropDown"
          v-on:input="onCategorySelected"
        >
          <template slot-scope="{ obj }">
            {{ obj.categoryName }}
          </template>
        </FieldSelectV2>

        <div v-text="getCategoryValueForDropDown.maxEntries"></div>
        <div v-text="getCategoryValueForDropDown.maxRelays"></div>
      </div>
    </template>
  </FormGenericFieldGridV2>

</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
} from "@vue/composition-api";
import {
  IClubCompCategory,
  IClubCompDefinition,
} from "./club-comp-setup-models";
import { isEqual, simpleClone } from "../../../../common/common-service-utils";
import AdminIdV2 from "../../../../common/ui/layoutV2/admin-id-v2.vue";
import FieldTextV2 from "../../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FieldSelectV2 from "../../../../common/ui/layoutV2/fields/field-select-v2.vue";
import FormGenericFieldGridV2 from "../../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";

export default defineComponent({
  name: "ClubCompSetupDefinition",
  components: { FormGenericFieldGridV2, FieldSelectV2, FieldTextV2, AdminIdV2 },
  props: {
    clubCompDefinition: {
      type: Object as PropType<IClubCompDefinition>,
      required: true,
    },
    clubCompCategories: {
      type: Array as PropType<IClubCompCategory[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(
    props: {
      clubCompDefinition: IClubCompDefinition;
      clubCompCategories: IClubCompCategory[];
    },
    context: SetupContext
  ) {
    const clubCompDefinitionInternal = ref(
      simpleClone(props.clubCompDefinition)
    );

    const isDirty = ref(false);

    const getCategoryValueForDropDown = computed(() => {
      return props.clubCompCategories.find((category) => {
        return category.id === clubCompDefinitionInternal.value.category.id;
      });
    });

    function onCategorySelected(clubCompCategory: IClubCompCategory) {
      clubCompDefinitionInternal.value.category = {
        id: clubCompCategory.id,
        name: clubCompCategory.categoryName,
      };
      setIsDirty();
    }

    function onBibNosChanged(bibNos: string) {
      clubCompDefinitionInternal.value.bibNos = bibNos;
      setIsDirty();
    }

    function setIsDirty() {
      isDirty.value = !isEqual(
        props.clubCompDefinition,
        clubCompDefinitionInternal.value
      );


      context.emit("onChanged", simpleClone(clubCompDefinitionInternal.value));
    }

    return {
      clubCompDefinitionInternal,
      getCategoryValueForDropDown,
      onCategorySelected,
      onBibNosChanged,
      isDirty,
    };
  },
});
</script>
