<template>
  <div class="e4s-flex-column">
    <LoadingSpinnerV2 v-if="clubCompSetupController.state.isLoading" />

    <ClubCompCategorySetup
      :club-comp-categories="clubCompSetupController.state.categories"
      v-on:submitCategory="clubCompSetupController.controller.submitCategory"
    />

    <div class="e4s-vertical-spacer--large">
      <ClubCompSetupDefinition
        v-if="clubCompSetupController.state.isReady"
        :club-comp-definition="clubCompDefinition"
        v-for="clubCompDefinition in clubCompSetupController.state
          .clubCompDefinitions"
        :club-comp-categories="clubCompSetupController.state.categories"
        :key="clubCompDefinition.id"
        class="e4s-repeatable-grid--top"
        v-on:onChanged="clubCompSetupController.controller.onDefinitionChanged"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext, watch } from "@vue/composition-api";
import { useClubCompSetupController } from "./clubCompSetupController";
import ClubCompSetupDefinition from "./ClubCompSetupDefinition.vue";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import ClubCompCategorySetup from "./ClubCompCategorySetup.vue";

export default defineComponent({
  name: "ClubCompSetupWrapper",
  components: {
    ClubCompCategorySetup,
    LoadingSpinnerV2,
    ClubCompSetupDefinition,
  },
  props: {
    compId: {
      type: Number,
      default: 0,
    },
  },
  setup(props: { compId: number }, context: SetupContext) {
    const clubCompSetupController = useClubCompSetupController();

    if (props.compId > 0) {
      clubCompSetupController.controller.init(props.compId);
    }

    watch(
      () => props.compId,
      (newValue: number) => {
        if (newValue > 0) {
          clubCompSetupController.controller.init(props.compId);
        }
      },
      {
        immediate: true,
      }
    );

    return { clubCompSetupController };
  },
});
</script>
