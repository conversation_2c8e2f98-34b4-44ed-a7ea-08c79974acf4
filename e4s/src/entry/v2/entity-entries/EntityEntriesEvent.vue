<template>
  <tr :class="scheduleTableRowState.cssClass">
    <td>
      <div
        class="
          e4s-flex-row
          e4s-container--row-mobile-col
          entity-entries-event--time
          e4s-gap--standard
        "
      >
        <div v-text="getEventDate"></div>
        <div v-text="getEventTime"></div>
      </div>
    </td>

    <td>
      <span
        :class="hasAnyEntries ? 'entity-entries--has-entries-title' : ''"
        v-text="scheduleTableRowState.eventDescription"
      ></span>
      <div class="e4s-flex-row e4s-gap--standard">
        <div v-text="scheduleTableRowState.entriesDescription"></div>
        <!--        <a href="#" v-on:click.prevent="editBibs">Edit Bibs</a>-->
      </div>
    </td>
    <td>
      <div class="e4s-flex-column" style="align-items: end">
        <span v-text="scheduleTableRowState.countDescription"></span>
        <PrimaryLink
          v-if="showEditBibs"
          link-text="Edit Bibs"
          @onClick="editBibs"
        />
        <!--        <a v-if="showEditBibs" href="#" v-on:click.prevent="editBibs"-->
        <!--          >Edit Bibs</a-->
        <!--        >-->
      </div>
    </td>
  </tr>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { IClubCompInfo } from "../schools/clubCompInfo-models";
import { simpleClone } from "../../../common/common-service-utils";
import { IScheduleTableRowState } from "../schools/clubCompInfoService";
import * as EntityService from "./entity-service";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import { format, parse } from "date-fns";

export default defineComponent({
  name: "entity-entries-event",
  components: { PrimaryLink },
  props: {
    scheduleTableRowState: {
      type: Object as PropType<IScheduleTableRowState>,
      required: true,
    },
    clubCompInfo: {
      type: Object as PropType<IClubCompInfo>,
      required: true,
    },
  },
  setup(
    props: {
      scheduleTableRowState: IScheduleTableRowState;
      clubCompInfo: IClubCompInfo;
    },
    context: SetupContext
  ) {
    function editBibs() {
      context.emit("editBibs", simpleClone(props.scheduleTableRowState));
    }

    const showEditBibs = computed(() => {
      return (
        !props.scheduleTableRowState.isTeamEvent &&
        props.scheduleTableRowState.entries.length > 1
      );
    });

    const hasAnyEntries = computed(() => {
      return EntityService.hasAnyEntries(props.scheduleTableRowState);
    });

    const getEventDate = computed(() => {
      return format(
        parse(props.scheduleTableRowState.scheduleTableRow.startTime),
        "Do MMM"
      );
    });

    const getEventTime = computed(() => {
      return format(
        parse(props.scheduleTableRowState.scheduleTableRow.startTime),
        "HH:mm"
      );
    });

    return {
      showEditBibs,
      hasAnyEntries,

      getEventDate,
      getEventTime,
      editBibs,
    };
  },
});
</script>

<style>
.entity-entries--has-entries {
  background-color: var(--blue-50);
}

.entity-entries--has-entries-full {
  background-color: var(--blue-100);
}

.entity-entries--has-entries-title {
  font-weight: bold;
}
</style>
