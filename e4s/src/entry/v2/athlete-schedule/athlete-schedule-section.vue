<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <LoadingSpinnerV2 v-if="athleteScheduleController.state.isLoading" />

    <!--    <AthleteGridRowContentV2-->
    <!--      class="e4s-card e4s-card&#45;&#45;generic e4s-card&#45;&#45;well"-->
    <!--      :athlete="athleteScheduleController.state.athlete"-->
    <!--      class-header="e4s-header&#45;&#45;400"-->
    <!--      :show-athlete-profile-link="false"-->
    <!--    />-->

    <div class="e4s-flex-column">
      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <h2
          class="e4s-header--400"
          v-text="athleteGridRowController.state.athleteNameDescription"
        ></h2>

        <div class="e4s-flex-row">
          <!--          <button-generic-v2-->
          <!--            text="Back to athletes"-->
          <!--            class="e4s-flex-row&#45;&#45;end e4s-button&#45;&#45;auto"-->
          <!--            @click="close"-->
          <!--          />-->
          <!--Since using tabs, no need for back button.-->
          <PrimaryLink
            link-text="Back to My Athletes"
            @onClick="close"
            class="e4s-show-only-mobile"
          />
        </div>
      </div>

      <div class="e4s-flex-row e4s-justify-flex-space-between">
        <p
          class="e4s-subheader--general e4s-subheader--500"
          v-text="athleteGridRowController.state.clubName"
        ></p>

        <p
          class="e4s-subheader--general e4s-subheader--500"
          v-text="athleteGridRowController.state.athleteGenderAgeDescription"
        ></p>
      </div>
    </div>

    <InfoSectionV2
      info-type="error"
      v-if="
        athleteScheduleController.hasSecondaryClaimClub.value &&
        !athleteScheduleController.isClubInfoCompetition.value
      "
    >
      <template slot>
        <div
          class="e4s-flex-column e4s-gap--standard"
          v-if="athleteScheduleController.canChangeSecondaryClaim.value"
        >
          <div>
            You have a secondary claim club.
            <PrimaryLink
              link-text="Click here if required."
              @onClick="showSecondaryClaim.value = true"
            />
            your profile to change.
          </div>
          <div v-if="showSecondaryClaim.value">
            <div>
              You have a secondary claim club. Change here if required. You can
              only enter the competition as either your primary or secondary
              claim, you can not change when entering different events in this
              competition.
            </div>
            <AthleteSecondarySelectV2
              :athlete-summary="athleteScheduleController.state.athlete"
              v-on:onSelected="
                athleteScheduleController.onSelectedSecondaryClaim
              "
            />
          </div>
        </div>

        <div
          class="e4s-flex-column e4s-gap--standard"
          v-if="!athleteScheduleController.canChangeSecondaryClaim.value"
        >
          <div class="e4s-flex-row e4s-gap--standard">
            You are entered into this competition as:
            <span
              v-text="athleteScheduleController.getClubEnteredAs.value.name"
            ></span
            ><AdminIdV2
              :obj="athleteScheduleController.getClubEnteredAs.value"
            />
          </div>
        </div>
      </template>
    </InfoSectionV2>

    <!--Some events require a PB-->
    <InfoSectionV2
      info-type="error"
      v-if="
        athleteScheduleController.state.athleteCompSchedRuleEvents.length > 0 &&
        athleteScheduleController.anyEventsRequiringPb.value
      "
    >
      <template slot>
        <p>
          <span
            class="e4s-subheader--200 e4s-label--black"
            style="padding-right: var(--e4s-gap--standard)"
            >Estimated Performance:</span
          >
          Some events require an estimated performance "EP", this is required to
          ensure athletes get seeded into the correct events.
        </p>
      </template>
    </InfoSectionV2>
    <!--/Some events require a PB-->

    <!--    <hr class="dat-e4s-hr" />-->

    <div v-show="athleteScheduleController.state.ui.showSection === 'GRID'">
      <div class="e4s-flex-column e4s-gap--standard">
        <!--Unregistered athlete in a registered only comp-->
        <InfoSectionV2
          info-type="error"
          v-if="athleteScheduleController.isUnRegAthleteInRegOnlyComp.value"
        >
          <template slot>
            <div>Competition only open to Active Registered athletes.</div>
          </template>
        </InfoSectionV2>
        <!--/Unregistered athlete in a registered only comp-->

        <!--Athlete has expired registration-->
        <InfoSectionV2
          info-type="error"
          v-if="
            athleteScheduleController.hasAthleteRegExpiredForCompetition.value
          "
        >
          <template slot>
            <div
              v-html="athleteScheduleController.getExpiredRegMessage.value.body"
            ></div>
          </template>
        </InfoSectionV2>
        <!--/Athlete has expired registration-->

        <!--Valid athlete but no events-->
        <InfoSectionV2
          info-type="error"
          v-if="
            athleteScheduleController.state.athlete.id > 0 &&
            athleteScheduleController.state.athleteCompSchedRuleEvents
              .length === 0 &&
            !athleteScheduleController.hasAthleteRegExpiredForCompetition.value
          "
        >
          <template slot>
            <p>
              There are no individual events for
              <span
                v-text="
                  athleteScheduleController.state.athlete.firstName +
                  ' ' +
                  athleteScheduleController.state.athlete.surName
                "
              ></span>
              at this competition. Please contact the
              <!--              <PrimaryHref-->
              <!--                link-text="Competition organiser"-->
              <!--                :link="getShowEntryV2Route"-->
              <!--              />-->
              <PrimaryLink
                link-text="Competition organiser"
                @onClick="contactOrganiser"
              />
              if there is an issue with the available events.
            </p>
          </template>
        </InfoSectionV2>
        <!--/Valid athlete but no events-->

        <!--No athlete selected-->
        <InfoSectionV2
          info-type="error"
          v-if="athleteScheduleController.state.athlete.id === 0"
        >
          <p>Select an athlete to view their competition schedule.</p>
        </InfoSectionV2>
        <!--/No athlete selected-->
      </div>

      <!--      {{athleteScheduleController.state.competitionSummaryPublic.clubCompInfo.entryData}}-->

      <AthleteScheduleGridV2
        v-if="
          athleteScheduleController.state.athleteCompSchedRuleEvents.length >
            0 && !athleteScheduleController.isUnRegAthleteInRegOnlyComp.value
        "
        :athlete-schedule-state="athleteScheduleController.state"
        @onEventSelected="onEventSelected"
        @doOptions="doOptions"
        @showEntrants="showEntrants"
        @showPbEditV3="athleteScheduleController.showPbEditV3"
      />
    </div>

    <SubscriptionMessageV2
      id="waiting-list-section"
      v-if="athleteScheduleController.state.ui.showSection === 'WAITING_LIST'"
      :selected-competition="
        athleteScheduleController.state.competitionSummaryPublic
      "
      :comp-event="
        athleteScheduleController.state.selectedAthleteCompSchedRuleEvent
      "
      :selected-athlete="athleteScheduleController.state.athlete"
      :config-app="configController.getStore.value.configApp"
      v-on:continue="athleteScheduleController.waitingListContinue"
      v-on:cancel="athleteScheduleController.state.ui.showSection = 'GRID'"
    />

    <!--    v-if="athleteScheduleController.state.ui.modal === 'EDIT_PB_V3'"-->
    <!--    :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"-->
    <!--    -->
    <ModalV2
      :always-show-header-blank="true"
      :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
      v-if="athleteScheduleController.state.ui.modal === 'PB_EDIT_V3'"
    >
      <EditPbV3
        slot="body"
        style="margin: 8px"
        :edit-pb-v3-input-prop="
          athleteScheduleController.state.pbV3.editPbV3InputProp
        "
        :edit-pb-options="athleteScheduleController.state.pbV3.editPbV3Options"
        @cancel="athleteScheduleController.state.ui.modal = ''"
        @submitRoute="athleteScheduleController.setPbV3"
      />
    </ModalV2>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
  watch,
  ref,
} from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import { IAthlete, IAthleteSummary } from "../../../athlete/athlete-models";
import { useAthleteScheduleController } from "./useAthleteScheduleController";
import { simpleClone } from "../../../common/common-service-utils";
import CardGenericV2 from "../../../common/ui/layoutV2/card-generic-v2.vue";
import AthleteScheduleGridV2 from "./grid/AthleteScheduleGrid.vue";
import InfoSectionV2 from "../../../common/ui/layoutV2/info-section-v2.vue";
import {
  AthleteScheduleStateOptionsOutput,
  IEmitAthleteScheduleEventSelected,
} from "./athlete-schedule-models";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { useConfigController } from "../../../config/useConfigStore";
import { IAthleteCompSchedRuleEvent } from "../../../athleteCompSched/athletecompsched-models";
import SubscriptionMessageV2 from "../../../athleteCompSched/subscription/subscription-message-v2.vue";
import AthleteSecondarySelectV2 from "../../../athlete/v2/form/athlete-secondary-select-v2.vue";
import AdminIdV2 from "../../../common/ui/layoutV2/admin-id-v2.vue";
import AthleteGridRowContentV2 from "../../../athlete/v2/AthleteGridRowContentV2.vue";
import { useAthleteGridRowController } from "../../../athlete/v2/useAthleteGridRowController";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import EditPbV3 from "../../../athleteCompSched/pb/v3/EditPbV3.vue";
import ModalV2 from "../../../common/ui/layoutV2/modal/modal-v2.vue";
import { VUE_MQ_SIZES } from "../../../index";
import PrimaryHref from "../../../common/ui/layoutV2/href/PrimaryHref.vue";

import { LAUNCH_ROUTES_PATHS_V2_BASE } from "../../../launch/v2/launch-routes-v2";

export interface IAthleteScheduleSectionInput {
  competitionSummaryPublic: ICompetitionSummaryPublic;
  athlete: IAthlete;
  athleteSummary: IAthleteSummary;
}

export default defineComponent({
  name: "athlete-schedule-section",
  components: {
    PrimaryHref,
    ModalV2,
    EditPbV3,
    PrimaryLink,
    ButtonGenericV2,
    ButtonGenericBackV2,
    AthleteGridRowContentV2,
    AdminIdV2,
    AthleteSecondarySelectV2,
    SubscriptionMessageV2,
    LoadingSpinnerV2,
    InfoSectionV2,
    AthleteScheduleGridV2,
    CardGenericV2,
  },
  props: {
    athleteScheduleSectionInput: {
      type: Object as PropType<IAthleteScheduleSectionInput>,
      required: true,
    },
  },
  setup(
    props: {
      athleteScheduleSectionInput: IAthleteScheduleSectionInput;
    },
    context: SetupContext
  ) {
    const configController = useConfigController();
    const athleteScheduleController = useAthleteScheduleController();

    const athleteGridRowController = useAthleteGridRowController(
      props.athleteScheduleSectionInput.athleteSummary
    );

    const showSecondaryClaim = ref(false);

    athleteScheduleController.state.competitionSummaryPublic = simpleClone(
      props.athleteScheduleSectionInput.competitionSummaryPublic
    );
    athleteScheduleController.setAthleteAndGetSchedule(
      props.athleteScheduleSectionInput.athlete,
      props.athleteScheduleSectionInput.athleteSummary
    );

    watch(
      () => props.athleteScheduleSectionInput,
      (
        newValue: IAthleteScheduleSectionInput,
        oldValue: IAthleteScheduleSectionInput
      ) => {
        athleteScheduleController.setAthleteAndGetSchedule(
          newValue.athlete,
          newValue.athleteSummary
        );
        athleteGridRowController.init(newValue.athleteSummary);
      }
    );

    const hasBuilderPermissionForComp = computed(() => {
      return configController.hasBuilderPermissionForComp(
        props.athleteScheduleSectionInput.competitionSummaryPublic.compOrgId,
        props.athleteScheduleSectionInput.competitionSummaryPublic.compId
      );
    });

    const getState = computed(() => {
      return simpleClone(athleteScheduleController.state);
    });

    function doOptions(athleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent) {
      athleteScheduleController.state.selectedAthleteCompSchedRuleEvent =
        simpleClone(athleteCompSchedRuleEvent);

      const output: AthleteScheduleStateOptionsOutput = {
        selectedAthleteCompSchedRuleEvent:
          athleteScheduleController.state.selectedAthleteCompSchedRuleEvent,
        pbMap: athleteScheduleController.state.pbMap,
        athleteCompSchedRuleEvents:
          athleteScheduleController.state.athleteCompSchedRuleEvents,
        athleteCompSchedResponse:
          athleteScheduleController.state.athleteCompSchedResponse,
      };
      context.emit("doOptions", simpleClone(output));
    }

    function showEntrants(
      athleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent
    ) {
      context.emit("showEntrants", athleteCompSchedRuleEvent);
    }

    const getShowEntryV2Route = computed(() => {
      return (
        LAUNCH_ROUTES_PATHS_V2_BASE +
        "/" +
        props.athleteScheduleSectionInput.competitionSummaryPublic.compId
      );
    });

    function contactOrganiser() {
      context.emit(
        "contactOrganiser",
        props.athleteScheduleSectionInput.competitionSummaryPublic
      );
    }

    function onEventSelected(
      emitPayload: IEmitAthleteScheduleEventSelected,
      forceContinue?: boolean
    ) {
      console.log("athlete-schedule-section.onEventSelected", emitPayload);
      // this submits to server.
      athleteScheduleController
        .onEventSelected(emitPayload, forceContinue)
        .then(() => {
          console.warn(
            "athlete-schedule-section.onEventSelected emit",
            emitPayload
          );
          context.emit("onEventSelected", emitPayload);
        });
    }

    function close() {
      console.log("athlete-schedule-section.close()");
      context.emit("close");
    }

    return {
      athleteGridRowController,
      athleteScheduleController,
      configController,
      hasBuilderPermissionForComp,
      getState,
      showSecondaryClaim,
      getShowEntryV2Route,

      onEventSelected,
      contactOrganiser,
      doOptions,
      showEntrants,
      close,
      VUE_MQ_SIZES,
    };
  },
});
</script>
