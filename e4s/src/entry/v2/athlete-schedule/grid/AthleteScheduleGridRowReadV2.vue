<template>
  <CardGenericV2 class="e4s-flex-column e4s-gap--standard">
    <template slot="all">
      <!--Event details-->
      <div class="e4s-flex-row e4s-full-width e4s-gap--standard">
        <div class="e4s-flex-row e4s-full-width e4s-justify-flex-space-between">
          <div class="e4s-flex-row e4s-gap--small">
            <div class="e4s-flex-row e4s-gap--small e4s-subheader--200">
              <div v-text="getCompEventDescription"></div>
              <AdminIdV2
                prop-name="ceid"
                :obj="athleteCompSchedRuleEvent"
                v-if="isAdmin"
              />
            </div>
          </div>

          <div class="e4s-flex-row e4s-gap--standard">
            <div class="e4s-subheader--200" v-text="startTime"></div>
          </div>
        </div>
      </div>
      <!--/Event details-->

      <div
        class="e4s-flex-row e4s-full-width"
        v-if="athleteCompSchedRuleEvent.ceoptions.helpText.length > 0"
      >
        <span v-text="athleteCompSchedRuleEvent.ceoptions.helpText"></span>
      </div>

      <div
        class="
          e4s-flex-row
          e4s-full-width
          e4s-justify-flex-space-between
          e4s-flex-end
        "
      >
        <div class="e4s-flex-column e4s-gap--standard e4s-align-self-flex-end">
          <div class="e4s-flex-column" v-if="requiresPb">
            <div
              class="
                e4s-flex-row
                e4s-gap--small
                e4s-subheader--200
                e4s-label--black
              "
            >
              <span>Estimated Performance: </span>
              <div v-text="humanReadablePb"></div>
            </div>
          </div>

          <div
            v-if="athleteCompSchedRuleEvent.ruleMessage.length > 0"
            v-text="athleteCompSchedRuleEvent.ruleMessage"
            class="e4s-subheader--200"
            :class="getInfoSectionType"
          ></div>
        </div>

        <div class="e4s-flex-row e4s-align-self-flex-end e4s-gap--standard">
          <slot name="buttons"> </slot>
        </div>
      </div>
    </template>
  </CardGenericV2>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  onUpdated,
  computed,
  watch,
  ref,
} from "@vue/composition-api";
import { IAthleteCompSchedRuleEvent } from "../../../../athleteCompSched/athletecompsched-models";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue";
import { IEmitAthleteScheduleEventSelected } from "../athlete-schedule-models";
import { AthleteCompSchedService } from "../../../../athleteCompSched/athletecompsched-service";
import FieldCheckboxV2 from "../../../../common/ui/layoutV2/fields/field-checkbox-v2.vue";

import * as AthleteScheduleService from "../athlete-schedule-service";
import { CONFIG } from "../../../../common/config";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";
import InfoSectionV2 from "../../../../common/ui/layoutV2/info-section-v2.vue";
import { EditTimePbInputV2 } from "../../../../athleteCompSched/pb/v2/edit-time-pb-models-v2";
import { IAthletePb } from "../../../../athlete/athlete-models";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { IPbSubmitState } from "../../../../athlete/athlete-data";
import AdminIdV2 from "../../../../common/ui/layoutV2/admin-id-v2.vue";
import { IEntriesOptionsAthleteScheduleInput } from "../entries-options-athlete-schedule-v2.vue";

export default defineComponent({
  name: "athlete-schedule-grid-row-read-v2",
  components: {
    AdminIdV2,
    InfoSectionV2,
    FieldCheckboxV2,
    CardGenericV2,
    ButtonGenericV2,
  },
  props: {
    athleteCompSchedRuleEvent: {
      type: Object as PropType<IAthleteCompSchedRuleEvent>,
      required: true,
    },
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    athleteCompSchedService: {
      type: Object as PropType<AthleteCompSchedService>,
      required: true,
    },
    entriesOptionsAthleteScheduleInput: {
      type: Object as PropType<IEntriesOptionsAthleteScheduleInput>,
      required: true,
    },
    hasBuilderPermission: {
      type: Boolean,
      required: false,
    },
    isAdmin: {
      type: Boolean,
      required: false,
    },
  },
  setup(
    props: {
      athleteCompSchedRuleEvent: IAthleteCompSchedRuleEvent;
      competitionSummaryPublic: ICompetitionSummaryPublic;
      athleteCompSchedService: AthleteCompSchedService;
      entriesOptionsAthleteScheduleInput: IEntriesOptionsAthleteScheduleInput;
      isCheckBoxDisabled: boolean;
      hasBuilderPermission: boolean;
      isAdmin: boolean;
    },
    context: SetupContext
  ) {
    // const configController = useConfigController();

    const checkboxValue = ref(false);
    checkboxValue.value = AthleteScheduleService.hasUserSelectedEvent(
      props.athleteCompSchedRuleEvent
    );

    const pbEntered = ref(-1);
    const pbStuff: IPbSubmitState = {
      aid: 0,
      eid: 0,
      pb: 0,
      pbText: "",
      trackSb: false,
    };
    const pbSubmitStateRef = ref(pbStuff);

    initPb();

    watch(
      () => props.athleteCompSchedRuleEvent,
      (
        newValue: IAthleteCompSchedRuleEvent,
        oldValue: IAthleteCompSchedRuleEvent
      ) => {
        checkboxValue.value =
          AthleteScheduleService.hasUserSelectedEvent(newValue);
        // initPb();
      }
    );

    watch(
      () => props.entriesOptionsAthleteScheduleInput,
      (
        newValue: IEntriesOptionsAthleteScheduleInput,
        oldValue: IEntriesOptionsAthleteScheduleInput
      ) => {
        initPb();
      }
    );

    onUpdated(() => {
      console.log(
        "athlete-schedule-grid-row-v2 " +
          props.athleteCompSchedRuleEvent.eventGroup +
          "...updated! This is here because: you want to keep these to a minimum."
      );
    });

    function onEventSelected() {
      if (props.athleteCompSchedRuleEvent.ceoptions.mandatoryPB) {
        if (pbEntered.value === -1) {
          // PB box is now launched if required.
          messageDispatchHelper(
            props.athleteCompSchedRuleEvent.maxgroup +
              ": An estimated time is required for seeding"
          );
          checkboxValue.value = true;
          // checkboxValue.value = false;
          window.setTimeout(() => {
            checkboxValue.value = false;
          }, 0);
          return;
        }
      }

      const emitPayload: IEmitAthleteScheduleEventSelected = {
        isSelected: checkboxValue.value,
        athleteCompSchedRuleEvent: props.athleteCompSchedRuleEvent,
        pb: pbEntered.value,
        pbSubmitState: pbSubmitStateRef.value,
      };
      context.emit("onEventSelected", emitPayload);
    }

    const startTime = computed(() => {
      return (
        "Starts: " +
        AthleteScheduleService.getStartDate(
          props.athleteCompSchedRuleEvent,
          props.competitionSummaryPublic
        )
      );
    });

    const getCompEventDescription = computed(() => {
      return props.athleteCompSchedService.getCompEventName(
        props.athleteCompSchedRuleEvent
      );
    });

    function onCheckBoxChange(isSelected: boolean) {
      checkboxValue.value = isSelected;
      onEventSelected();
    }

    const hasUserSelectedEvent = computed(() => {
      return AthleteScheduleService.hasUserSelectedEvent(
        props.athleteCompSchedRuleEvent
      );
    });

    const hasAdminOrBuilderPermission = computed(() => {
      return props.isAdmin || props.hasBuilderPermission;
    });

    function initPb() {
      const athleteCurrentPb: IAthletePb | null =
        props.entriesOptionsAthleteScheduleInput.pbMap[
          props.athleteCompSchedRuleEvent.eventid
        ];
      if (athleteCurrentPb && athleteCurrentPb.pb! > 0) {
        pbEntered.value = athleteCurrentPb.pb!;
      }
    }

    const editTimePbInputV2 = computed(() => {
      const editTimePbInputV2: EditTimePbInputV2 = {
        athlete: props.entriesOptionsAthleteScheduleInput.athlete,
        athleteCompSched: props.athleteCompSchedRuleEvent,
      };
      return editTimePbInputV2;
    });

    const isInCart = computed(() => {
      return (
        props.athleteCompSchedRuleEvent.order.productId > 0 &&
        props.athleteCompSchedRuleEvent.paid === 0
      );
    });

    const requiresPb = computed(() => {
      if (props.athleteCompSchedRuleEvent.maxathletes === -1) {
        return false;
      }
      return props.athleteCompSchedRuleEvent.ceoptions.mandatoryPB;
    });

    const getInfoSectionType = computed<"e4s-info-text--error" | "">(() => {
      return props.athleteCompSchedRuleEvent.paid === 1
        ? ""
        : "e4s-info-text--error";
    });

    const getCompEventMessage = computed(() => {
      if (props.isCheckBoxDisabled) {
        return props.athleteCompSchedRuleEvent.ruleMessage;
      }
      return "";
    });

    const humanReadablePb = computed(() => {
      return props.athleteCompSchedRuleEvent.perfInfo.perfText;
    });

    return {
      onEventSelected,
      startTime,
      getCompEventDescription,
      getCompEventMessage,
      checkboxValue,
      onCheckBoxChange,
      hasUserSelectedEvent,
      CONFIG,
      hasAdminOrBuilderPermission,
      editTimePbInputV2,
      isInCart,
      requiresPb,
      humanReadablePb,
      getInfoSectionType,
    };
  },
});
</script>

<!--<style>-->
<!--.athlete-schedule-grid-row&#45;&#45;in-cart {-->
<!--  background: var(&#45;&#45;yellow-50);-->
<!--  border-color: var(&#45;&#45;yellow-400);-->
<!--}-->

<!--.athlete-schedule-grid-row&#45;&#45;paid {-->
<!--  background: var(&#45;&#45;green-50);-->
<!--  border-color: var(&#45;&#45;green-400);-->
<!--}-->
<!--</style>-->
