import { IAthleteSectionUi } from "./athlete/useAthleteSectionController";
import { ICompetitionSummaryPublic } from "../../competition/competition-models";
import { IAthleteSummary } from "../../athlete/athlete-models";
import {
  CurrentCompSelection,
  ISectionLink,
} from "../../common/ui/layoutV2/tabs/section-links-models";
import { IDoOptionsAthleteSchedule } from "./athlete-schedule/athlete-schedule-models";

export type EntryStateSection =
  | "ENTRY_CONDITIONS"
  | "COMPETITION"
  | "CREATE_ATHLETE"
  | "COMPETITION_MORE_INFO"
  | "CONTACT_ORGANISER"
  | "ENTRY_OPTIONS_ATHLETE_SCHEDULE"
  | "VIEW_ATHLETE"
  | "YOUR_ENTRIES";

export type EntryStateCompSection =
  | "INDIVIDUAL"
  | "ATHLETES_GRID"
  | "EVENTS"
  | "OPTIONS"
  | "TEAMS"
  | "SHOP"
  | "VIEW_ATHLETE"
  | "YOUR_ENTRIES";

export type EntryCompSectionLink = ISectionLink<EntryStateCompSection>;

export interface IEntryState {
  isLoading: boolean;
  ui: {
    showSection: EntryStateSection;
    sectionsHistory: EntryStateSection[];
    showGlobalMask: boolean;
    // showEntryConditions: boolean;
    // showCompetition: boolean;
    // showCreatAthlete: boolean;
    athlete: IAthleteSectionUi;
    compSectionLinkMap: Record<EntryStateCompSection, EntryCompSectionLink>;
    compSectionLinks: EntryCompSectionLink[];
    compSectionLinkSelected: EntryCompSectionLink;
    currentCompSelection: CurrentCompSelection;
    debugEnabled: boolean;
  };
  competitionSummaryPublic: ICompetitionSummaryPublic;
  selectedAthlete: IAthleteSummary;
  doOptionsAthleteSchedule: IDoOptionsAthleteSchedule | null;
}
