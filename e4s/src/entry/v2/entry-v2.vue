<template>
  <div class="e4s-content-wrapper">
    <LoadingSpinnerV2 v-if="entry.state.ui.showGlobalMask" />

    <!--    v-if="entry.state.ui.debugEnabled"-->
    <div
      class="e4s-flex-column e4s-gap--standard"
      v-if="entry.state.ui.debugEnabled"
    >
      <div>entry.state.ui.showSection: {{ entry.state.ui.showSection }}</div>
      <div>
        entry.state.ui.currentCompSelection:
        {{ entry.state.ui.currentCompSelection }}
      </div>
      <div>
        entry.state.ui.compSectionLinkSelected.uniqueDesc:
        {{ entry.state.ui.compSectionLinkSelected.uniqueDesc }}
      </div>
      <button v-on:click="entry.init(entry.state.competitionSummaryPublic)">
        test
      </button>
    </div>

    <!--ENTRY_CONDITIONS-->
    <EntryConditionsV2
      v-if="entry.state.ui.showSection === 'ENTRY_CONDITIONS'"
      :competition-summary-public="entry.state.competitionSummaryPublic"
      v-on:proceedToComp="entry.entryConditionsProceed()"
      v-on:cancel="entry.entryConditionsCancel()"
    />
    <!--/ENTRY_CONDITIONS-->

    <div
      class="e4s-flex-column e4s-full-width"
      v-if="entry.canShowMainSection.value"
    >
      <!--Home-->
      <div class="e4s-flex-row">
        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end-x">
          <BreadCrumbsEntry
            :comp-id="entry.state.competitionSummaryPublic.compId"
          />
        </div>
      </div>
      <!--/Home-->

      <div class="e4s-vertical-spacer--standard"></div>

      <!--      <div class="e4s-card e4s-card&#45;&#45;generic">-->
      <!--        <CompetitionHeaderSimple-->
      <!--          :competition-summary-public="entry.state.competitionSummaryPublic"-->
      <!--        />-->
      <!--      </div>-->

      <CompHeaderSimpleV2
        :competition-summary-public="entry.state.competitionSummaryPublic"
      >
        <ButtonGenericV2
          class="e4s-flex-row--end"
          slot="button-more-info"
          text="More Info"
          @click="entry.competitionSummaryPublicController.goCompHome()"
        />
      </CompHeaderSimpleV2>

      <!--      <PublicCompCardV2-->
      <!--        :competition-summary-public="entry.state.competitionSummaryPublic"-->
      <!--      >-->
      <!--        <ButtonGenericV2-->
      <!--          class="e4s-flex-row&#45;&#45;end"-->
      <!--          slot="button-more-info"-->
      <!--          text="More Info"-->
      <!--          @click="entry.competitionSummaryPublicController.goCompHome()"-->
      <!--        />-->
      <!--      </PublicCompCardV2>-->

      <div class="e4s-vertical-spacer--standard"></div>

      <div
        class="e4s-flex-column e4s-gap--standard"
        v-if="entry.shouldShowIndivTeamsTabs.value"
      >
        <!--        <div class="e4s-flex-row e4s-gap&#45;&#45;standard">-->
        <!--          <ButtonGenericV2-->
        <!--            class="e4s-button&#45;&#45;150"-->
        <!--            text="Athlete Events"-->
        <!--            :disabled="-->
        <!--              entry.state.ui.compSectionLinkSelected.uniqueDesc === 'INDIVIDUAL'-->
        <!--            "-->
        <!--            @click="-->
        <!--              entry.onLinkSelected(entry.state.ui.compSectionLinkMap.INDIVIDUAL)-->
        <!--            "-->
        <!--          />-->

        <!--          <ButtonGenericV2-->
        <!--            class="e4s-button&#45;&#45;150"-->
        <!--            text="Team Events"-->
        <!--            :disabled="-->
        <!--     sdsds         entry.state.ui.compSectionLinkSelected.uniqueDesc === 'TEAMS'-->
        <!--            "-->
        <!--            @click="-->
        <!--              entry.onLinkSelected(entry.state.ui.compSectionLinkMap.TEAMS)-->
        <!--            "-->
        <!--          />-->
        <!--        </div>-->

        <!--Indiv Team Tabs-->
        <div v-if="entry.shouldShowIndivTeamsTabs.value">
          <div
            class="e4s-flex-row e4s-tab-links--section-links-bottom"
            id="qqz---tabs-top"
          >
            <SectionLink
              class="e4s-header--400"
              :section-link="entry.state.ui.compSectionLinkMap.INDIVIDUAL"
              :is-active="entry.isIndividualTabActive.value"
              v-on:selected="entry.onLinkSelected"
            />

            <SectionLink
              class="e4s-header--400"
              :section-link="entry.state.ui.compSectionLinkMap.TEAMS"
              :is-active="
                entry.state.ui.compSectionLinkSelected.uniqueDesc ===
                entry.state.ui.compSectionLinkMap.TEAMS.uniqueDesc
              "
              v-on:selected="entry.onLinkSelected"
            />

            <SectionLink
              class="e4s-header--400"
              :section-link="entry.state.ui.compSectionLinkMap.YOUR_ENTRIES"
              :is-active="
                entry.state.ui.compSectionLinkSelected.uniqueDesc ===
                entry.state.ui.compSectionLinkMap.YOUR_ENTRIES.uniqueDesc
              "
              v-on:selected="entry.onLinkSelected"
            />
          </div>
          <div class="e4s-vertical-spacer--standard"></div>
        </div>
        <!--/Indiv Team Tabs-->

        <div class="e4s-vertical-spacer--standard"></div>
      </div>

      <!--wraps all indiv team-->
      <div
        class="e4s-card-x e4s-card--generic-x"
        id="entry-v2--athlete-section"
      >
        <div
          class="e4s-flex-column e4s-gap--standard"
          id="entry-v2--athlete-section--a"
          v-show="entry.state.ui.showSection === 'COMPETITION'"
        >
          <AthleteSection
            v-show="entry.isIndividualTabActive.value"
            :competition-summary-public="entry.state.competitionSummaryPublic"
            :entry-comp-section-link="entry.state.ui.compSectionLinkSelected"
            :athlete-id="athleteId"
            @onAthleteSelected="entry.onAthleteSelected"
            @onCreateAthlete="entry.setShowSection('CREATE_ATHLETE')"
            @doOptions="entry.doOptions"
            @closeOptions="entry.closeOptions"
            @viewAthlete="entry.viewAthlete"
            @contactOrganiser="entry.setShowSection('CONTACT_ORGANISER')"
          />
          <!--          @athleteScheduleClosed="entry.reloadCompetition()"-->
          <!--          @onEventSelected="entry.reloadCompetition()"-->

          <EventTeamsPanelV2
            v-if="entry.state.ui.compSectionLinkSelected.uniqueDesc === 'TEAMS'"
            :competition="entry.state.competitionSummaryPublic"
            v-on:contactOrganiser="entry.setShowSection('CONTACT_ORGANISER')"
          />

          <!--          style="padding: 8px"-->
          <EntityEntriesLoader
            v-if="
              entry.state.ui.compSectionLinkSelected.uniqueDesc ===
              'YOUR_ENTRIES'
            "
            :comp-id="entry.state.competitionSummaryPublic.compId"
            :club-comp-info="clubCompInfo"
          />
        </div>
      </div>
      <!--/wraps all indiv team-->
    </div>

    <!--    <div>-->
    <!--      <div class="e4s-vertical-spacer&#45;&#45;standard"></div>-->
    <!--      <div>-->
    <!--        ==== EVERYTHING BELOW THIS LINE IS BEING REMOVED wHEN TESTING OVER. ====-->
    <!--      </div>-->
    <!--      <div class="e4s-vertical-spacer&#45;&#45;standard"></div>-->
    <!--    </div>-->

    <!--    ////////////////////////////////////////-->
    <!--    ////////////////////////////////////////-->
    <!--    ////////////////////////////////////////    -->
    <!--    ////////////////////////////////////////-->

    <!--e4s-gap standard-y-->
    <div
      class="e4s-flex-column e4s-full-width"
      v-if="entry.state.ui.showSection !== 'ENTRY_CONDITIONS'"
    >
      <!--CONTACT_ORGANISER-->
      <div v-if="entry.state.ui.showSection === 'CONTACT_ORGANISER'">
        <div class="e4s-vertical-spacer--large"></div>
        <AskOrganiserFormV2
          class="e4s-card--half-screen e4s-card--well"
          :competition-summary-public="entry.state.competitionSummaryPublic"
          v-on:cancel="entry.showCompAgain"
        />
      </div>
      <!--/CONTACT_ORGANISER-->

      <!--CREATE_ATHLETE-->
      <div v-if="entry.state.ui.showSection === 'CREATE_ATHLETE'">
        <div class="e4s-vertical-spacer--large"></div>
        <AthleteCreateFormV2
          v-if="entry.state.ui.showSection === 'CREATE_ATHLETE'"
          :show-cancel-button="true"
          :competition-base="entry.state.competitionSummaryPublic"
          v-on:cancelled="entry.setShowLastSection"
          v-on:submitted="entry.setShowLastSection"
        />
      </div>
      <!--/CREATE_ATHLETE-->

      <!--VIEW_ATHLETE-->
      <div v-if="entry.state.ui.showSection === 'VIEW_ATHLETE'">
        <div class="e4s-vertical-spacer--large"></div>
        <LoadingSpinnerV2 v-if="entry.athleteState.isLoading.value" />
        <AthleteFormV2
          :athlete="entry.athleteState.athlete.value"
          :default-ao="configController.getStore.value.configApp.defaultao"
          :aos="configController.getStore.value.configApp.aos"
          :is-admin="configController.isAdmin.value"
          v-on:onCancel="entry.setShowLastSection"
        />
      </div>
      <!--/VIEW_ATHLETE-->
    </div>

    <!--    <EntriesOptionsAthleteScheduleV2-->
    <!--      v-if="entry.state.ui.showSection === 'ENTRY_OPTIONS_ATHLETE_SCHEDULE'"-->
    <!--      :do-options-athlete-schedule="entry.state.doOptionsAthleteSchedule"-->
    <!--      v-on:cancel="entry.setShowSection('COMPETITION')"-->
    <!--    />-->
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
  computed,
} from "@vue/composition-api";
import { useEntry } from "./useEntry";
import { ICompetitionSummaryPublic } from "../../competition/competition-models";
import EntryConditionsV2 from "../../public/entry-public/entry-conditions-v2.vue";
import AthleteSection from "./athlete/AthleteSection.vue";
import FormGenericSectionTitleV2 from "../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import CompHeaderLogoCountsV2 from "../../public/entry-public/public-list/v2/comp-header-logo-counts-v2.vue";
import CompHeaderNameLocationV2 from "../../public/entry-public/public-list/v2/comp-header-name-location-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import AskOrganiserFormV2 from "../../competition/askorganiser/ask-organiser-form-v2.vue";
import AthleteCreateFormV2 from "../../athlete/v2/form/athlete-create-form-v2.vue";
import AthleteSearchV2 from "../../athlete/v2/search/athlete-search-v2.vue";
import CardGenericV2 from "../../common/ui/layoutV2/card-generic-v2.vue";
import SectionLinks from "../../common/ui/layoutV2/tabs/section-links.vue";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
import EventTeamsPanelV2 from "../../athleteCompSched/comp-event-teams/v2/event-teams-panel-v2.vue";
import ButtonGenericBackV2 from "../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import LoggingDisplay from "../../logging/LoggingDisplay.vue";
import SectionLink from "../../common/ui/layoutV2/tabs/section-link.vue";
import SectionLinksWrapper from "../../common/ui/layoutV2/tabs/section-links-wrapper.vue";
import SectionLinksComp from "../../common/ui/layoutV2/tabs/section-links-comp.vue";
import AthleteFormV2 from "../../athlete/v2/form/athlete-form-v2.vue";
import CompetitionHeaderSimple from "../../competition/v2/CompetitionHeaderSimple.vue";
import BreadCrumb from "../../common/ui/breadcrumb/BreadCrumb.vue";
import BreadCrumbFromConfig from "../../common/ui/breadcrumb/BreadCrumbFromConfig.vue";
import BreadCrumbsEntry from "../../common/ui/breadcrumb/BreadCrumbsEntry.vue";
import PublicCompCardV2 from "../../public/entry-public/public-list/v2/public-list-comp-card-v2.vue";
import CompHeaderSimpleV2 from "../../public/entry-public/public-list/v2/CompHeaderSimpleV2.vue";
import { useConfigController } from "../../config/useConfigStore";
import { useEntryStore } from "../entry-store";
import EntityEntriesLoader from "./entity-entries/EntityEntriesLoader.vue";

export default defineComponent({
  name: "entry-v2",
  components: {
    EntityEntriesLoader,
    CompHeaderSimpleV2,
    PublicCompCardV2,
    BreadCrumbsEntry,
    BreadCrumbFromConfig,
    BreadCrumb,
    CompetitionHeaderSimple,
    AthleteFormV2,
    SectionLinksComp,
    SectionLinksWrapper,
    SectionLink,
    LoggingDisplay,
    ButtonGenericBackV2,
    EventTeamsPanelV2,
    LoadingSpinnerV2,
    SectionLinks,
    CardGenericV2,
    AthleteSearchV2,
    AthleteCreateFormV2,
    AskOrganiserFormV2,
    ButtonGenericV2,
    CompHeaderNameLocationV2,
    CompHeaderLogoCountsV2,
    FormGenericSectionTitleV2,
    AthleteSection,
    EntryConditionsV2,
  },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    athleteId: {
      type: Number,
      default: 0,
    },
  },
  setup(
    props: {
      competitionSummaryPublic: ICompetitionSummaryPublic;
      athleteId: number;
    },
    context: SetupContext
  ) {
    const entryStore = useEntryStore();
    const clubCompInfo = computed(() => {
      return entryStore.entryForm.clubCompInfo;
    });

    const entry = useEntry();

    const configController = useConfigController();

    entry.init(props.competitionSummaryPublic);

    watch(
      () => props.competitionSummaryPublic,
      (
        newValue: ICompetitionSummaryPublic,
        oldValue: ICompetitionSummaryPublic
      ) => {
        entry.init(newValue);
      }
    );

    return {
      configController,
      entry,
      entryStore,
      clubCompInfo,
    };
  },
});
</script>
