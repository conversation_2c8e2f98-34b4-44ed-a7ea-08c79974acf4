import {
  IAthleteSectionState,
  IAthleteSectionUi,
} from "./useAthleteSectionController";
import { CompetitionService } from "../../../competition/competiton-service";
import * as AthleteServiceUtils from "../../../athlete/athlete-service-utils";
import { AthleteCompSchedService } from "../../../athleteCompSched/athletecompsched-service";
import { DEFAULT_PAGE_SIZE } from "../../../config/config-store";
import { IAthleteSearch } from "../../../athlete/athlete-models";
import { simpleClone } from "../../../common/common-service-utils";
import * as ClubCompInfoService from "../schools/clubCompInfoService";

const competitionService = new CompetitionService();

export function factoryAthleteSectionState(): IAthleteSectionState {
  return {
    competitionSummaryPublic: competitionService.factorySummaryPublic(),
    isLoading: false,
    selectedAthleteSummary: AthleteServiceUtils.factoryAthleteSummary(),
    selectedAthlete: AthleteServiceUtils.factoryGetAthlete(),
    athleteScheduleStateOptionsOutput: null,
    isLoadingAthlete: false,
    athletes: [],
    athletesToDisplay: [],
    ui: {
      showSection: "ATHLETES",
      showSearch: false,
      showCreate: true,
      showPaging: false,
      showNoAthletesMessage: false,
      showQuickSearch: true,
      showClubCompInfo: false,
      filterKeysEnabled: {},
      isFilterEnabled: false,
    },
    page: {
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE,
      totalCount: 0,
    },
    ageGroupInfo: {},
    athleteSearch: AthleteServiceUtils.factoryAthleteSearch(),
    athleteEntries: [],
    athleteCompSchedRuleEvent:
      new AthleteCompSchedService().factoryAthleteCompSchedRuleEvent(),
  };
}

export function getFilterKeysEnabled(
  athleteSearch: IAthleteSearch,
  includeQuickSearch: boolean = true
): Partial<Record<keyof IAthleteSearch, unknown>> {
  const keySearch: (keyof IAthleteSearch)[] = [
    "search",
    "firstname",
    "surname",
    "urn",
    "gender",
    "club",
    "county",
    "ageGroupId",
  ];
  return keySearch.reduce<Partial<Record<keyof IAthleteSearch, unknown>>>(
    (accum, propName) => {
      if (athleteSearch[propName].toString().length > 0) {
        accum[propName] = null;
      }
      return accum;
    },
    {}
  );
}
export function calculateUiState(
  state: IAthleteSectionState
): IAthleteSectionUi {
  const athleteSectionUiInternal = simpleClone(state.ui);

  athleteSectionUiInternal.filterKeysEnabled = getFilterKeysEnabled(
    state.athleteSearch
  );
  athleteSectionUiInternal.isFilterEnabled =
    Object.keys(athleteSectionUiInternal.filterKeysEnabled).length > 0;

  athleteSectionUiInternal.showQuickSearch =
    !athleteSectionUiInternal.showSearch;

  //  Only if no athletes and no searches being run.
  athleteSectionUiInternal.showNoAthletesMessage =
    state.athletes.length === 0 && !athleteSectionUiInternal.isFilterEnabled;

  athleteSectionUiInternal.showPaging =
    state.page.totalCount > state.page.pageSize;

  athleteSectionUiInternal.showClubCompInfo =
    ClubCompInfoService.hasClubCompInfoCompetition(
      state.competitionSummaryPublic.clubCompInfo
    );

  return athleteSectionUiInternal;
}
