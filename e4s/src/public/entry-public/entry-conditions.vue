<template>
    <div class="row">

        <div class="col s12 m12 l12" v-if="competitionSummaryPublic.compId > 0">
            <div v-text="getCompName" class="e4s-1-5-em"></div>
        </div>

        <div class="col s12 m12 l12">
<!--            <div v-if="isLoading">-->
<!--                Loading...-->
<!--                <LoadingSpinner></LoadingSpinner>-->
<!--            </div>-->

            <TermsConditions
                v-if="getShowTerms"
                :competition-summary-public="competitionSummaryPublic"
            >
                <div slot="header"></div>
                <div slot="buttons"></div>
            </TermsConditions>

            <Priority
                v-if="getShowPriority"
                :competition-summary-public="competitionSummaryPublic"
                v-on:cancel="cancel"
                v-on:submit="proceedToComp"
            >
                <div slot="header"></div>
            </Priority>

            <PriorityV2 :competition-summary-public="competitionSummaryPublic" v-on:cancel="cancel" v-on:submit="proceedToComp"/>
        </div>

        <div
            class="col s12 m12 l12"
            v-if="!getShowPriority"
        >
            <div class="right">

                <LoadingSpinner v-if="isLoading"></LoadingSpinner>

                <button v-on:click.stop="cancel"
                        class="btn waves-effect waves red">
                    <span>Cancel</span>
                </button>

                <button class="btn waves-effect waves green"
                        v-on:click.stop="proceedToComp">
                    <span>Submit</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import {CompetitionService} from "../../competition/competiton-service"
import {ICompetitionSummaryPublic} from "../../competition/competition-models"
import {CompetitionData} from "../../competition/competition-data"
import {handleResponseMessages} from "../../common/handle-http-reponse"
import TermsConditions from "./terms-conditions/terms-conditions.vue"
import Priority from "./priority/priority.vue"
import {isBefore, parse} from "date-fns"
// import {RawLocation} from "vue-router"
import {LAUNCH_ROUTES_PATHS} from "../../launch/launch-routes"
import PriorityV2 from "./priority/priority-v2.vue";

const competitionService = new CompetitionService();

@Component({
    name: "entry-conditions",
    components: {PriorityV2, Priority, TermsConditions}
})
export default class EntryConditions extends Vue {
    public isLoading = false;
    public compId: number = 0;

    public competitionService: CompetitionService = new CompetitionService()
    public competitionSummaryPublic: ICompetitionSummaryPublic = this.competitionService.factorySummaryPublic();

    public created() {
        const id: number = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);

        if (id > 0) {
            this.compId = id;
            this.isLoading = true;
            const prom = new CompetitionData().getCompById(this.compId);
            handleResponseMessages(prom);
            prom
                .then((resp) => {
                    if (resp.errNo === 0) {
                        this.competitionSummaryPublic = resp.data;
                    }
                })
                .finally(() => {
                    this.isLoading = false
                })
        }
    }

    public get getCompName() {
        return competitionService.getCompetitionTitle(this.competitionSummaryPublic);
    }

    public get getShowTerms(): boolean {
        return this.competitionSummaryPublic.termsConditions.length > 0;
    }

    public get getShowPriority() {

        const priorityDate = this.competitionSummaryPublic.options.priority &&
          this.competitionSummaryPublic.options.priority.dateTime &&
          this.competitionSummaryPublic.options.priority.dateTime.length > 0 ?
          this.competitionSummaryPublic.options.priority.dateTime : "";

        //  Comp is priority only for whole duration.
        if (this.competitionSummaryPublic.options.priority.required && priorityDate.length === 0) {
            return true;
        }

        if (priorityDate.length > 0) {
            return isBefore(new Date(), parse(this.competitionSummaryPublic.options.priority.dateTime));
        }

        return false;
    }

    public cancel() {
        this.$router.push({
            path: "/" + LAUNCH_ROUTES_PATHS.SHOW_ENTRIES
        });
    }

    public proceedToComp() {
        //
        // let location: RawLocation;
        // location = ;

        this.$router.push({
            path: "/entry",
            query: {
                comporgid: this.competitionSummaryPublic.compOrgId.toString(),
                compid: this.competitionSummaryPublic.compId.toString()
            }
        });
    }

}
</script>
