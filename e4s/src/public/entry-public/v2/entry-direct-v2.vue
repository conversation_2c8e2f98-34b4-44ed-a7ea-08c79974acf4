<template>
  <div class="e4s-content-wrapper">
    <LoadingSpinnerV2 v-if="isLoading" />
    <CompMoreInfoV2
      :competition-summary-public="competitionSummaryPublic"
      v-if="competitionSummaryPublic.compId > 0 && showSection === 'MORE_INFO'"
      :show-comp-results-buttons="true"
      :show-bottom-button-bar="false"
      v-on:showContactOrganiser="setShowSection('CONTACT_ORGANISER')"
      @onOrgApproved="onOrgApproved"
    />

    <div
      v-if="showSection === 'CONTACT_ORGANISER'"
      class="e4s-flex-column e4s-gap--standard"
    >
      <PublicCompCardV2 :competition-summary-public="competitionSummaryPublic">
        <ButtonGotoCompV2
          slot="button-more-info"
          class="e4s-flex-row--end"
          :competition-summary-public="competitionSummaryPublic"
          :config-version="configController.getVersion.value"
        />
      </PublicCompCardV2>

      <div class="e4s-card e4s-card--generic">
        <AskOrganiserFormV2
          :competition-summary-public="competitionSummaryPublic"
          v-on:cancel="setShowSection('MORE_INFO')"
        >
          <PrimaryLink
            slot="top-right-back-button"
            link-text="Back"
            @onClick="setShowSection('MORE_INFO')"
          />
        </AskOrganiserFormV2>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, SetupContext } from "@vue/composition-api";
import { CompetitionService } from "../../../competition/competiton-service";
import { CompetitionData } from "../../../competition/competition-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { useRoute } from "../../../router/migrateRouterVue3";
import CompMoreInfoV2 from "../public-list/v2/moreinfo/comp-more-info-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import AskOrganiserFormV2 from "../../../competition/askorganiser/ask-organiser-form-v2.vue";
import PublicCompCardV2 from "../public-list/v2/public-list-comp-card-v2.vue";
import ButtonGotoCompV2 from "../../../common/ui/layoutV2/buttons/button-goto-comp-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import { useConfigController } from "../../../config/useConfigStore";

const competitionService = new CompetitionService();

type EntryDirectSectionType = "CONTACT_ORGANISER" | "MORE_INFO";

export default defineComponent({
  name: "entry-direct-v2",
  components: {
    PrimaryLink,
    ButtonGotoCompV2,
    PublicCompCardV2,
    AskOrganiserFormV2,
    LoadingSpinnerV2,
    CompMoreInfoV2,
  },
  setup(props: {}, context: SetupContext) {
    const isLoading = ref(false);
    const route = useRoute();
    const competitionSummaryPublic = ref(
      competitionService.factorySummaryPublic()
    );

    const configController = useConfigController();

    const showSection = ref<EntryDirectSectionType>("MORE_INFO");

    const compId = isNaN(Number(route.params.id))
      ? 0
      : parseInt(route.params.id, 0);

    loadComp();

    function loadComp() {
      if (compId > 0) {
        isLoading.value = true;
        const prom = new CompetitionData().getCompById(compId);
        handleResponseMessages(prom);
        prom
          .then((resp) => {
            if (resp.errNo === 0) {
              const comp = resp.data;

              competitionSummaryPublic.value = comp;
            }
          })
          .finally(() => {
            isLoading.value = false;
          });
      }
    }

    function setShowSection(entryDirectSectionType: EntryDirectSectionType) {
      showSection.value = entryDirectSectionType;
    }

    function onOrgApproved() {
      console.log("EntryDirectV1.onOrgApproved");
      loadComp();
    }

    return {
      isLoading,
      showSection,
      competitionSummaryPublic,
      setShowSection,
      configController,
      onOrgApproved,
    };
  },
});
</script>

<style></style>
