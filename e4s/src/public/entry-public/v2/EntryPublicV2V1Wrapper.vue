<template>
  <div style="padding: 8px">
    <EntryPublicV2
      :default-to-section="state.showSummarySection"
      @goToCompV1="goToComp"
      v-show="state.showSection === 'SUMMARY'"
    />

    <div
      v-if="
        state.showSection === 'ENTRY_CONDITIONS' ||
        state.showSection === 'TERMS_CONDITIONS' ||
        state.showSection === 'PRIORITY'
      "
      class="
        e4s-content-max-center e4s-width-controller-x e4s-hybrid-content-wrapper
      "
      id="entry-public-v2-v1-wrapper--entry-conditions-v2"
    >
      <EntryConditionsV2
        :competition-summary-public="state.proceedToCompetitionSummaryPublic"
        v-on:proceedToComp="
          proceedToComp(state.proceedToCompetitionSummaryPublic)
        "
        v-on:cancel="termsAndConditionsCancelled()"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, SetupContext } from "@vue/composition-api";
import EntryPublicV2 from "./entry-public-v2.vue";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import { isBefore, parse } from "date-fns";
import { RawLocation } from "vue-router";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";
import { simpleClone } from "../../../common/common-service-utils";
import { useAuthStoreController } from "../../../auth/useAuthStore";
import { useRouter } from "../../../router/migrateRouterVue3";
import TermsConditionsV2 from "../terms-conditions/terms-conditions-v2.vue";
import PriorityV2 from "../priority/priority-v2.vue";
import { EntryPublicSection } from "./entry-public-models";
import EntryConditionsV2 from "../entry-conditions-v2.vue";

interface IEntryPublicV2V1WrapperState {
  proceedToCompetitionSummaryPublic: ICompetitionSummaryPublic;
  showSection:
    | "SHOP_ONLY"
    | "PRIORITY"
    | "TERMS_CONDITIONS"
    | "ENTRY_CONDITIONS"
    | "SUMMARY"
    | "";
  showSummarySection: EntryPublicSection | "";
}

export default defineComponent({
  name: "EntryPublicV2V1Wrapper",
  components: {
    EntryConditionsV2,
    PriorityV2,
    TermsConditionsV2,
    EntryPublicV2,
  },
  props: {},
  setup(props: any, context: SetupContext) {
    const state = reactive<IEntryPublicV2V1WrapperState>({
      proceedToCompetitionSummaryPublic: {} as ICompetitionSummaryPublic,
      showSection: "SUMMARY",
      showSummarySection: "",
    });

    const authStoreController = useAuthStoreController();
    const routerInternal = useRouter();

    function goToComp(comp: ICompetitionSummaryPublic) {
      console.log("EntryPublicV2V1Wrapper.goToCompV1");
      state.proceedToCompetitionSummaryPublic = simpleClone(comp);

      const priorityDate =
        comp.options.priority &&
        comp.options.priority.dateTime &&
        comp.options.priority.dateTime.length > 0
          ? comp.options.priority.dateTime
          : "";

      const priorityAlwaysRequired =
        priorityDate.length === 0 && comp.options.priority.required;

      if (comp.options.priority.required) {
        if (
          isBefore(new Date(), parse(comp.options.priority.dateTime)) ||
          priorityAlwaysRequired
        ) {
          let location: RawLocation;
          if (!authStoreController.isLoggedIn.value) {
            location = {
              path: LAUNCH_ROUTES_PATHS.LOGIN_V1,
              query: {
                redirectFrom:
                  "/" +
                  LAUNCH_ROUTES_PATHS.ENTRY_CONDITIONS +
                  "/" +
                  comp.compId,
              },
            };
          } else {
            location = {
              path:
                "/" + LAUNCH_ROUTES_PATHS.ENTRY_CONDITIONS + "/" + comp.compId,
            };
          }
          routerInternal.push(location);
          return;
        }
      }

      if (comp.termsConditions.length > 0) {
        state.showSection = "TERMS_CONDITIONS";
        return;
      }

      proceedToComp(comp);
    }

    function termsAndConditionsCancelled() {
      state.showSection = "SUMMARY";
      state.showSummarySection = "MORE_INFO";
    }

    function priorityCodeResult(isOk: boolean) {
      if (!isOk) {
        state.showSection = "SUMMARY";
        return;
      }
      if (
        state.proceedToCompetitionSummaryPublic.termsConditions &&
        state.proceedToCompetitionSummaryPublic.termsConditions.length > 0
      ) {
        state.showSection = "TERMS_CONDITIONS";
        return;
      }
      proceedToComp(state.proceedToCompetitionSummaryPublic);
    }

    function handleTermsConditionsResult(isOk: boolean) {
      if (!isOk) {
        state.showSection = "SUMMARY";
        return;
      }
      proceedToComp(state.proceedToCompetitionSummaryPublic);
    }

    function proceedToComp(comp: ICompetitionSummaryPublic) {
      if (comp.options.ui.entryDefaultPanel === "SHOP_ONLY") {
        routerInternal.push({
          path: "/shop/" + comp.compId,
        });
        return;
      }

      let location: RawLocation;
      if (!authStoreController.isLoggedIn.value) {
        location = {
          path: LAUNCH_ROUTES_PATHS.LOGIN_V1,
          query: {
            redirectFrom:
              "/entry?comporgid=" + comp.compOrgId + "&compid=" + comp.compId,
          },
        };
      } else {
        location = {
          path: LAUNCH_ROUTES_PATHS.ENTRY, //"/entry"
          query: {
            comporgid: comp.compOrgId.toString(),
            compid: comp.compId.toString(),
          },
        };
      }

      routerInternal.push(location);
    }

    return {
      goToComp,
      state,
      priorityCodeResult,
      handleTermsConditionsResult,
      termsAndConditionsCancelled,
      proceedToComp,
    };
  },
});
</script>

<style scoped></style>
