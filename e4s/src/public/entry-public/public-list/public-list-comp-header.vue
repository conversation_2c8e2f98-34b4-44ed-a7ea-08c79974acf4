<template>
  <div class="list-card">
    <div class="list-card--row list-card--title">
      <span class="list-card--comp">
        <span v-text="getCompName"></span>
        &nbsp;(<span v-text="getCompDatesText"></span>)
      </span>
      <div class="list-card--top-right">
        <a
          class="list-card--more-info"
          href="#"
          v-on:click.prevent="showMoreInfo"
          >More Info</a
        >
      </div>
    </div>
    <div
      class="list-card--row list-card--location"
      v-text="getLocationText"
    ></div>
    <div class="list-card--third">
      <a :href="getAllEntriesUrl" v-if="getShowAthleteCount" target="_blank">
        Athletes: <span v-text="comp.entries.athletes"></span>
      </a>
      &nbsp;&nbsp;
      <a :href="getAllEntriesUrl" v-if="getShowIndivCount" target="_blank">
        Entries: <span v-text="comp.entries.indiv"></span>
      </a>
      &nbsp;&nbsp;
      <a :href="getAllEntriesUrl" v-if="getShowTeamCount" target="_blank">
        Teams: <span v-text="comp.entries.team"></span>
      </a>

      <div class="list-card--bottom-right">
        <img class="list-card--logo" :src="getLogo" />
      </div>
    </div>

    <div class="list-card--restricted" v-if="getShowCompRestrictedMessage">
      <div class="e4s-section-padding-separator"></div>
      <CompRestricted
        :athlete-security="comp.options.athleteSecurity"
      ></CompRestricted>
    </div>

    <div v-if="comp.options.homeInfo && comp.options.homeInfo.length > 0">
      <div class="e4s-section-padding-separator"></div>
      <div v-text="comp.options.homeInfo"></div>
    </div>

    <div class="list-card--footer">
      <div class="row">
        <div class="col s12" v-if="$mq === VUE_MQ_SIZES.MOBILE.name">
          <button
            class="
              btn
              waves-effect waves
              green
              public-comp-list--enter-button
              col
              s12
            "
            v-if="comp.options.resultsAvailable"
            v-on:click="goToResults"
          >
            <i class="material-icons normal">format_list_numbered</i>
            Results
          </button>

          <button
            :style="getCanEnter ? '' : 'visibility: hidden;'"
            class="
              btn
              waves-effect waves
              green
              public-comp-list--enter-button
              col
              s12
            "
            v-on:click="goToComp"
          >
            <span v-text="getEnterButtonText"></span>
          </button>

          <button
            class="
              btn
              waves-effect waves
              green
              public-comp-list--enter-button
              col
              s12
            "
            v-if="hasLinkingTicketComp"
            v-on:click="goToLinkingTicketComp"
          >
            <span v-text="comp.options.ui.ticketCompButtonText"></span>
          </button>

          <div class="col s12">
            <span
              class="open-close-message right"
              v-html="getEntriesOpenCloseMessage"
            ></span>
          </div>
        </div>

        <div class="col s12 m12 l12" v-if="$mq !== VUE_MQ_SIZES.MOBILE.name">
          <button
            class="btn waves-effect waves green public-comp-list--enter-button"
            v-if="comp.options.resultsAvailable"
            v-on:click="goToResults"
          >
            <i class="material-icons normal">format_list_numbered</i>
            Results
          </button>

          <button
            :style="getCanEnter ? '' : 'visibility: hidden;'"
            class="btn waves-effect waves green public-comp-list--enter-button"
            v-on:click="goToComp"
          >
            <span v-text="getEnterButtonText"></span>
          </button>

          <button
            class="btn waves-effect waves green public-comp-list--enter-button"
            v-if="hasLinkingTicketComp"
            v-on:click="goToLinkingTicketComp"
          >
            <span v-text="comp.options.ui.ticketCompButtonText"></span>
          </button>

          <span
            class="open-close-message right"
            v-html="getEntriesOpenCloseMessage"
          ></span>
        </div>
      </div>

      <div class="row" v-if="getShowNewsFlash">
        <div class="col s12 m12 l12">
          <div class="news-flash">
            <a href="#" v-on:click.prevent="doShowNewsFlash"
              >Important News, please click here for details</a
            >
          </div>
        </div>
      </div>

      <div v-if="isAdmin" style="margin-top: 5px">
        <!--        <div class="row">-->
        <!--          <div class="col s12 m12 l12">-->
        <!--            <div class="e4s-section-divider-line&#45;&#45;simple-grey"></div>-->
        <!--            Admin Section-->
        <!--            <a-->
        <!--              href="#"-->
        <!--              v-on:click.prevent="adminSectionExpand = !adminSectionExpand"-->
        <!--            >-->
        <!--              <span v-text="adminSectionExpand ? 'Hide' : 'Show'"></span>-->
        <!--            </a>-->
        <!--          </div>-->
        <!--        </div>-->

        <div class="e4s-section-divider-line--simple-grey"></div>

        <div class="e4s-flex-row e4s-justify-flex-space-between">
          <div class="e4s-flex-row e4s-gap--standard">
            <span>Admin Section</span>
            <a
              href="#"
              v-on:click.prevent="adminSectionExpand = !adminSectionExpand"
            >
              <span v-text="adminSectionExpand ? 'Hide' : 'Show'"></span>
            </a>
          </div>

          <div
            class="e4s-flex-row e4s-gap--standard"
            v-if="isPastCompDate && isAdmin"
          >
            <span>Status</span>
            <a
              href="#"
              v-on:click.prevent="showStatus"
              class="e4s-justify-flex-end"
            >
              <span
                v-text="comp.status.description"
                :class="getStatusClass"
              ></span>
            </a>
          </div>
        </div>

        <div v-if="adminSectionExpand">
          <div class="row">
            <div class="col s12 m12 l12">
              <BuilderContactOrganisers
                :org-name="comp.club"
                :organisers="comp.organisers"
              />
            </div>
          </div>

          <div class="e4s-flex-row"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import { format, isBefore, parse } from "date-fns";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { AUTH_STORE_CONST, IAuthStoreState } from "../../../auth/auth-store";
import { ICompSummary } from "../../../competition/competition-models";
import { IConfigApp, USER_ROLES } from "../../../config/config-app-models";
import { EntryPublicService } from "../entry-public-service";
import { CompetitionService } from "../../../competition/competiton-service";
import { CONFIG } from "../../../common/config";
import CompRestricted from "../../../competition/restricted/comp-restricted.vue";
import { RawLocation } from "vue-router";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";
import { VUE_MQ_SIZES } from "../../../index";

@Component({
  name: "public-comp-card-header",
  components: {
    CompRestricted,
    BuilderContactOrganisers: () => {
      return import(
        "../../../builder/form/builder-contact/builder-contact-organisers.vue"
      );
    },
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, {
      isLoggedIn: (state: IAuthStoreState) => state.isLoggedIn,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class PublicListCompHeader extends Vue {
  public configApp: IConfigApp;
  public isLoggedIn: boolean;

  @Prop({
    required: true,
  })
  public comp: ICompSummary;

  public entryPublicService: EntryPublicService = new EntryPublicService();
  public competitionService = new CompetitionService();

  public userRoles = USER_ROLES;

  public adminSectionExpand = false;

  public $mq: any;
  public VUE_MQ_SIZES = VUE_MQ_SIZES;

  public get getFirstCompDate() {
    return format(parse(this.comp.dates[0]), "Do MMM YYYY");
  }

  public get getCompDatesText() {
    if (this.comp.dates && this.comp.dates.length > 1) {
      return (
        format(parse(this.comp.dates[0]), "Do MMM YYYY") +
        " - " +
        format(
          parse(this.comp.dates[this.comp.dates.length - 1]),
          "Do MMM YYYY"
        )
      );
    }

    return format(parse(this.comp.dates[0]), "Do MMM YYYY");
  }

  public get getCompName() {
    return this.competitionService.getCompetitionTitle(this.comp);
  }

  public get getEntriesOpenCloseMessage() {
    return this.competitionService.getPublicCardOpenCloseMessage(this.comp);
  }

  public get getCanEnter(): boolean {
    return this.competitionService.canUserEnter(this.comp);
  }

  public get isPastLateEntry() {
    return this.competitionService.isPastLateEntry(this.comp);
  }

  public get getLocationText() {
    return (
      this.comp.location.name +
      ": " +
      ["address1", "address2", "town", "county", "postcode"]
        .reduce((accum, prop) => {
          //  @ts-ignore
          if (this.comp.location[prop]) {
            //  @ts-ignore
            accum.push(this.comp.location[prop]);
          }
          return accum;
        }, [])
        .join(", ")
    );
  }

  public get getLogo() {
    return CONFIG.E4S_HOST + "/" + this.comp.logo;
  }

  public get isLive() {
    return this.competitionService.isLive(this.comp);
  }

  // public get getReportLastChecked() {
  //
  //   const reportLastChecked = this.comp.reportLastChecked;
  //   return R.isNil(reportLastChecked) || R.isEmpty(reportLastChecked)
  //     ? ""
  //     : " (" + format(parse(reportLastChecked), "Do MMM YY HH:mm:ss") + ")";
  // }

  // public get getHasReportNotRun() {
  //   if (!this.getCanEnter) {
  //     return false;
  //   }
  //   const hasNotRun = this.competitionService.hasReportNotRun(this.comp);
  //   return this.configApp.role === this.userRoles.E4SUSER && hasNotRun;
  // }

  public get getAllEntriesUrl() {
    return (
      CONFIG.E4S_HOST +
      "/entry/v5/competition/showEntriesV2.php?compid=" +
      this.comp.compId
    );
  }

  public get getResultsShortCutUrl() {
    return CONFIG.E4S_HOST + "/" + this.comp.compId + "/results";
  }

  public goToResults() {
    let location: RawLocation;
    location = {
      path:
        "/" + LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC + "/" + this.comp.compId,
    };
    this.$router.push(location);
    // window.location.href = this.getResultsUrl;
  }

  public get getShowTeamCount() {
    return !R.isNil(this.comp.entries.teamEventCount);
  }

  public get getShowIndivCount() {
    return !R.isNil(this.comp.entries.indiv);
  }

  public get getShowAthleteCount() {
    return !R.isNil(this.comp.entries.athletes);
  }

  public get getEnterButtonText() {
    return this.competitionService.getEnterButtonText(this.comp.options);
  }

  public get getShowNewsFlash() {
    return false;
    // return this.comp.newsFlash && this.comp.newsFlash.length > 0;
  }

  public doShowNewsFlash() {
    this.$emit("doShowNewsFlash", this.comp);
  }

  public goToComp() {
    this.$emit("enterComp", this.comp);
  }

  public showMoreInfo() {
    this.$emit("showMoreInfo", this.comp);
  }

  public get getShowCompRestrictedMessage(): boolean {
    if (
      this.comp.options.athleteSecurity.clubs &&
      this.comp.options.athleteSecurity.clubs.length > 0
    ) {
      if (
        this.comp.options.athleteSecurity.onlyClubsUpTo &&
        this.comp.options.athleteSecurity.onlyClubsUpTo.length > 0
      ) {
        const onlyClubsUpTo = parse(
          this.comp.options.athleteSecurity.onlyClubsUpTo
        );
        return isBefore(new Date(), onlyClubsUpTo);
      }
      //  No date set, so comp completely locked down to these clubs.
      return true;
    }
    return false;
  }

  public get hasLinkingTicketComp(): boolean {
    return !!(
      this.comp.options.ui.ticketComp && this.comp.options.ui.ticketComp > 0
    );
  }

  public get getLinkingTicketCompUrl(): string {
    return CONFIG.E4S_HOST + "/" + this.comp.options.ui.ticketComp;
  }

  public goToLinkingTicketComp() {
    window.location.href = this.getLinkingTicketCompUrl;
  }

  public get getStatusClass() {
    return this.entryPublicService.getStatusClass(this.comp);
  }

  public showStatus() {
    this.$emit("onShowStatus", R.clone(this.comp));
  }

  public get isPastCompDate() {
    return this.competitionService.isPastCompDate(this.comp);
  }
}
</script>

<style scoped>
.list-card--row {
  padding-bottom: 5px;
}
.list-card--top-left {
  display: inline-flex;
}
.list-card--date {
}
.open-close-message {
  margin-left: 0.5em;
}
.e4s-card--competition__logo-image {
  height: 3em;
  margin-top: -2em;
  /*float: right;*/
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.list-card--title {
  font-size: 1.1em;
  color: #1c2024;
}
.list-card--comp {
  /*text-align: right;*/
  display: inline-block;
  max-width: 75%;
}
.list-card--top-right {
  float: right;
  text-align: right;
  display: inline-block;
  width: 25%;
  max-width: 100px;
}
.list-card--location {
  color: grey;
  padding-bottom: 20px;
}
.list-card--third {
}

.list-card--restricted {
}

.list-card--more-info {
}
.list-card--bottom-right {
  float: right;
}
.list-card--enter {
  color: white;
  background-color: green;
  border: 1px solid grey;
  border-radius: 2px;
  padding: 2px 15px 2px 15px;
}
.list-card--badge {
  color: #30373f;
  background-color: #dbf2ff;
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 0.8em;
}
.list-card--footer {
  padding-top: 0.5em;
}

.list-card--logo {
  height: 3em;
}

.news-flash {
  text-align: center;
  padding: 1em 0 0 0;
  font-weight: 600;
}
</style>
