<template>
  <div class="e4s-flex-column">
    <FormGenericSectionHeader value="Competition Dates" />

    <div class="e4s-show-only-mobile">
      <div class="e4s-flex-row e4s-flex-wrap">
        <div class="e4s-flex-column e4s-50-percent-width">
          <FormGenericSpanTextV2
            :form-label="'Competition Date' + (isMultiDay ? 's' : '')"
            :value="getCompDates"
          />
        </div>
        <div
          class="e4s-flex-column e4s-50-percent-width"
          v-if="!isPastOpenDate"
        >
          <FormGenericSpanTextV2
            form-label="Entries Open"
            :value="getEntryOpenDate"
          />
        </div>
        <div class="e4s-flex-column e4s-50-percent-width">
          <FormGenericSpanTextV2
            form-label="Entries Close"
            :value="getEntryCloseDate"
          />
        </div>
        <div
          class="e4s-flex-column e4s-50-percent-width"
          v-if="getLateEntryDate.length > 0 && !isPastLateEntryDate"
        >
          <FormGenericSpanTextV2
            form-label="Price Increase From"
            :value="getLateEntryDate"
          />
        </div>
        <div
          class="e4s-flex-column e4s-50-percent-width"
          v-if="getCheckinDateTime.length > 0"
        >
          <FormGenericSpanTextV2
            form-label="Check-In Available From"
            :value="getCheckinDateTime"
          />
        </div>
      </div>
    </div>

    <FormGenericFieldGridV2 style="margin-bottom: 0" class="e4s-hide-mobile">
      <template slot="content">
        <FormGenericSpanTextV2
          :form-label="'Competition Date' + (isMultiDay ? 's' : '')"
          :value="getCompDates"
        />

        <FormGenericSpanTextV2
          v-if="!isPastOpenDate"
          form-label="Entries Open"
          :value="getEntryOpenDate"
        />

        <FormGenericSpanTextV2
          form-label="Entries Close"
          :value="getEntryCloseDate"
        />

        <FormGenericSpanTextV2
          v-if="getLateEntryDate.length > 0 && !isPastLateEntryDate"
          form-label="Price Increase From"
          :value="getLateEntryDate"
        />

        <FormGenericSpanTextV2
          v-if="getCheckinDateTime.length > 0"
          form-label="Check-In Available From"
          :value="getCheckinDateTime"
        />
      </template>
    </FormGenericFieldGridV2>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../../../competition/competition-models";
import {
  eventDateDisplay,
  getE4sStandardHumanDateTimeOutPut,
} from "../../../../../common/common-service-utils";
import { isAfter, parse } from "date-fns";
import FormGenericFieldGridV2 from "../../../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericSectionHeader from "../../../../../common/ui/layoutV2/form/form-generic-section-header.vue";
import FormGenericSpanTextV2 from "../../../../../common/ui/layoutV2/form/form-generic--span-text-v2.vue";
import * as CompetitonServiceV2 from "../../../../../competition/v2/competiton-service-v2";

export default defineComponent({
  name: "CompSectionDates",
  components: {
    FormGenericSpanTextV2,
    FormGenericSectionHeader,
    FormGenericFieldGridV2,
  },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
  },
  setup(
    props: { competitionSummaryPublic: ICompetitionSummaryPublic },
    context: SetupContext
  ) {
    // const competitionService = new CompetitionService();

    const getFirstCompDate = computed(() => {
      // return format(
      //   parse(props.competitionSummaryPublic.options.dates[0]),
      //   "Do MMM YYYY"
      // );
      return eventDateDisplay(props.competitionSummaryPublic.options.dates[0]);
    });

    const getCompDates = computed<string>(() => {
      return CompetitonServiceV2.getCompDatesListForDisplay(
        props.competitionSummaryPublic
      );
    });

    const isMultiDay = computed(() => {
      return CompetitonServiceV2.isMultiDay(props.competitionSummaryPublic);
    });

    const isPastOpenDate = computed(() => {
      return isAfter(
        new Date(),
        parse(props.competitionSummaryPublic.opendate)
      );
    });

    const getEntryOpenDate = computed(() => {
      return getE4sStandardHumanDateTimeOutPut(
        props.competitionSummaryPublic.opendate,
        false
      );
    });

    const getLateEntryDate = computed(() => {
      if (CompetitonServiceV2.hasSaleEndDate(props.competitionSummaryPublic)) {
        return CompetitonServiceV2.getSaleEndDateTime(
          props.competitionSummaryPublic
        );
      }
      return "";
    });

    const isPastLateEntryDate = computed(() => {
      return CompetitonServiceV2.isPastLateEntry(
        props.competitionSummaryPublic
      );
    });

    const getEntryCloseDate = computed(() => {
      return getE4sStandardHumanDateTimeOutPut(
        props.competitionSummaryPublic.closedate,
        false
      );
    });

    const getCheckinDateTime = computed(() => {
      const checkInDateTimeOpens =
        props.competitionSummaryPublic.options.checkIn.checkInDateTimeOpens;
      if (checkInDateTimeOpens.length === 0) {
        return "";
      }
      return getE4sStandardHumanDateTimeOutPut(checkInDateTimeOpens, false);
    });

    const isNearToEntryClose = computed(() => {
      return CompetitonServiceV2.isNearToCloseDate(
        props.competitionSummaryPublic
      );
    });

    return {
      getCompDates,
      getFirstCompDate,
      isPastOpenDate,
      isPastLateEntryDate,
      getEntryOpenDate,
      getLateEntryDate,
      getEntryCloseDate,
      getCheckinDateTime,
      isNearToEntryClose,
      isMultiDay,
    };
  },
});
</script>
