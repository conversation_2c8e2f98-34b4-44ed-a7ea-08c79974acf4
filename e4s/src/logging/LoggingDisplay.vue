<template>
  <div class="e4s-flex-column">
    <div>Logging Display: <span v-text="logging.getLoggingEvents.value.length"></span></div>
    <div
      v-for="message in logging.getLoggingEvents.value"
      :key="message.id"
      v-text="message.message"
    ></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import { useLogging } from "./useLogging";

export default defineComponent({
  name: "logging-display",
  components: {},
  props: {},
  setup(props: any, context: SetupContext) {
    const logging = useLogging();

    return { logging };
  },
});
</script>
