<template>
  <div class="no-print">
    <div
      class="r4s-nav"
      :class="'e4s-secondary-color-bg-' + configApp.theme + getClass"
      :style="
        rs4ScoreboardOutput.options.ui.body.all +
        rs4ScoreboardOutput.options.ui.header.body
      "
    >
      <div
        class="r4s-title"
        :style="rs4ScoreboardOutput.options.ui.header.title"
      >
        <div v-text="eventName"></div>
      </div>
      <div
        class="r4s-right-content"
        :style="rs4ScoreboardOutput.options.ui.header.rightContent"
      >
        <slot name="r4s-right-content"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import { Prop } from "vue-property-decorator";
import { IRs4ScoreboardOutput } from "./rs4-scoreboard-models";
import { ScoreboardOutputService } from "../scoreboard-output/scoreboard-output-service";

const scoreboardOutputService = new ScoreboardOutputService();

@Component({
  name: "r4s-route-header",
  components: {},
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class R4sRouteHeader extends Vue {
  public readonly configApp: IConfigApp;

  @Prop({
    default: () => {
      return scoreboardOutputService.factoryScoreboardOutput();
    },
  })
  public readonly rs4ScoreboardOutput: IRs4ScoreboardOutput;

  @Prop({
    default: "",
  })
  public readonly compName!: string;

  @Prop({
    default: "",
  })
  public readonly eventName!: string;

  public get getClass() {
    return " r4s-route-header--scoreboard";
  }

  public get getTextClass() {
    return " r4s-route-header--scoreboard-text";
  }
}
</script>

<style scoped>
.r4s-nav {
  height: 10vh;
  /*background-color: purple;*/
  line-height: 10vh;
  color: white;
  font-size: 3vh;
}

.r4s-logo-a {
  float: left;
}

.r4s-logo {
  height: 8vh;
}

.r4s-title {
  display: inline-block;
  margin-left: 1vw;
  font-size: 9vh;
}

.r4s-comp-event {
  font-size: 10vh;
  height: 5vh;
  position: fixed;
  margin-top: -1vh;
}

.r4s-comp-name {
  height: 5vh;
  position: fixed;
  margin-top: 2vh;
  font-size: 2vh;
  color: #464242;
  font-weight: 500;
}

.r4s-right-content {
  float: right;
  margin-right: 2vw;
}

.r4s-route-header--scoreboard {
  background-color: black !important;
}

.r4s-route-header--scoreboard-text {
  color: white !important;
}
</style>
