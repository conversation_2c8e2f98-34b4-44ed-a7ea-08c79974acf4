<template>
  <video muted style="width: 100%" :id="random6Char">
    <source :src="videoLinkName + '.avi'" type="video/avi" />
    <source :src="videoLinkName + '.mp4'" type="video/mp4" />
    Your browser does not support the video tag.
  </video>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  SetupContext,
  PropType,
  watch,
  ref,
  onMounted,
  onBeforeUnmount,
} from "@vue/composition-api";
import {
  IR4sSocketVideoMessage,
  IR4sSocketVideoPayload,
  IRs4ScoreboardOutput,
} from "../../../../rs4/rs4-scoreboard-models";
import { ScoreboardOutputService } from "../../../scoreboard-output-service";
import EaAward from "../../../../../../common/ui/ea-award/EaAward.vue";
import { IR4sSocketDataMessageDatav3 } from "../useScoreboardOutputDisplayV3";
import { IScoreboardVideoState } from "./video-models-v3";
import { getVideoLink } from "./video-service-v3";
import { getFileName } from "../../../../../results/results-file-import/results-import-service";

const scoreboardOutputService = new ScoreboardOutputService();

export default defineComponent({
  name: "VideoV3",
  components: { EaAward },
  props: {
    rs4ScoreboardOutput: {
      type: Object as PropType<IRs4ScoreboardOutput>,
      default: () => {
        return scoreboardOutputService.factoryScoreboardOutput();
      },
    },
    socketDataMessage: {
      type: Object as PropType<IR4sSocketVideoMessage>,
      required: true,
    },
    videoShouldLoopCount: {
      type: Number,
      default: 1,
    },
  },
  setup(
    props: {
      rs4ScoreboardOutput: IRs4ScoreboardOutput;
      socketDataMessage: IR4sSocketVideoMessage;
      videoShouldLoopCount: number;
    },
    context: SetupContext
  ) {
    let state: IScoreboardVideoState = reactive({
      socketVideoMessage: props.socketDataMessage,
    });

    // const videoRef = ref();
    let videoEle: HTMLVideoElement | null;
    // let pageCycleTimeOut: any;

    //  I guess should use const videoRef = ref<HTMLVideoElement | null>(null);...but...
    const random6Char = Math.random().toString(36).substring(7);

    const videoLink = ref("");
    const videoLinkName = ref("");

    const videoLoopCount = ref(0);

    //  Create a variable that checks whether browser is: Chrome, firefox, Safari, or Edge
    //  ...see https://stackoverflow.com/questions/9847580/how-to-detect-safari-chrome-ie-firefox-and-opera-browser
    //  ...and https://stackoverflow.com/questions/4565112/javascript-how-to-find-out-if-the-user-browser-is-chrome
    const whatIsBrowser = {
      isChrome: Boolean((window as any).chrome),
      isSafari: Boolean((window as any).safari),
      isFirefox: Boolean(
        (window as any).navigator.userAgent.search("Firefox") > -1
      ),
      isEdge: Boolean((window as any).StyleMedia),
    };

    console.log("VideoV3 whatIsBrowser: ", whatIsBrowser);

    //  This is a "cheat" way to get video to auto start.  It's not ideal, but it works.
    //  ...see https://stackoverflow.com/questions/34764876/html-5-video-autoplay-not-automatically-starting-in-chrome
    //  ...and var video=document.getElementById("videoId");...video.play()

    onMounted(() => {
      // videoEle = document.getElementById(random6Char) as HTMLVideoElement;

      init();
    });

    watch(
      () => props.socketDataMessage,
      (newValue: IR4sSocketVideoMessage, oldValue: IR4sSocketVideoMessage) => {
        init();
      }
    );

    function init() {
      // let pageCycleMs = 5000;

      videoEle = document.getElementById(random6Char) as HTMLVideoElement;
      if (!videoEle) {
        console.error("init()...videoEle not found...");
        return;
      }

      videoEle.loop = false;

      const message =
        props.socketDataMessage as any as IR4sSocketDataMessageDatav3;
      if (message.meta) {
        // pageCycleMs = message.meta.displayTimeOutMs;
      }

      videoLink.value = getVideoLink(
        props.socketDataMessage.payload as IR4sSocketVideoPayload
      );
      videoLinkName.value = getFileName(videoLink.value);
      // showVideo.value = true;

      // if (videoEle) {
      console.log("init()...load...: " + new Date().toLocaleTimeString());

      //  This is randomly getting set to false...so set it to true here.  But still loops???????

      videoEle.load();
      console.log("init()...load(ed)...: " + new Date().toLocaleTimeString());
      videoEle
        .play()
        .then(() => {
          console.log(
            "videoEle.play().then()...: " + new Date().toLocaleTimeString()
          );
        })
        .catch(() => {
          console.log(
            "videoEle.play().catch()...: " + new Date().toLocaleTimeString()
          );
        })
        .finally(() => {
          console.log(
            "videoEle.play().finally()...: " + new Date().toLocaleTimeString()
          );
        });

      videoEle.onplaying = function () {
        console.warn(
          ">>>>>>> videoEle.onplaying...: " + new Date().toLocaleTimeString()
        );
        //  Chrome loops this...Firefox does not
        checkIfShouldStop("onplay");
        videoLoopCount.value++;
      };

      videoEle.onended = function () {
        console.error(
          ">>>>>>> videoEle.onended...: " + new Date().toLocaleTimeString()
        );

        if (
          props.videoShouldLoopCount === -1 ||
          videoLoopCount.value < props.videoShouldLoopCount
        ) {
          videoEle!.play();
          return;
        }

        videoEle!.pause();
        context.emit("onFinishPageCycle", 1);
      };
    }

    function checkIfShouldStop(triggeredBy: string) {
      console.warn(
        ">>>>>>> checkIfShouldStop(" +
          triggeredBy +
          ")...videoLoopCount: " +
          videoLoopCount.value +
          ", videoShouldLoopCount: " +
          props.videoShouldLoopCount +
          ", " +
          new Date().toLocaleTimeString()
      );

      if (videoLoopCount.value >= props.videoShouldLoopCount) {
        // if (videoEle) {
        //   videoEle.pause();
        // }
        // context.emit("onFinishPageCycle", 1);
      }
    }

    function onVideoEnd() {
      console.error(
        ">>>>>>> videoEle.onVideoEnd...: " + new Date().toLocaleTimeString()
      );
    }

    onBeforeUnmount(() => {
      // window.clearTimeout(pageCycleTimeOut);
      console.log("onBeforeUnmount()...");
    });

    return {
      state,
      videoLink,
      videoLinkName,
      random6Char,
      onVideoEnd,
    };
  },
});
</script>

<style>
.video-v3--wrapper {
  height: 80vh;
}

.video-v3--header {
  /*float: left;*/
  font-weight: 500;
  /*position: absolute;*/
  width: 100%;
  padding: 0 0 0 1em;
  background-color: black;
  color: white;
}

.video-v3--header-element {
  margin-right: 5em;
  display: inline-block;
  /*width: 25%;*/
  font-size: 1.25em;
  font-weight: 500;
}

.video-v3--row {
  border-bottom: 1px solid white;
}

.video-v3--td {
  padding: 5px;
  vertical-align: top;
}

.video-v3--td-centre {
  text-align: center;
}

.video-v3--image {
  /*height: 75vh;*/
  width: 100vw;
  /*border: 5px solid #fab001;*/
}
</style>
