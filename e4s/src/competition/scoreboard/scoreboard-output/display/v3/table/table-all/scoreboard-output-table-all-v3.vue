<template>
  <div class="e4s-flex-column">
    <ScoreboardOutputTableRowAllV3
      class="e4s-repeatable-grid--top"
      v-for="scoreboardMessageReason in scoreboardMessageReasons"
      :scoreboard-message-reason="scoreboardMessageReason"
      :key="messageKey(scoreboardMessageReason)"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { IScoreboardMessageReason } from "../../useScoreboardOutputDisplayV3";
import ScoreboardOutputTableRowAllV3 from "./scoreboard-output-table-row-all-v3.vue";

export default defineComponent({
  name: "ScoreboardOutputTableAllV3",
  components: { ScoreboardOutputTableRowAllV3 },
  props: {
    scoreboardMessageReasons: {
      type: Array as PropType<IScoreboardMessageReason[]>,
      required: true,
    },
  },
  setup(
    props: { scoreboardMessageReasons: IScoreboardMessageReason[] },
    context: SetupContext
  ) {
    function messageKey(scoreboardMessageReason: IScoreboardMessageReason) {
      return scoreboardMessageReason.message.key + scoreboardMessageReason.time;
    }

    return { messageKey };
  },
});
</script>
