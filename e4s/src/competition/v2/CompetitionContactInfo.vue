<template>
  <div class="e4s-flex-column">
    <div class="e4s-flex-row">
      <FormGenericSectionHeader value="Contact Information" />

      <ButtonGenericV2
        class="e4s-button--150 e4s-flex-row--end"
        button-type="primary"
        text="Contact Organiser"
        v-on:click="contactOrganiser"
      />
    </div>

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericSpanTextV2
          v-if="getContactName.length > 0"
          form-label="Name"
          :value="getContactName"
        />

        <FormGenericSpanTextV2
          v-if="getContactTel.length > 0"
          form-label="Telephone"
          :value="getContactTel"
        />

        <FormGenericSpanTextV2 form-label="Email" :value="getContactTel">
          <a
            slot="field"
            :href="'mailto:' + getContactEmail"
            class="e4s-hyperlink--100 e4s-hyperlink--primary"
            v-text="getContactEmail"
          ></a>
        </FormGenericSpanTextV2>
        <!--        <FormGenericInputTemplateV2-->
        <!--          form-label="Email"-->
        <!--          v-if="getContactEmail.length > 0"-->
        <!--        >-->
        <!--          <template slot="field">-->
        <!--            <a-->
        <!--              :href="'mailto:' + getContactEmail"-->
        <!--              class="e4s-hyperlink&#45;&#45;100 e4s-hyperlink&#45;&#45;primary"-->
        <!--              v-text="getContactEmail"-->
        <!--            ></a>-->
        <!--          </template>-->
        <!--        </FormGenericInputTemplateV2>-->
      </template>
    </FormGenericFieldGridV2>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../competition-models";
import CounterV2 from "../../common/ui/layoutV2/counter/counter-v2.vue";
import FormGenericSectionHeader from "../../common/ui/layoutV2/form/form-generic-section-header.vue";
import FormGenericFieldGridV2 from "../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericSpanTextV2 from "../../common/ui/layoutV2/form/form-generic--span-text-v2.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

export default defineComponent({
  name: "CompetitionContactInfo",
  components: {
    ButtonGenericV2,
    FormGenericInputTemplateV2,
    FormGenericSpanTextV2,
    FormGenericFieldGridV2,
    FormGenericSectionHeader,
    CounterV2,
  },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
  },
  setup(
    props: { competitionSummaryPublic: ICompetitionSummaryPublic },
    context: SetupContext
  ) {
    const getContactName = computed(() => {
      const contact = props.competitionSummaryPublic.options.contact;
      return contact.userName.length > 0 ? contact.userName : "";
    });

    const getContactTel = computed<string>(() => {
      const contact = props.competitionSummaryPublic.options.contact;
      const tel = contact.tel.toString();
      return tel.length > 0 ? tel : "";
    });

    const getContactEmail = computed(() => {
      const contact = props.competitionSummaryPublic.options.contact;
      return contact.email.length > 0 ? contact.email : "";
    });

    function contactOrganiser() {
      context.emit("contactOrganiser", props.competitionSummaryPublic);
    }

    return { getContactName, getContactTel, getContactEmail, contactOrganiser };
  },
});
</script>
