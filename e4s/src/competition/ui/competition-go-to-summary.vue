<template>
  <a :href="getPath" v-text="linkText"></a>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { LAUNCH_ROUTES_PATHS } from "../../launch/launch-routes";

@Component({
  name: "competition-go-to-summary",
})
export default class CompetitionGoToSummary extends Vue {
  @Prop({
    default: 0,
  })
  public readonly compId!: number;

  @Prop({
    default: "Comp Admin",
  })
  public readonly linkText!: string;

  public get getPath() {
    return "#/" + LAUNCH_ROUTES_PATHS.SHOW_ENTRY + "/" + this.compId;
  }
}
</script>
