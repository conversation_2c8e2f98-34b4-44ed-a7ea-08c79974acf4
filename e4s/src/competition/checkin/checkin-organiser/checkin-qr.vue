<template>

    <div>
        <div class="only-print-this">

            <div class="qr--page-layout" v-if="!isLoading">
                <div class="qr--header">
                    <div v-text="getHeaderTitle" class="center qr--header-message"></div>
                    <div v-text="getLocationAndDate" class="center qr--header-location"></div>
                </div>

                <div class="qr--body">

                    <div v-html="getInfoText" class="qr--body-info-text"></div>

                    <div>
                        1. If you have received an email with a "check in" link, use this link to
                        check in and enter code:<br/>
                        <span v-text="compCheckInCode" class="qr--code-number"></span>
                    </div>

                    <div class="qr--body-check-type-sep"></div>
                    <!--                <div class="qr&#45;&#45;body-or">OR</div>-->

                    <div>
                        2. Scan the QR code below with your mobile phone camera or suitable device.
                        <div v-html="qrHtml" class="qr--code-image"></div>
                    </div>

                    <div class="qr--body-check-type-sep"></div>
                    <!--                <div class="qr&#45;&#45;body-or">OR</div>-->

                    <div>
                        3. Visit this link: <span v-text="checkInUrl" class="qr--body-url"></span>
                    </div>

                    <div class="qr--body-check-type-sep"></div>
                    <!--                <div class="qr&#45;&#45;body-or">OR</div>-->

                    <div>
                        4. Go to registration.
                    </div>
                </div>

                <div class="qr--footer">
                    <div class="qr--footer-e4s">
                        <img class="qr--e4s-logo" :src="require('../../../images/e4s-black.png')"/>
                        <span class="qr--e4s-text">Powered by Entry4Sports</span>
                    </div>
                </div>


            </div>

        </div>

    </div>

</template>

<script lang="ts">
    import Component from "vue-class-component";
    import Vue from "vue";
    import {ICheckinCompSummary} from "../checkin-models";
    import qrcode from "qrcode-generator";
    import { Watch, Prop } from "vue-property-decorator";
    import {CheckinService} from "../checkin-service";
    import {format, parse} from "date-fns"
    import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";

    const checkinService = new CheckinService();

    @Component(
        {
            name: "checkin-qr"
        }
    )
    export default class CheckinQr extends Vue {

        @Prop({
            default: () => {
                return checkinService.factoryCheckinCompSummary()
            }
        }) public readonly checkinCompSummary: ICheckinCompSummary;
        @Prop({
            default: ""
        }) public readonly shortUrl: string;
        @Prop({
            default: false
        }) public readonly isLoading: boolean;
        @Prop({
            default: ""
        }) public readonly compCheckInDate: string;
        @Prop({
            default: ""
        }) public readonly compCheckInCode: string;

        public qrHtml: string = "<div>QR Code loading...</div>";
        public checkInUrl: string = "";

        public LAUNCH_ROUTES_PATHS = LAUNCH_ROUTES_PATHS;

        public created() {
            this.buildQrCode();
        }

        @Watch("shortUrl")
        public onShortUrlChanged(newValue: string) {
            this.buildQrCode();
        }

        public buildQrCode() {
            if (this.shortUrl.length > 0) {
                this.checkInUrl = this.shortUrl.replace("{compid}", this.checkinCompSummary.id.toString());
                const qr = qrcode(0, 'M');
                qr.addData(unescape(encodeURI(this.checkInUrl )));
                qr.make();
                this.qrHtml = qr.createImgTag(4, 4);
            }
        }

        public get getHeaderTitle(): string {
            const isOrgInCompName = this.checkinCompSummary.name.toLowerCase().indexOf(this.checkinCompSummary.organiser.name.toLowerCase()) > -1;
            return (isOrgInCompName  ? "" : this.checkinCompSummary.organiser.name + " - ") +
                this.checkinCompSummary.name;
        }

        public get getInfoText() {
            return this.checkinCompSummary.checkIn.text;
        }

        public get getLocationAndDate() {
            const compCheckInDate = this.compCheckInDate.length > 0 ? format(parse(this.compCheckInDate), "ddd Do MMM YYYY") : "";
            return compCheckInDate + " " + this.checkinCompSummary.location.name;
        }
    }
</script>

<style scoped>

    .qr--page-layout {
        border-radius: 2vw;
        border: solid 2px black;
        padding: 2vw;
        margin: 1vw;
    }

    .qr--header {
        margin-bottom: 2vw;
    }

    .qr--header-message {
        font-size: 3vw;
    }

    .qr--header-location {
        font-size: 2vw;
    }

    .qr--body {
        font-size: 2vw;
    }

    .qr--body-info-text {
        margin-bottom: 5vw;
    }

    .qr--body-check-type-sep {
        margin-bottom: 3vw;
    }

    .qr--code-number {
        font-weight: bold;
        letter-spacing: 1vw;
    }

    .qr--body-or {
        margin: 1em 0 1em 0;
    }

    .qr--code-image {
        text-align: center;
    }

    .qr--body-url {
        font-weight: bold;
    }

    .qr--footer {

    }

    .qr--footer-e4s {
        text-align: right;
    }
    .qr--e4s-logo {
        height: 100px;
        vertical-align: middle;
    }

    .qr--e4s-text {
        font-size: 2vw;
    }


    @page {
        size: A4;
        margin: 0;
        /*background-color: red;*/
    }
    @media print {

        .only-print-this {
            width: 210mm;
            height: 290mm;
        }

    }
</style>
