<template>

        <div class="row">
            <div class="col s1 m1 l1">
                <div class="e4s-indicator e4s-force-inline-block" :class="getCheckedInClass"></div>
            </div>
            <div class="col s2 m2 l2">
                <span v-text="athleteEntryInternal.checkedIn ? 'Confirmed' : 'Not confirmed'"></span>
            </div>
            <div class="col s4 m4 l4">
                <div class="checkin-user-entry--start-time e4s-force-inline-block" v-text="getStartTime"></div>
            </div>
            <div class="col s3 m3 l3">
                <span class="checkin-user-entry--event" v-text="athleteEntryInternal.name"></span>
            </div>
            <div class="col s2 m2 l2">
                (<span class="checkin-user-entry--age-group" v-text="athleteEntryInternal.ageGroup"></span>)
            </div>
        </div>

</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component"
    import { Prop, Watch } from "vue-property-decorator";
    import {CheckinService} from "../checkin-service";
    import {IAthleteEntry} from "../checkin-models"
    import {format, parse} from "date-fns"

    const checkinService: CheckinService = new CheckinService();

    @Component({
        name: "whoishere-user-entry"
    })
    export default class WhoishereUserEntry extends Vue {
        @Prop({
            default: () => {
                return checkinService.factoryAthleteEntry();
            }
        }) public readonly athleteEntry: IAthleteEntry;

        @Prop({
            default: false
        }) public readonly isEventDisabled: boolean;

        public athleteEntryInternal: IAthleteEntry = checkinService.factoryAthleteEntry();
        // public dateTimeNow = new Date();
        // public dateTimePattern = "HH:mm Do MMM";

        public created() {
            this.athleteEntryInternal = R.clone(this.athleteEntry);
        }

        @Watch("athleteEntry")
        public onAthleteEntryChanged(newValue: IAthleteEntry, oldValue: IAthleteEntry) {
            this.athleteEntryInternal = R.clone(newValue);
        }

        public get getEntryName() {
            const startTime = format(parse(this.athleteEntryInternal.dateTime), "hh:mma");
            return "<span class=''>" + startTime + "</span>" +
                "<span>" + this.athleteEntryInternal.ageGroup + "</span>" +
                " - <span>" + this.athleteEntryInternal.name + "</span>";
                // ", before: " + this.isEventCheckInBefore + ", opens: " + this.checkInOpensMessage + ", after: " + this.isEventCheckInAfter;
        }

        public get getStartTime() {
            return format(parse(this.athleteEntryInternal.dateTime), "hh:mma");
        }

        public get getCheckedInClass() {
            return this.athleteEntryInternal.checkedIn ? "e4s-indicator--green" : "e4s-indicator--red"
        }

        // public get isEventCheckInBefore() {
        //     if (this.athleteEntryInternal.checkInFrom > -1 ) {
        //         const checkInFromFns = parse(this.athleteEntryInternal.checkInFrom);
        //         if (isBefore(this.dateTimeNow, checkInFromFns)) {
        //             return true;
        //         }
        //     }
        //     return false;
        // }

        // public get isEventCheckInAfter() {
        //     if (this.athleteEntryInternal.checkInTo > -1 ) {
        //         const checkInToFns = parse(this.athleteEntryInternal.checkInTo);
        //         if (isAfter(this.dateTimeNow, checkInToFns)) {
        //             return true;
        //         }
        //     }
        //     return false;
        // }
        //
        // public get eventAtMessage() {
        //     return format(parse(this.athleteEntryInternal.dateTime), this.dateTimePattern);
        // }

        // public get checkInOpensMessage() {
        //     if (this.athleteEntryInternal.checkInFrom > -1 && this.isEventCheckInBefore) {
        //         return "Check in opens: " + format(addMinutes(parse(this.athleteEntryInternal.dateTime), -this.athleteEntryInternal.checkInFrom), this.dateTimePattern);
        //     }
        //     return "Check in is open";
        // }
        //
        // public get checkInClosesMessage() {
        //     if (this.athleteEntryInternal.checkInTo > -1) {
        //         return "Check in closes: " + format(
        //             addMinutes(
        //                 parse(this.athleteEntryInternal.dateTime),
        //                 -this.athleteEntryInternal.checkInTo),
        //                 this.dateTimePattern
        //             );
        //     }
        //     return "Check in is open";
        // }

        // public get isEventCheckInDisabled() {
        //
        //     if (this.isEventDisabled) {
        //         return true;
        //     }
        //
        //     // const dateTimeNow = new Date();
        //     // if (this.athleteEntryInternal.checkInFrom > -1 ) {
        //     //     const checkInFromFns = parse(this.athleteEntryInternal.checkInFrom);
        //     //     if (isBefore(dateTimeNow, checkInFromFns)) {
        //     //         return true;
        //     //     }
        //     // }
        //     //
        //     // if (this.athleteEntryInternal.checkInTo > -1 ) {
        //     //     const checkInToFns = parse(this.athleteEntryInternal.checkInTo);
        //     //     if (isAfter(dateTimeNow, checkInToFns)) {
        //     //         return true;
        //     //     }
        //     // }
        //     return false;
        // }

        public entrySelected() {
            this.$emit("onChange", R.clone(this.athleteEntryInternal));
        }

    }
</script>

<!--<style>-->
<!--    .whoishere-user-entry&#45;&#45;checked-in-status {-->
<!--      height: 1em;-->
<!--      width: 0.5em;-->
<!--    }-->
<!--    .whoishere-user-entry&#45;&#45;checked-in {-->
<!--        background-color: #0f9d58;-->
<!--    }-->
<!--    .whoishere-user-entry&#45;&#45;not-checked-in {-->
<!--      background-color: red;-->
<!--    }-->
<!--</style>-->
