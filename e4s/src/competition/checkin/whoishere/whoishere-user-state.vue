<template>
    <div>

        <div class="e4s-simple-form--header">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="left">
                        Bib: <span v-text="athleteCheckinInternal.bibNo"></span>
                        &nbsp
                        <span class="e4s-simple-form--header-text" :id="PREFIX + 'get-name'" v-text="getName"></span>
                    </div>
                    <div class="right">
                        <span v-text="athleteCheckinInternal.collected ? 'Registered' : 'Not Registered'"></span>
                        <div class="e4s-force-inline-block e4s-indicator" :class="athleteCheckinInternal.collected ? 'e4s-indicator--green' : 'e4s-indicator--red'"></div>
                    </div>
                </div>
            </div>


            <div class="row e4s-simple-form--header-sub-text-row">
                <div class="col s12 m12 l12">
                    <div class="left">
                        <span class="e4s-simple-form--header-sub-text" v-text="getHeaderInfoLeft"></span>
                    </div>
                    <div class="right">
                        <span class="e4s-simple-form--header-sub-text" v-text="getHeaderInfoRight"></span>
                    </div>
                </div>
            </div>

        </div>

<!--        <div class="e4s-section-padding-separator"></div>-->

<!--        <div class="row">-->
<!--            <div class="col s12 m12 l12">-->
<!--                Events the athlete confirmed-->
<!--                &nbsp;<div class="e4s-force-inline-block e4s-indicator e4s-indicator&#45;&#45;green"></div>&nbsp;Confirmed-->
<!--                &nbsp;<div class="e4s-force-inline-block e4s-indicator e4s-indicator&#45;&#45;red"></div>&nbsp;Not Confirmed-->
<!--            </div>-->
<!--        </div>-->

        <div id="checkin-entries">

            <div v-for="(entry, index) in athleteCheckinInternal.entries"
                 class="e4s-card-std"
                 :class="index % 2 === 0 ? '' : 'e4s-card-std__row-odd'"
                 :key="entry.id">

                <WhoishereUserEntry class="checkin-user--entries"
                                    :athlete-entry="entry">
                </WhoishereUserEntry>
            </div>

        </div>

        <div class="e4s-section-padding-separator"></div>
        <div class="row">
            <div class="col s12 m12 l12">
                <a href="#" v-on:click.prevent="showMoreCheckInDetails = !showMoreCheckInDetails">
                    <span v-text="showMoreCheckInDetails ? 'Hide' : 'Show More...'"></span>
                </a>
            </div>
        </div>

        <div class="row" v-if="showMoreCheckInDetails">
            <div class="input-field col s12 m6 l6">

                <label class="active">
                    Check in time for this athlete:
                </label>
                <div v-text="getCheckinTimeRangeMessage"></div>
                <div v-text="getCheckinSuggestedMessage"></div>

            </div>

            <div class="input-field col s12 m6 l6">

                <label class="active">
                    Confirmed Time:
                </label>
                <span v-text="getConfirmedTime"></span>
            </div>

            <div class="e4s-section-padding-separator"></div>

<!--            <div class="row">-->
<!--                <div class="col s12 m12 l12" v-if="showBibCollected">-->
<!--                    Registered <span v-text="getCollectedTime"></span>-->
<!--                </div>-->

<!--            </div>-->

        </div>


    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Component from "vue-class-component";
    import Vue from "vue";
    import FieldValidationLabel from "../../../validation/validation-field-lable.vue";
    import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
    import { Prop, Watch } from "vue-property-decorator";
    import {ICheckinAthlete, IAthleteEntry, CHECKIN_STATUS, ICheckinCompSummary, IAthleteCheckinDetails} from "../checkin-models"
    import { CheckinService } from "../checkin-service";
    import {ValidationController} from "../../../validation/validation-controller";
    import {FormController} from "../../../common/ui/form/form-controller/form-controller";
    import {parse, format, isAfter, isBefore} from "date-fns"
    import CheckinUserEntry from "../checkin-organiser/checkin-user-entry.vue";
    import WhoishereUserEntry from "./whoishere-user-entry.vue"

    const checkinService: CheckinService = new CheckinService();

    @Component({
        name: "whoishere-user-state",
        components: {
            WhoishereUserEntry,
            CheckinUserEntry,
            FieldValidationLabel,
            FieldHelp
        }
    })
    export default class WhoishereUserState extends Vue {
        @Prop({
            default: () => {
                return checkinService.factoryCheckinAthlete();
            }
        }) public readonly athleteCheckin: ICheckinAthlete;
        @Prop({
            default: () => {
                return checkinService.factoryCheckinCompSummary();
            }
        }) public readonly checkinCompSummary: ICheckinCompSummary;
        @Prop({
            default: false
        }) public readonly isLoading: boolean;
        @Prop(
            {
                default: true
            }
        ) public readonly showBibCollected: boolean;
        @Prop(
            {
                default: false
            }
        ) public readonly disableBibCollected: boolean;
        @Prop(
            {
                default: false
            }
        ) public readonly isOrganiser: boolean;

        public athleteCheckinInternal: ICheckinAthlete = checkinService.factoryCheckinAthlete();
        public athleteCheckinOriginal: ICheckinAthlete = checkinService.factoryCheckinAthlete();
        public PREFIX = Math.random().toString(36).substring(2);
        public validationController: ValidationController = new ValidationController();
        public formController: FormController = new FormController(this.athleteCheckin, this.athleteCheckinInternal);
        public checkinService = checkinService;
        public showMoreCheckInDetails = false;


        public created() {
            this.athleteCheckinOriginal = R.clone(this.athleteCheckin);
            this.athleteCheckinInternal = R.clone(this.athleteCheckin);
            // this.formController.setSources(this.athleteCheckin, this.athleteCheckinInternal);
            console.log("CheckinUser.created() bibNo: " + this.athleteCheckin.bibNo);
            this.setFromController();
        }

        @Watch("athleteCheckin")
        public onAthleteCheckinChange(newValue: ICheckinAthlete, oldValue: ICheckinAthlete) {
            // they don't have newValue.id
            console.log("CheckinUser.Watch(athleteCheckin) bibNo: " + this.athleteCheckin.bibNo);
            this.athleteCheckinInternal = R.clone(newValue);
            // this.formController.setSources(this.athleteCheckin, this.athleteCheckinInternal);
            this.setFromController();
        }

        public setFromController() {
            if (this.athleteCheckin.bibNo > 0 && this.athleteCheckinInternal.bibNo > 0) {
                this.formController.setSources(this.athleteCheckinOriginal, this.athleteCheckinInternal);
            }
        }

        public get getName(): string {
            return this.athleteCheckinInternal.firstName + " " + this.athleteCheckinInternal.surName;
        }

        public get getDOB(): string {
            if (this.athleteCheckinInternal.dob.length === 0 ) {
                return "";
            }
            return format(parse(this.athleteCheckinInternal.dob), "Do MMM YYYY");
        }

        public getEntryName(entry: IAthleteEntry) {
            return entry.ageGroup + " - " + entry.name;
        }

        public isEventCheckInDisabled(entry: IAthleteEntry) {
            const dateTimeNow = new Date();

            if (entry.checkInFrom > -1 ) {
                const checkInFromFns = parse(entry.checkInFrom);
                if (isBefore(dateTimeNow, checkInFromFns)) {
                    return true;
                }
            }

            if (entry.checkInTo > -1 ) {
                const checkInToFns = parse(entry.checkInTo);
                if (isAfter(dateTimeNow, checkInToFns)) {
                    return true;
                }
            }
            return false;
        }

        public entrySelected(entry: IAthleteEntry) {
            this.athleteCheckinInternal.entries = this.athleteCheckinInternal.entries.map((ent) => {
                if (entry.id === ent.id) {
                    return entry
                }
                return ent;
            });

            this.formController.processChanges();
            this.onChange();
        }

        public generalOnChange(): void {
            this.formController.processChanges();
            this.onChange();
        }

        public get getHeaderInfoLeft() {
            return (this.athleteCheckinInternal.gender === "F" ? "Female" : "Male") + " / " + this.getDOB;
        }

        public get getHeaderInfoRight() {
            const urn = this.athleteCheckinInternal?.urn ? (this.athleteCheckinInternal.urn + "") : "";
            const club = this.athleteCheckinInternal.club ? this.athleteCheckinInternal.club : "Unattached";
            return club + (urn.length  > 0 ? " / " + urn :  "");
        }

        public get getHasUserConfirmed() {
            return this.athleteCheckinInternal.confirmedTime && this.athleteCheckinInternal.confirmedTime.length > 0;
        }

        public get getConfirmedTime() {
            return this.athleteCheckinInternal.confirmedTime && this.athleteCheckinInternal.confirmedTime.length > 0 ?
                format(parse(this.athleteCheckinInternal.confirmedTime), "HH:mm Do MMM YYYY") :
                "";
        }

        public get getCollectedTime() {
            return this.athleteCheckinInternal.collectedTime && this.athleteCheckinInternal.collectedTime.length > 0 ?
                format(parse(this.athleteCheckinInternal.collectedTime), "HH:mm Do MMM YYYY") :
                "";
        }

        public getEventStartTime(entry: IAthleteEntry) {
            return format(parse(entry.dateTime), "hh:mma");
        }

        public get getCheckInDetails(): IAthleteCheckinDetails {
            //  TODO
            // const pointInTime = new Date(2020, 4, 28, 9, 1, 0);
            const pointInTime = new Date();
            const checkInDateTimeOpens = this.checkinCompSummary.checkIn.checkInDateTimeOpens;
            let compDateOpens = null;
            if (checkInDateTimeOpens.length  > 0) {
                compDateOpens = parse(checkInDateTimeOpens);
            }

            return this.checkinService.getAthleteCheckinDetails(this.checkinCompSummary.checkIn, this.athleteCheckinInternal, pointInTime, compDateOpens);
        }

        public get getCheckinMessage() {
            return this.getCheckInDetails.message;
        }

        public get getCheckinTimeRangeMessage() {
            return this.getCheckInDetails.checkInRangeMessage;
        }

      public get getCheckinSuggestedMessage() {
        return "Registration: " + this.getCheckInDetails.suggestedAthleteMessage;
      }

        public onChange() {
            this.$emit("onChange", R.clone(this.athleteCheckinInternal));
        }

        public submit() {
            this.$emit("onSubmit", R.clone(this.athleteCheckinInternal));
        }

        public get getCheckInIndicatorStatusCss() {
            const cssMap: Record<CHECKIN_STATUS, string> = {
                ["NOT_YET_OPEN"]: "check-in-bg-not-yet-open",
                ["OPEN"]: "check-in-bg-open",
                ["CLOSED"]: "check-in-bg-closed"
            }
            if (cssMap[this.getCheckInDetails.status]) {
                return cssMap[this.getCheckInDetails.status];
            }
            return "";
        }

        public get getIsSaveDisabled() {
            return this.isLoading || !this.formController.isDirty || (!this.isOrganiser && this.getHasUserConfirmed);
        }

        public get getShouldBibCheckBoxBeDisabled() {
            const haveAnyEventsBeenSelected = new CheckinService().haveAnyEventsBeenSelected(this.athleteCheckinInternal);
            return this.disableBibCollected || !haveAnyEventsBeenSelected;
        }
    }

</script>

<style scoped>
    .check-in-indicator {
        width: 0.5em;
        height: 1em;
    }

    .whoishere--registered {
        background-color: #0f9d58;
    }
    .whoishere--not-registered {
        background-color: red;
    }

    .checkin-user--entries {
        padding: 0.5em 0 0.5em 0;
    }
</style>
