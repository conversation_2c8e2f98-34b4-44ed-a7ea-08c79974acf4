<template>
    <div>
        <div v-for="athlete in athleteCheckin"
             class="checkin--athlete"
             :key="athlete.id">
            <CheckinUser :athlete-checkin="athlete"
                         :disable-bib-collected="disableBibCollected"
                         :is-organiser="isOrganiser"
                         :checkin-comp-summary="checkinSummary"
                         :is-loading="isLoading"
                         v-on:onChange="onChangeAthleteCheckIn"
                         v-on:onSubmit="onSubmitAthleteCheckIn">
            </CheckinUser>
        </div>
    </div>
</template>

<script lang="ts">
    import Component from "vue-class-component";
    import Vue from "vue";
    import {Prop, Watch} from "vue-property-decorator"
    import {ICheckinAthlete, ICheckinCompSummary} from "./checkin-models";
    import {CheckinService} from "./checkin-service";
    import * as R from "ramda"
    import CheckinUser from "./checkin-organiser/checkin-user.vue"

    const checkinService = new CheckinService();

    @Component({
        name: "check-in-user-grid",
        components: {CheckinUser}
    })
    export default class CheckinUserGrid extends Vue {
        @Prop({
            default: () => []
        }) readonly athleteCheckin: ICheckinAthlete[];
        @Prop({
            default: () => {
                return checkinService.factoryCheckinCompSummary();
            }
        }) public readonly checkinSummary: ICheckinCompSummary;
        @Prop(
            {
                default: false
            }
        ) public readonly disableBibCollected: boolean;
        @Prop({
            default: false
        }) public readonly isLoading: boolean;
        @Prop(
            {
                default: false
            }
        ) public readonly isOrganiser: boolean;

        public athleteCheckinInternal: ICheckinAthlete[] = [];

        public created() {
            this.athleteCheckinInternal = R.clone(this.athleteCheckin);
        }

        @Watch("athleteCheckin")
        public onAthleteCheckinChanged(newValue: ICheckinAthlete[]) {
            this.athleteCheckinInternal = R.clone(newValue);
        }

        public onChangeAthleteCheckIn(athleteCheckIn: ICheckinAthlete) {
            this.$emit("onChange", R.clone(athleteCheckIn))
        }

        public onSubmitAthleteCheckIn(athleteCheckIn: ICheckinAthlete) {
            this.$emit("onSubmit", R.clone(athleteCheckIn));
        }

    }
</script>
