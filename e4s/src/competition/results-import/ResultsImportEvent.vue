<template>
  <AccordionV3
    :title="eventTitle"
    :is-expanded="expanded"
    :suppress-row-click="true"
    :show-arrow="false"
    @onIsExpanded="expandSection"
  >
    <div
      slot="summary"
      class="e4s-flex-row e4s-full-width e4s-justify-flex-row-vert-center"
    >
      <div class="e4s-flex-row e4s-gap--standard e4s-header--500">
        <div
          v-text="eventTitle"
          class="results-import--event-group-header"
        ></div>
        <div
          class="e4s-flex-row e4s-gap--standard"
          style="color: var(--zinc-500)"
        >
          <div
            :class="hasFinalRounds ? '' : 'e4s-info-text--error '"
            v-text="hasFinalRounds ? 'Final(s)' : 'No Finals'"
          ></div>
          <div v-text="isFieldEvent ? 'F' : 'T'"></div>
          <div v-text="'Ages(s): ' + ageGroupsCount"></div>
          <div
            v-if="targetEntries.length > 0"
            v-text="'Entries: ' + targetEntries.length"
            class="results-import--event-group-entries"
          ></div>
          <div
            v-if="targetEntries.length > 0"
            v-text="'Entries'"
            class="results-import--event-group-entries"
          ></div>
        </div>

        <!--        <a href="#" v-on:click.prevent="getResults">Get Results</a>-->
      </div>

      <div
        class="
          e4s-flex-row
          e4s-gap--standard
          e4s-justify-flex-row-vert-center
          e4s-flex-row--end
        "
      >
        <!--        <span v-text="sourceSecurity" v-if="!hasAnyTargetEntries"></span>-->
        <!--        <span-->
        <!--          v-if="hasAnyTargetEntries"-->
        <!--          :title="state.sourceEventGroupSummary.id"-->
        <!--          v-text="state.sourceEventGroupSummary.name"-->
        <!--        ></span>-->

        <PrimaryLink
          v-if="
            sourceBestGuessResultsImportEventGroupSummary.closeMatches.length >
            1
          "
          @onClick="toggleShowBestGuesses('source')"
          :link-text="sourceShowBestGuesses ? 'All' : 'Filter'"
        />
        <EventGroupSelectV2
          style="width: auto"
          :value="state.sourceEventGroupSummary"
          :event-group-summaries="getSourceEventSummaries"
          v-on:input="setSource"
        />

        <span>to</span>

        <PrimaryLink
          v-if="
            targetBestGuessResultsImportEventGroupSummary.closeMatches.length >
            1
          "
          @onClick="toggleShowBestGuesses('target')"
          :link-text="targetShowBestGuesses ? 'All' : 'Filter'"
        />
        <EventGroupSelectV2
          style="width: auto"
          :value="state.targetEventGroupSummary"
          :event-group-summaries="getTargetEventSummaries"
          v-on:input="setTarget"
        />

        <!--        <div-->
        <!--          class="e4s-flex-row e4s-gap&#45;&#45;small"-->
        <!--          v-if="showSourceEventGroupSummaries"-->
        <!--        >-->
        <!--          <span v-if="!hasAnyTargetEntries"-->
        <!--            ><span v-text="sourceSecurity"></span-->
        <!--          ></span>-->
        <!--          <div class="e4s-flex-column e4s-gap&#45;&#45;small e4s-full-width">-->
        <!--            <span-->
        <!--              v-if="hasAnyTargetEntries"-->
        <!--              :title="state.sourceEventGroupSummary.id"-->
        <!--              v-text="state.sourceEventGroupSummary.name"-->
        <!--            ></span>-->
        <!--            <EventGroupSelectV2-->
        <!--              :value="state.sourceEventGroupSummary"-->
        <!--              :event-group-summaries="getSourceEventSummaries"-->
        <!--              v-on:input="setSource"-->
        <!--            />-->

        <!--            <div-->
        <!--              v-if="-->
        <!--                sourceBestGuessResultsImportEventGroupSummary.closeMatches-->
        <!--                  .length !== 1-->
        <!--              "-->
        <!--              class="e4s-flex-row e4s-gap&#45;&#45;small"-->
        <!--            >-->
        <!--              <a-->
        <!--                v-if="-->
        <!--                  sourceBestGuessResultsImportEventGroupSummary.closeMatches-->
        <!--                    .length > 1-->
        <!--                "-->
        <!--                href="#"-->
        <!--                v-on:click.prevent="toggleShowBestGuesses('source')"-->
        <!--                v-text="sourceShowBestGuesses ? 'All' : 'Filter'"-->
        <!--              ></a>-->
        <!--              <span-->
        <!--                class="results-import&#45;&#45;best-message"-->
        <!--                v-text="getSourceBestGuessMessage"-->
        <!--                :title="getSourceBestGuessMessage"-->
        <!--              ></span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->

        <!--        <div class="e4s-flex-row e4s-gap&#45;&#45;small">-->
        <!--          <span v-if="!hasAnyTargetEntries"-->
        <!--            ><span v-text="targetSecurity"></span-->
        <!--          ></span>-->
        <!--          <div class="e4s-flex-column e4s-gap&#45;&#45;small e4s-full-width">-->
        <!--            &lt;!&ndash;            v-show="!hasAnyTargetEntries"&ndash;&gt;-->
        <!--            <span-->
        <!--              v-if="hasAnyTargetEntries"-->
        <!--              :title="state.targetEventGroupSummary.id"-->
        <!--              v-text="state.targetEventGroupSummary.name"-->
        <!--            ></span>-->
        <!--            <EventGroupSelectV2-->
        <!--              :value="state.targetEventGroupSummary"-->
        <!--              :event-group-summaries="getTargetEventSummaries"-->
        <!--              v-on:input="setTarget"-->
        <!--            />-->
        <!--            &lt;!&ndash;            v-show="!hasAnyTargetEntries"&ndash;&gt;-->
        <!--            <div-->
        <!--              v-if="-->
        <!--                targetBestGuessResultsImportEventGroupSummary.closeMatches-->
        <!--                  .length !== 1-->
        <!--              "-->
        <!--              class="e4s-flex-row e4s-gap&#45;&#45;small"-->
        <!--            >-->
        <!--              <a-->
        <!--                v-if="-->
        <!--                  targetBestGuessResultsImportEventGroupSummary.closeMatches-->
        <!--                    .length > 1-->
        <!--                "-->
        <!--                href="#"-->
        <!--                v-on:click.prevent="toggleShowBestGuesses('target')"-->
        <!--                v-text="targetShowBestGuesses ? 'All' : 'Filter'"-->
        <!--              ></a>-->
        <!--              <span-->
        <!--                class="results-import&#45;&#45;event-group-best-message"-->
        <!--                v-text="getTargetBestGuessMessage"-->
        <!--                :title="getTargetBestGuessMessage"-->
        <!--              ></span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->

        <!--        <button-->
        <!--          class="btn waves-effect waves"-->
        <!--          :class="expanded ? 'red' : 'blue'"-->
        <!--          :disabled="state.targetEventGroupSummary.id === 0"-->
        <!--          style="width: 30px"-->
        <!--          v-on:click="expandSection"-->
        <!--          v-text="expanded ? '-' : '+'"-->
        <!--        ></button>-->
        <ButtonGenericV2
          style="width: 50px"
          :button-type="expanded ? 'destructive' : 'primary'"
          :disabled="state.targetEventGroupSummary.id === 0"
          @click="expandSection"
          :text="expanded ? '-' : '+'"
        />
      </div>
    </div>

    <div slot="content">
      <ResultsImportEventList
        :time-tronics-event="timeTronicsEvent"
        :time-tronics-event-list="timeTronicsEventList"
        :results-import-event-group-summary="state.targetEventGroupSummary"
        :source-event-group-summary="state.sourceEventGroupSummary"
        v-if="
          cheatLazyRefreshIsVisible &&
          expanded &&
          gotTimeTronicsEventListData &&
          state.targetEventGroupSummary.id > 0
        "
        v-on:getOwners="getOwners"
        @getOwnersForParticipants="getOwnersForParticipants"
        v-on:doSubmit="doSubmit"
      />
    </div>
  </AccordionV3>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { ITimeTronicsEvent } from "./results-import-models-timetronics";
import * as ResultsImportService from "./results-import-service";
import EventGroupSelectV2 from "../../builder/form/event-group/event-group-select-v2.vue";
import {
  IFlattenedParticipation,
  IOutputResultsImportEventOnExpanded,
  IResultsImportEventGroupSummary,
  IResultsImportEventOutput,
  IResultsImportEventState,
  MapFlattenedParticipations,
} from "./results-import-models";
import { CompEventService } from "../../compevent/compevent-service";
import { simpleClone } from "../../common/common-service-utils";
import {
  ITimeTronicsEventList,
  ITimeTronicsHeatList,
} from "./results-import-models-timetronics-list";
import ResultsImportEventList from "./ResultsImportEventList.vue";
import { useResultsImportController } from "./useResultsImportController";
import { IR4sAthleteEntry } from "../scoreboard/rs4/rs4-scoreboard-models";
import { IBestGuessResultsImportEventGroupSummary } from "./results-import-service";
import { useConfigController } from "../../config/useConfigStore";
import { AgeGroupService } from "../../agegroup/agegroup-service";
import AccordionV3 from "../../common/ui/layoutV2/accordion/AccordionV3.vue";
import PrimaryLink from "../../common/ui/layoutV2/href/PrimaryLink.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

export default defineComponent({
  name: "results-import-event",
  components: {
    ButtonGenericV2,
    PrimaryLink,
    AccordionV3,
    ResultsImportEventList,
    EventGroupSelectV2,
  },
  props: {
    timeTronicsEvent: {
      type: Object as PropType<ITimeTronicsEvent>,
      required: true,
    },
    showSourceEventGroupSummaries: {
      type: Boolean,
      required: true,
    },
    sourceEventGroupSummaries: {
      type: Array as PropType<IResultsImportEventGroupSummary[]>,
      default: () => {
        return [];
      },
    },
    targetEventGroupSummaries: {
      type: Array as PropType<IResultsImportEventGroupSummary[]>,
      default: () => {
        return [];
      },
    },
    timeTronicsEventList: {
      type: Object as PropType<ITimeTronicsEventList | undefined>,
    },
  },
  setup(
    props: {
      timeTronicsEvent: ITimeTronicsEvent;
      showSourceEventGroupSummaries: boolean;
      sourceEventGroupSummaries: IResultsImportEventGroupSummary[];
      targetEventGroupSummaries: IResultsImportEventGroupSummary[];
      timeTronicsEventList: ITimeTronicsEventList | undefined;
    },
    context: SetupContext
  ) {
    const configController = useConfigController();

    const compEventService = new CompEventService();
    // const importServiceController = resultsImportServiceController();
    const resultsImportController = useResultsImportController();
    const ageGroupService = new AgeGroupService();
    const state = reactive<IResultsImportEventState>({
      sourceEventGroupSummary: {
        ...compEventService.factoryEventGroupSummary(),
        security: { clubs: [], counties: [], regions: [] },
        genders: [],
        eventName: "",
        ageGroup: ageGroupService.factoryGetAgeGroup(),
        isTeam: false,
      },
      targetEventGroupSummary: {
        ...compEventService.factoryEventGroupSummary(),
        security: { clubs: [], counties: [], regions: [] },
        genders: [],
        eventName: "",
        ageGroup: ageGroupService.factoryGetAgeGroup(),
        isTeam: false,
      },
      showTopXRows: 3,
    });

    const expanded = ref(false);
    const hasEntries = ref(false);

    //  Get best guesses showing by default, can toggle to ALL events.
    const showSourceBestGuesses = ref(true);
    const showTargetBestGuesses = ref(true);

    const sourceBestGuessResultsImportEventGroupSummary =
      ref<IBestGuessResultsImportEventGroupSummary>(
        ResultsImportService.factoryBestGuessResultsImportEventGroupSummary()
      );
    const targetBestGuessResultsImportEventGroupSummary =
      ref<IBestGuessResultsImportEventGroupSummary>(
        ResultsImportService.factoryBestGuessResultsImportEventGroupSummary()
      );

    const cheatLazyRefreshIsVisible = ref(true);

    const eventTitle = computed(() => {
      return (
        "[" +
        props.timeTronicsEvent.seqno +
        ":" +
        props.timeTronicsEvent.eventtype.id +
        "]" +
        ": " +
        props.timeTronicsEvent.name
      );
    });

    const hasFinalRounds = computed(() => {
      return ResultsImportService.hasFinalRounds(props.timeTronicsEvent);
    });

    const isFieldEvent = computed(() => {
      return ResultsImportService.isFieldEvent(props.timeTronicsEvent);
    });

    const ageGroupsCount = computed(() => {
      return props.timeTronicsEvent.categories.length;
    });

    const targetEntries = computed(() => {
      // return ResultsImportService.getTimeTronicsEventEntries(
      //   resultsImportController.importServiceController.state,
      //   props.timeTronicsEvent,
      //   "target"
      // );: Record<EventGroupIdString, IR4sAthleteEntry[]>
      let entries: IR4sAthleteEntry[] = [];
      if (state.targetEventGroupSummary.id === 0) {
        return entries;
      }
      if (
        resultsImportController.state.targetComp.entries[
          state.targetEventGroupSummary.id
        ]
      ) {
        entries =
          resultsImportController.state.targetComp.entries[
            state.targetEventGroupSummary.id
          ];
      }
      return entries ? entries : [];
    });

    const hasAnyTargetEntries = computed(() => {
      //  @ts-ignore
      return targetEntries.value.length > 0;
    });

    watch(
      () => resultsImportController.state.targetComp.entries,
      (newValue: any) => {
        if (newValue[state.targetEventGroupSummary.id]) {
          hasEntries.value = true;
        }
      }
    );

    // const getTargetEventGroupSummary =
    //   computed<IResultsImportEventGroupSummary | null>(() => {
    //     return resultsImportController.importServiceController.getResultsImportEventGroupSummaryFor(
    //       props.timeTronicsEvent,
    //       "target"
    //     );
    //   });

    const getTargetEventText = computed<string>(() => {
      const eventGroupSummary: IResultsImportEventGroupSummary | null =
        state.targetEventGroupSummary;
      return eventGroupSummary
        ? (configController.isAdmin ? eventGroupSummary.id + ": " : " ") +
            eventGroupSummary.name
        : "";
    });

    const getSourceEventText = computed<string>(() => {
      const eventGroupSummary: IResultsImportEventGroupSummary | null =
        state.sourceEventGroupSummary;
      return eventGroupSummary && eventGroupSummary.id > 0
        ? (configController.isAdmin ? eventGroupSummary.id + ": " : " ") +
            eventGroupSummary.name
        : "";
    });

    setBestGuessEventGroup();

    /**
     * N.B.  You HAVE to have props.timeTronicsEventList to be able to run this.
     */
    function getResults(): Promise<void> {
      const output: IResultsImportEventOutput = {
        timeTronicsEvent: simpleClone(props.timeTronicsEvent),
        sourceEventGroupSummary: state.sourceEventGroupSummary,
        targetEventGroupSummary: state.targetEventGroupSummary,
        timeTronicsEventList: props.timeTronicsEventList!,
        showTopXRows: state.showTopXRows,
      };

      return resultsImportController.importServiceController.getDataForEventList(
        output
      );
    }

    function doSubmit(
      mapFlattenedParticipations: MapFlattenedParticipations
    ): Promise<void> {
      console.log("ResultsImportEvent.doSubmit", mapFlattenedParticipations);
      return resultsImportController.importServiceController
        .doSubmitEntries(
          props.timeTronicsEvent,
          state.targetEventGroupSummary,
          mapFlattenedParticipations
        )
        .then(() => {
          //pass in this payload so we can do this...

          // const output: IResultsImportEventOutput = {
          //   timeTronicsEvent: simpleClone(props.timeTronicsEvent),
          //   sourceEventGroupSummary: state.sourceEventGroupSummary,
          //   targetEventGroupSummary: state.targetEventGroupSummary,
          //   timeTronicsEventList: props.timeTronicsEventList!,
          //   showTopXRows: state.showTopXRows,
          // };
          //
          // importServiceController.getDataForEventList(output);

          //  To get the part'ns to go green
          cheatLazyRefreshIsVisible.value = false;
          return getResults().then(() => {
            cheatLazyRefreshIsVisible.value = true;
          });
        });
    }

    const gotTimeTronicsEventListData = computed(() => {
      return !!(
        props.timeTronicsEventList && props.timeTronicsEventList.id > 0
      );
    });

    function getOwners(timeTronicsHeatList: ITimeTronicsHeatList) {
      console.log("ResultsImportEvent.getOwners", timeTronicsHeatList);
      const eventGroupIds = [state.sourceEventGroupSummary.id];
      resultsImportController.importServiceController.getParticipantOwners(
        timeTronicsHeatList,
        eventGroupIds
      );
    }

    function getOwnersForParticipants(
      flattenedParticipations: IFlattenedParticipation[]
    ) {
      console.log("ResultsImportEvent.getOwners", flattenedParticipations);
      resultsImportController.importServiceController.getOwnersForParticipants(
        flattenedParticipations,
        state.sourceEventGroupSummary.id
      );
    }

    const sourceSecurity = computed(() => {
      return ResultsImportService.getEventGroupSecuritySummaryText(
        state.sourceEventGroupSummary
      );
    });

    const targetSecurity = computed(() => {
      return ResultsImportService.getEventGroupSecuritySummaryText(
        state.targetEventGroupSummary
      );
    });

    function setBestGuessEventGroup() {
      // const res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
      //   props.targetEventGroupSummaries,
      //   props.timeTronicsEvent
      // );
      // if (res.eventGroupMatches.length === 1) {
      //   const bestEvtGroup = res.eventGroupMatches[0]
      //   props.targetEventGroupSummaries.forEach((evt)=>{
      //     if (evt.id === bestEvtGroup.id) {
      //       state.targetEventGroupSummary = evt;
      //     }
      //   })
      // }

      //  User has to have a target
      setBestGuessEventGroupForWhich("target");

      if (props.sourceEventGroupSummaries.length > 0) {
        setBestGuessEventGroupForWhich("source");
      }
    }

    function setBestGuessEventGroupForWhich(which: "source" | "target") {
      const eventGroupSummaries =
        which === "source"
          ? props.sourceEventGroupSummaries
          : props.targetEventGroupSummaries;

      const res = ResultsImportService.bestGuessResultsImportEventGroupSummary(
        eventGroupSummaries,
        props.timeTronicsEvent
      );

      if (which === "source") {
        sourceBestGuessResultsImportEventGroupSummary.value = res;
      } else {
        targetBestGuessResultsImportEventGroupSummary.value = res;
      }

      if (res.eventGroupMatches.length === 1) {
        const bestEvtGroup = res.eventGroupMatches[0];
        eventGroupSummaries.forEach((evt) => {
          if (evt.id === bestEvtGroup.id) {
            which === "source"
              ? (state.sourceEventGroupSummary = evt)
              : (state.targetEventGroupSummary = evt);
          }
        });
      }
    }

    function setSource(evt: IResultsImportEventGroupSummary) {
      state.sourceEventGroupSummary = simpleClone(evt);
      if (!expanded.value) {
        //  User needs to have selected a target
        return;
      }
      getResults();
      expanded.value = true;
    }

    function setTarget(evt: IResultsImportEventGroupSummary) {
      state.targetEventGroupSummary = simpleClone(evt);
      getResults();
      expanded.value = true;
    }

    function expandSection() {
      expanded.value = !expanded.value;
      const outputResultsImportEventOnExpanded: IOutputResultsImportEventOnExpanded =
        {
          isExpanded: expanded.value,
          timeTronicsEvent: simpleClone(props.timeTronicsEvent),
        };
      context.emit("onExpanded", outputResultsImportEventOnExpanded);
      if (expanded.value) {
        const source =
          resultsImportController.importServiceController.getResultsImportEventGroupSummaryFor(
            props.timeTronicsEvent,
            "source"
          );
        if (source) {
          state.sourceEventGroupSummary = simpleClone(source);
        }

        const target =
          resultsImportController.importServiceController.getResultsImportEventGroupSummaryFor(
            props.timeTronicsEvent,
            "target"
          );

        if (target) {
          state.targetEventGroupSummary = simpleClone(target);
        }

        getResults();
      }
    }

    const getSourceBestGuessMessage = computed(() => {
      return getBestGuessMessage("source");
    });

    const getTargetBestGuessMessage = computed(() => {
      return getBestGuessMessage("target");
    });

    function getBestGuessMessage(which: "source" | "target") {
      const best =
        which === "source"
          ? sourceBestGuessResultsImportEventGroupSummary
          : targetBestGuessResultsImportEventGroupSummary;

      const matches = best.value.closeMatches.length;
      const message =
        matches +
        " match" +
        (matches === 1 ? "" : "es") +
        (matches > 1
          ? ": " +
            best.value.closeMatches
              .map((evtSummary) => {
                return evtSummary.eventName;
              })
              .join(", ")
          : "");

      return message;
    }

    function toggleShowBestGuesses(which: "source" | "target") {
      if (which === "source") {
        showSourceBestGuesses.value = !showSourceBestGuesses.value;
      } else {
        showTargetBestGuesses.value = !showTargetBestGuesses.value;
      }
    }

    const getSourceEventSummaries = computed(() => {
      return getEventSummaries("source");
    });

    const getTargetEventSummaries = computed(() => {
      return getEventSummaries("target");
    });

    function getEventSummaries(which: "source" | "target") {
      //  boolean flaf to
      const showBestGuesses =
        which === "source" ? showSourceBestGuesses : showTargetBestGuesses;

      const allEventGroupSummaries =
        which === "source"
          ? props.sourceEventGroupSummaries
          : props.targetEventGroupSummaries;

      const eventGroupMatches =
        which === "source"
          ? sourceBestGuessResultsImportEventGroupSummary.value.closeMatches
          : targetBestGuessResultsImportEventGroupSummary.value.closeMatches;

      if (eventGroupMatches.length === 0) {
        return allEventGroupSummaries;
      }

      return showBestGuesses.value && eventGroupMatches.length > 0
        ? eventGroupMatches
        : allEventGroupSummaries;
    }

    return {
      state,
      eventTitle,
      hasFinalRounds,
      isFieldEvent,
      ageGroupsCount,
      getResults,
      gotTimeTronicsEventListData,
      getOwners,
      getOwnersForParticipants,
      sourceSecurity,
      targetSecurity,
      setSource,
      setTarget,
      expanded,
      hasEntries,
      targetEntries,
      hasAnyTargetEntries,
      getTargetEventText,
      getSourceEventText,
      expandSection,
      doSubmit,
      sourceBestGuessResultsImportEventGroupSummary,
      targetBestGuessResultsImportEventGroupSummary,
      getSourceBestGuessMessage,
      getTargetBestGuessMessage,
      configController,
      cheatLazyRefreshIsVisible,
      sourceShowBestGuesses: showSourceBestGuesses,
      targetShowBestGuesses: showTargetBestGuesses,
      toggleShowBestGuesses,
      getSourceEventSummaries,
      getTargetEventSummaries,
    };
  },
});
</script>
