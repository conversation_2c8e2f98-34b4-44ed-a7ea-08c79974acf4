import { IResultAthlete } from "./results-models";
import {
  IR4sScheduledEvent,
  IScheduleTableRow,
  R4SEventType,
} from "../scoreboard/rs4/rs4-scoreboard-models";
import {
  isNumeric,
  simpleClone,
  sortArray,
} from "../../common/common-service-utils";

export function sortEntries(
  resultAthletes: IResultAthlete[],
  eventType?: R4SEventType
): IResultAthlete[] {
  const splitIntoWithAndWithOutScore = resultAthletes.reduce(
    (accum, result) => {
      if (result.position > 0) {
        accum.withScore.push(result);
      } else {
        accum.noScore.push(result);
      }

      return accum;
    },
    {
      withScore: [] as IResultAthlete[],
      noScore: [] as IResultAthlete[],
    }
  );
  return [
    ...splitIntoWithAndWithOutScore.withScore.sort(function (a, b) {
      //  Track: lowest time is better, Field: highest score is better..
      // return eventType === 'T' ?
      //     b.results.currentPosition - a.results.currentPosition :
      //     a.results.currentPosition - b.results.currentPosition;
      return a.position - b.position;
    }),
    ...splitIntoWithAndWithOutScore.noScore,
  ];
}

export function getBibNo(result: IResultAthlete): string {
  return result.bibText && result.bibText.length > 0
    ? result.bibText
    : result.bibNo.toString();
}

/**
 * put current position zero at bottom
 * @param entries
 */
export function sortResults(
  resultAthletes: IResultAthlete[],
  eventType: R4SEventType = "T"
): IResultAthlete[] {
  const splitIntoWithAndWithOutScore = resultAthletes.reduce(
    (accum, result) => {
      const resultInternal = simpleClone(result);
      const hasScore =
        resultInternal.scoreValue && isNumeric(resultInternal.scoreValue);

      //  @ts-ignore
      resultInternal.hasScore = hasScore;

      //  @ts-ignore
      resultInternal.x = "1";

      if (hasScore) {
        const realValue = Number(resultInternal.scoreValue);
        resultInternal.score = realValue;
        accum.withScore.push(resultInternal);
      } else {
        accum.noScore.push(resultInternal);
      }

      return accum;
    },
    {
      withScore: [] as IResultAthlete[],
      noScore: [] as IResultAthlete[],
    }
  );

  const withScoresSorted = sortArray(
    "score",
    splitIntoWithAndWithOutScore.withScore,
    eventType === "T" ? "ASC" : "DESC"
  ).map((ath, index) => {
    ath.position = index + 1;
    return ath;
  });

  return [...withScoresSorted, ...splitIntoWithAndWithOutScore.noScore];
}

export function getNextResultsEventPositionToShow(
  currentlySelectedPosition: number,
  scheduleTableRows: Pick<IScheduleTableRow, "hasResults">[],
  goForward: boolean = true
): number {
  const length = scheduleTableRows.length;
  let internalCounter = currentlySelectedPosition;

  for (let i = 0; i < length; i++) {
    //  if i is greater than the length, then we are at the end of the list, so start at the beginning
    if (goForward) {
      internalCounter++;
    } else {
      internalCounter--;
    }
    // internalCounter++;
    if (goForward) {
      if (internalCounter >= length) {
        internalCounter = 0;
      }
    } else {
      if (internalCounter <= 0) {
        internalCounter = length - 1;
      }
    }

    if (scheduleTableRows[internalCounter].hasResults) {
      return internalCounter;
    }
  }

  //  If cycling and each event has no results, return 0
  return 0;
}

export function hasAnyResults(scheduledEvent: IR4sScheduledEvent[]): boolean {
  return scheduledEvent.reduce<boolean>((accum, schedule) => {
    return accum || schedule.results;
  }, false);
}
