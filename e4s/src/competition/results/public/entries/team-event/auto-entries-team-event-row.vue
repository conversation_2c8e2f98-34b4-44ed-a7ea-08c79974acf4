<template>
  <tr>
    <td class="auto-entries-event-row--center">
      <span v-text="teamEntry.gender"></span>
    </td>
    <td class="result-entries--centre">
      <span v-text="teamEntry.ageGroup.name"></span>
    </td>
    <td>
      <span v-text="teamEntry.teamName"></span>
    </td>
    <td>
      <div v-text="teamEntry.entity.teamEntity"></div>
    </td>
    <td>
      <div v-text="teamEntry.user.name"></div>
    </td>
    <td>
      <div
        v-if="teamEntry.entryOptions.autoEntries.targetEventGroup.id > 0"
        v-text="teamEntry.entryOptions.autoEntries.targetEventGroup.name"
        :title="
          teamEntry.entryOptions.autoEntries.targetEventGroup.id +
          ': ' +
          teamEntry.entryOptions.autoEntries.targetEventGroup.name
        "
      ></div>
    </td>
    <td class="auto-entries-event-row--buttons-cell" v-if="showQualify">
      <button
        :disabled="!isTargetEventGroupSelected"
        v-if="!isTeamAlreadyInTargetEvent"
        class="
          e4s-button e4s-button--green e4s-button--height-25
          auto-entries-event-row--button
          e4s-button--pad
        "
        v-on:click="setTargetEvent(teamEntry)"
      >
        Qualify
      </button>
      <button
        v-if="canShowRemoveButton"
        class="
          e4s-button e4s-button--soft-red e4s-button--height-25
          auto-entries-event-row--button
          e4s-button--pad
        "
        v-on:click="removeTargetEvent(teamEntry)"
      >
        Remove
      </button>
      <span v-if="hasEntryBeenPaid">Paid</span>
    </td>
  </tr>
</template>

<script lang="ts">
import * as R from "ramda";
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";

import {
  IR4sScheduledEvent,
  IR4sTeamEntry,
} from "../../../../scoreboard/rs4/rs4-scoreboard-models";
import { ResultsService } from "../../../results-service";
import { AutoEntriesService } from "../auto-entries-service";

const resultsService: ResultsService = new ResultsService();
const autoEntriesService = new AutoEntriesService();

export default defineComponent({
  name: "auto-entries-team-event-row",
  components: {},
  props: {
    teamEntry: {
      type: Object as PropType<IR4sTeamEntry>,
      default: () => {
        return resultsService.factoryR4sTeamEntry();
      },
    },
    showQualify: {
      type: Boolean,
      default: false,
    },
    scheduledEventSelected: {
      type: Object as PropType<IR4sScheduledEvent>,
      default: () => {
        return resultsService.factoryR4sScheduledEvent();
      },
    },
  },
  setup(
    props: {
      teamEntry: IR4sTeamEntry;
      showQualify: boolean;
      scheduledEventSelected: IR4sScheduledEvent;
    },
    context: SetupContext
  ) {
    function setTargetEvent() {
      context.emit("setTargetEvent", R.clone(props.teamEntry));
    }

    function removeTargetEvent() {
      context.emit("removeTargetEvent", R.clone(props.teamEntry));
    }

    const isTargetEventGroupSelected = computed(() => {
      return props.scheduledEventSelected.id > 0;
    });

    const isTeamAlreadyInTargetEvent = computed(() => {
      return autoEntriesService.isAlreadyInTargetEvent(props.teamEntry);
    });


    const canShowRemoveButton = computed(() => {
      return autoEntriesService.canShowRemoveButton(props.teamEntry);
    });

    const hasEntryBeenPaid = computed( () => {
      return autoEntriesService.hasEntryBeenPaid(props.teamEntry);
    });

    return {
      setTargetEvent,
      removeTargetEvent,
      isTargetEventGroupSelected,
      isTeamAlreadyInTargetEvent,
      canShowRemoveButton,
      hasEntryBeenPaid
    };
  },
});
</script>

<style>
.auto-entries-event-row--buttons-cell {
  border-left: 1px solid lightgrey;
  text-align: center;
}

.auto-entries-event-row--center {
  text-align: center;
}

.auto-entries-event-row--button {
  margin: 2px 0;
}
</style>
