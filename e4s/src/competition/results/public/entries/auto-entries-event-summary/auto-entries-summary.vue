<template>
  <span>
    <AutoEntriesEventSummary
      class="auto-entries-event--summary"
      v-for="(autoEntriesEventSummary) in summary"
      :key="autoEntriesEventSummary.targetId"
      :auto-entries-event-summary="autoEntriesEventSummary"
    />
  </span>
</template>

<script lang="ts">
import {computed, defineComponent, PropType, SetupContext} from "@vue/composition-api";
import {IR4sBaseEntry} from "../../../../scoreboard/rs4/rs4-scoreboard-models";
import { AutoEntriesService } from "../auto-entries-service";
import AutoEntriesEventSummary from "./auto-entries-event-summary.vue";

const autoEntriesService: AutoEntriesService = new AutoEntriesService();

export default defineComponent({
  name: "auto-entries-summary",
  components: {AutoEntriesEventSummary},
  props: {
    entries: {
      type: Array as PropType<IR4sBaseEntry[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(props: {entries: IR4sBaseEntry[]}, context: SetupContext) {
    const summary = computed( () => {
      return autoEntriesService.getTargetEventSummary(props.entries);
    })

    return {
      summary
    };
  },
});
</script>

<style>
.auto-entries-event--summary {
  margin-left: 10px;
}
</style>
