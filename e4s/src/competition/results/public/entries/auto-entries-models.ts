export interface IAutoEntrySubmit {
  sourceEntryId: number;
  targetEventGroupId: number;
}

export interface IAutoEntriesEventSummary {
  targetId: number;
  eventGroup: string;
  count: number;
  paidCount: number;
}

export type AutoEntriesSortBase = "GENDER" | "AGE" | "USER" | "TARGET_EVENT";
export type AutoEntriesSortBy = AutoEntriesSortBase | "FIRST_NAME" | "CLUB";
export type AutoEntriesTeamSortBy = AutoEntriesSortBase | "TEAM_NAME" | "ENTITY";
