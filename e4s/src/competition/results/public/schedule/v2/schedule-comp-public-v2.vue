<template>
  <div class="e4s-flex-column e4s-full-width e4s-gap--standard">
    <div
      class="e4s-flex-row e4s-justify-flex-space-between e4s-flex-center-yy"
      v-if="showSection === sections.SCHEDULE"
    >
      <div class="e4s-header--500">
        <div v-if="scheduleDates.length === 1">
          <span v-text="getDate"></span>
        </div>

        <div v-if="scheduleDates.length > 1">
          <span>Dates:</span>
          <span v-for="startDate in scheduleDates" :key="startDate.iso">
            <a
              class="schedule-comp-public-v2--comp-date-link"
              :class="
                isDateSelected(startDate)
                  ? 'schedule-comp-public-v2--comp-date-link-selected'
                  : ''
              "
              href="#"
              v-on:click.prevent="selectedScheduleByDate(startDate)"
            >
              <span v-text="startDate.display"></span>
            </a>
          </span>
        </div>
      </div>

      <div class="e4s-flex-row e4s-flex-center-xx e4s-gap--standard">
        <a
          href="#"
          v-on:click.prevent="clearFilter"
          v-if="defaultFilterValue.length > 0"
        >
          Clear
        </a>
        <FieldTextV2
          class="e4s-input-field--150"
          :value="defaultFilterValue"
          @keyUp="doFilter"
        />
        <!--        <FormGenericInputTextV2-->
        <!--          class="e4s-input-field&#45;&#45;150"-->
        <!--          :show-label="false"-->
        <!--          :value="defaultFilterValue"-->
        <!--          v-on:onKeyUp="doFilter"-->
        <!--          place-holder="Event search..."-->
        <!--        />-->
      </div>
    </div>

    <div class="e4s-flex-row">
      <ButtonGenericV2
        text="Detailed Entries"
        @click="goToDetailedEntries"
        class="e4s-button--auto"
      />
    </div>

    <!--https://codepen.io/vasansr/pen/PZOJXr-->
    <div
      v-show="showSection === sections.SCHEDULE"
      class="e4s-flex-column e4s-full-width e4s-gap--standard"
    >
      <table>
        <tr class="e4s-header--500">
          <td>Time</td>
          <td>Event Name</td>
          <td>
            <div class="e4s-flex-row e4s-justify-flex-space-between">
              <span v-if="getHaveAnyResults">Results</span>
              <span class="e4s-flex-row--end">Entries</span>
            </div>
          </td>
        </tr>

        <tr class="" v-for="(scheduleTableRow, index) in scheduleTableRows">
          <td class="">
            <div v-text="getEventTimeForRow(scheduleTableRow)"></div>
          </td>

          <td class="">
            <span v-text="scheduleTableRow.eventName"></span>
          </td>
          <td class="">
            <div class="e4s-flex-row e4s-justify-flex-space-between">
              <PrimaryLink
                link-text="Results"
                v-if="scheduleTableRow.hasResults"
                @onClick="displayRankings(scheduleTableRow.eventGroupId)"
              />

              <PrimaryLink
                class="e4s-flex-row--end"
                :link-text="scheduleTableRow.entries + ' Entries'"
                v-if="scheduleTableRow.counts.entries > 0"
                @onClick="showEntries(scheduleTableRow.eventGroupId)"
              />
            </div>
          </td>
        </tr>
      </table>
    </div>

    <!--Entries-->
    <div v-if="showSection === sections.ENTRIES">
      <!--      <div class="e4s-vertical-spacer&#45;&#45;large"></div>-->

      <ResultEntriesV2
        back-button-text="Back to Schedule"
        :r4s-comp-schedule="r4sCompSchedule"
        :r4s-comp-schedule-target="r4sCompScheduleTarget"
        :schedule-table-row="scheduleTableRowDisplay"
        v-on:close="showSection = sections.SCHEDULE"
      />
    </div>
    <!--/Entries-->

    <div v-if="showSection === sections.RANKING">
      <!--      <div-->
      <!--        class=""-->
      <!--        v-if="isLoadingLatestScore || showTheseEntries.length === 0"-->
      <!--      >-->
      <!--        <div v-if="!isLoadingLatestScore && showTheseEntries.length === 0" class="e4s-flex-row">-->
      <!--          <span>Nothing available.</span>-->

      <!--          <PrimaryLink-->
      <!--            class="e4s-flex-row&#45;&#45;end"-->
      <!--            link-text="Show Schedule"-->
      <!--            @onClick="showSection = sections.SCHEDULE"-->
      <!--          />-->
      <!--          -->
      <!--        </div>-->
      <!--      </div>-->

      <div
        v-if="!resultRowsFromLatestScores.resultRows.length === 0"
        class="e4s-flex-row"
      >
        <span>Nothing available.</span>

        <PrimaryLink
          class="e4s-flex-row--end"
          link-text="Show Schedule"
          @onClick="showSection = sections.SCHEDULE"
        />
      </div>

      <div v-if="resultRowsFromLatestScores.resultRows.length > 0">
        <!--        <ResultsPublicField-->
        <!--          v-if="showTheseResultRowsType !== 'T'"-->
        <!--          :event-name="showTheseEntries[0].eventGroup.groupName"-->
        <!--          :public-heat-group-meta="showThisMeta"-->
        <!--          :results="showTheseResultRows"-->
        <!--          v-on:close="showSection = sections.SCHEDULE"-->
        <!--        >-->
        <!--        </ResultsPublicField>-->
        <!--        <ResultsPublicTrack-->
        <!--          v-if="showTheseResultRowsType === 'T'"-->
        <!--          :event-name="showTheseEntries[0].eventGroup.groupName"-->
        <!--          :public-heat-group-meta="showThisMeta"-->
        <!--          :results="showTheseResultRows"-->
        <!--          v-on:close="showSection = sections.SCHEDULE"-->
        <!--        >-->
        <!--        </ResultsPublicTrack>-->
        <ResultsPublicField
          v-if="resultRowsFromLatestScores.eventType !== 'T'"
          :event-name="resultRowsFromLatestScores.eventName"
          :public-heat-group-meta="showThisMeta"
          :results="resultRowsFromLatestScores.resultRows"
          v-on:close="showSection = sections.SCHEDULE"
        >
        </ResultsPublicField>
        <ResultsPublicTrack
          v-if="resultRowsFromLatestScores.eventType === 'T'"
          :event-name="resultRowsFromLatestScores.eventName"
          :public-heat-group-meta="showThisMeta"
          :results="resultRowsFromLatestScores.resultRows"
          v-on:close="showSection = sections.SCHEDULE"
        >
        </ResultsPublicTrack>
      </div>
    </div>

    <!--    <LoadingSpinnerModal message="Loading..." :show-it="isLoading" />-->
    <LoadingSpinnerV2 v-if="isLoading || isLoadingLatestScore" />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { format, parse } from "date-fns";
import { RawLocation } from "vue-router";
import {
  IPublicHeatGroupMeta,
  IR4sCompSchedule,
  IR4sEntry,
  IR4sScheduledEvent,
  IResultRow,
  IScheduleTableRow,
  R4SEventType,
} from "../../../../scoreboard/rs4/rs4-scoreboard-models";
import {
  ILatestScoresQueryResponseType,
  ScoreboardData,
} from "../../../../scoreboard/scoreboard-data";
import { ConfigService } from "../../../../../config/config-service";
import { ScoreboardService } from "../../../../scoreboard/scoreboard-service";
import { ISimpleDateModel } from "../../../../../common/common-models";
import { handleResponseMessages } from "../../../../../common/handle-http-reponse";
import { LAUNCH_ROUTES_PATHS } from "../../../../../launch/launch-routes";
import { CONFIG } from "../../../../../common/config";
import {
  IResultsStoreState,
  RESULTS_STORE_CONST,
} from "../../../results-store";
import { CommonService } from "../../../../../common/common-service";
import { Rs4Service } from "../../../../scoreboard/rs4/rs4-service";
import { IConfigApp } from "../../../../../config/config-app-models";
import { ResultsService } from "../../../results-service";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../../config/config-store";
import ResultEntries from "../result-entries.vue";
import LoadingSpinnerModal from "../../../../../common/ui/modal/loading-spinner-modal.vue";
import InputDebounce from "../../../../../common/ui/field/input-debounce.vue";
import ResultsPublicField from "../../../../scoreboard/rs4/output/public/results/results-public-field.vue";
import ResultsPublicTrack from "../../../../scoreboard/rs4/output/public/results/results-public-track.vue";
import RankingsPublic from "../../../../scoreboard/rs4/output/public/rankings/rankings-public.vue";
import FormGenericInputTextV2 from "../../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import ResultEntriesV2 from "./result-entries-v2.vue";
import LoadingSpinnerV2 from "../../../../../common/ui/loading-spinner-v2.vue";
import PrimaryLink from "../../../../../common/ui/layoutV2/href/PrimaryLink.vue";
import {
  createResultRowsFromLatestScores,
  IResultRowsFromLatestScores,
} from "./schedule-comp-service";
import ButtonGenericV2 from "../../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FieldTextV2 from "../../../../../common/ui/layoutV2/fields/field-text-v2.vue";

const rs4Service: Rs4Service = new Rs4Service();

@Component({
  name: "schedule-comp-public-v2",
  components: {
    FieldTextV2,
    ButtonGenericV2,
    PrimaryLink,
    LoadingSpinnerV2,
    ResultEntriesV2,
    FormGenericInputTextV2,
    LoadingSpinnerModal,
    ResultEntries,
    InputDebounce,
    ResultsPublicTrack,
    ResultsPublicField,
    RankingsPublic,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapState(RESULTS_STORE_CONST.RESULTS_CONST_MODULE_NAME, {
      dateSelectedStore: (state: IResultsStoreState) => state.dateSelected,
    }),
  },
})
export default class ScheduleCompPublicV2 extends Vue {
  public readonly isAdmin!: boolean;
  public readonly configApp!: IConfigApp;
  public readonly dateSelectedStore!: ISimpleDateModel;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: 0,
  })
  public readonly eventId: number;

  public rs4Service: Rs4Service = rs4Service;
  public commonService: CommonService = new CommonService();
  public scoreboardData: ScoreboardData = new ScoreboardData();
  public configService: ConfigService = new ConfigService();
  public resultsService: ResultsService = new ResultsService();
  public scoreboardService: ScoreboardService = new ScoreboardService();

  public r4sCompScheduleTarget: IR4sCompSchedule =
    this.rs4Service.factoryR4sCompSchedule();

  public sections = {
    SCHEDULE: "SCHEDULE",
    RANKING: "RANKING",
    ENTRIES: "ENTRIES",
  };
  public showSection: string = this.sections.SCHEDULE;

  public scheduleDates: ISimpleDateModel[] = [];
  public scheduleDateDisplay: ISimpleDateModel = {
    iso: "",
    display: "",
  };
  public scheduleTableRowDisplay: IScheduleTableRow =
    this.scoreboardService.factoryScheduleTableRow();

  public scheduleTableRows: IScheduleTableRow[] = [];

  public eventGroupIdKeyEntries: Record<string, IR4sEntry[]> = {};
  public eventGroupIdKeyMeta: Record<
    string,
    Record<number, IPublicHeatGroupMeta>
  > = {};
  public showTheseEntries: IR4sEntry[] = [];

  public latestScoresQueryResponseType: ILatestScoresQueryResponseType = {};
  public resultRowsFromLatestScores: IResultRowsFromLatestScores = {
    resultRows: [],
    eventType: "T",
    eventName: "",
  };

  public showTheseResultRows: IResultRow[] = [];
  public showTheseResultRowsType: R4SEventType | "" = "";

  public showThisMeta: Record<number, IPublicHeatGroupMeta> =
    this.rs4Service.factoryPublicEventGroupMeta();

  public isLoadingLatestScore = false;
  public isLoading = false;

  public defaultFilterValue = "";

  public created() {
    const scheduleDates = this.getCompResultDates();
    const scheduleDatesIso = scheduleDates.map((scheduleDate) => {
      return scheduleDate.iso;
    });

    this.scheduleDates = scheduleDates;
    if (this.dateSelectedStore.iso.length > 0) {
      this.scheduleDateDisplay = this.dateSelectedStore;
    } else {
      const todayDate = format(new Date(), "YYYY-MM-DD");
      const datePosition = scheduleDatesIso.indexOf(todayDate);
      if (datePosition > -1) {
        // this.state.date = todayDate;
        this.scheduleDateDisplay = scheduleDates[datePosition];
      } else {
        // this.state.date = this.checkinSummary.dates[0];
        this.scheduleDateDisplay = this.scheduleDates[0];
      }

      // this.scheduleDateDisplay = this.scheduleDates[0];
    }

    this.createScheduleTableRows();
  }

  @Watch("r4sCompSchedule")
  public onR4sCompScheduleChanged() {
    this.createScheduleTableRows();
  }

  @Watch("eventId")
  public onEventIdChanged() {
    this.createScheduleTableRows();
  }

  //  showEntries(scheduleTableRow.eventGroupId)

  public selectedScheduleByDate(startDate: ISimpleDateModel) {
    this.scheduleDateDisplay = startDate;
    this.showSection = this.sections.SCHEDULE;

    this.$store.commit(
      RESULTS_STORE_CONST.RESULTS_CONST_MODULE_NAME +
        "/" +
        RESULTS_STORE_CONST.RESULTS_MUTATIONS_SET_DATE,
      R.clone(startDate)
    );

    this.createScheduleTableRows();
  }

  public getCompResultDates(): ISimpleDateModel[] {
    return this.commonService.sortArray(
      "iso",
      this.commonService.uniqueArrayById(
        this.r4sCompSchedule.schedule.map((schedule) => {
          return {
            iso: schedule.startDate.split("T")[0],
            display: format(parse(schedule.startDate), "Do MMM"),
          };
        }),
        "iso"
      )
    );
  }

  public isDateSelected(simpleDateModel: ISimpleDateModel): boolean {
    return simpleDateModel.iso === this.scheduleDateDisplay.iso;
  }

  public sortEvents(
    r4sScheduledEvents: IR4sScheduledEvent[]
  ): IR4sScheduledEvent[] {
    return this.commonService.sortArray("startDate", r4sScheduledEvents);
  }

  public createScheduleTableRows(): void {
    let scheduleRows: IScheduleTableRow[] = this.r4sCompSchedule.schedule
      .map((scheduledEvent) => {
        return this.rs4Service.mapScheduleEventToScheduleTableRow(
          scheduledEvent
        );
      })
      .filter((scheduleTableRow) => {
        return (
          scheduleTableRow.startTime.split("T")[0] ===
          this.scheduleDateDisplay.iso
        );
      });

    if (this.defaultFilterValue.length > 0) {
      const searchTerm = this.defaultFilterValue.toLowerCase();
      scheduleRows = scheduleRows.filter((scheduleTableRow) => {
        return (
          scheduleTableRow.eventName.toLowerCase().indexOf(searchTerm) > -1
        );
      });
    }
    // const trackRows: IScheduleTableRow[] = Object.keys(this.trackPayloads).map( (key) => {
    //     return this.rs4Service.mapTrackToScheduleTableRow(this.trackPayloads[key]);
    // })

    this.scheduleTableRows = this.commonService.sortArray(
      "startTime",
      scheduleRows
    );
  }

  public get hasResultsPermissionForComp() {
    return this.configService.hasResultsPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.r4sCompSchedule.compId
    );
  }

  public get getLogoPath() {
    return CONFIG.E4S_HOST + this.r4sCompSchedule.logo;
  }

  public get getDate() {
    return format(parse(this.r4sCompSchedule.date), "Do MMM YYYY");
  }

  public getEventTime(r4sScheduledEvent: IR4sScheduledEvent) {
    return format(parse(r4sScheduledEvent.startDate), "HH:mm");
  }

  public getEventTimeForRow(scheduleTableRow: IScheduleTableRow) {
    const time = format(parse(scheduleTableRow.startTime), "HH:mm");
    return time === "00:00" ? "TBC" : time;
  }

  public hasResultsWaitingText(scheduleTableRow: IScheduleTableRow): string {
    return scheduleTableRow.waiting ? " ( Awaiting seeding )" : "";
  }

  public getLatestScoreData(
    compId: number,
    eventGroupIds: number[]
  ): Promise<boolean> {
    if (compId === 0 || eventGroupIds.length === 0) {
      return Promise.resolve(false);
    }
    this.isLoadingLatestScore = true;

    const prom = this.scoreboardData.getLatestScoresNew(compId, eventGroupIds);
    handleResponseMessages(prom);
    return prom
      .then((resp) => {
        if (resp.errNo > 0) {
          return false;
        }

        this.latestScoresQueryResponseType = resp.data;

        // this.addEntriesByEventIds(resp.data);
        // if (resp.meta) {
        //   this.addMetaByEventIds(resp.meta);
        // }
        return true;
      })
      .finally(() => {
        this.isLoadingLatestScore = false;
      });
  }

  public addEntriesByAthlete(entries: Record<number, IR4sEntry>): void {
    if (entries && Object.keys(entries).length > 0) {
      const entriesArray = this.commonService.convertObjectToArray(entries);
      if (entriesArray.length === 0) {
        return;
      }
      const eventGroupId: string = entriesArray[0].eventGroup.id.toString();
      this.eventGroupIdKeyEntries[eventGroupId] = entriesArray;
    }
  }

  public addEntriesByEventIds(
    entriesByEventIds: Record<number, Record<number, IR4sEntry>>
  ) {
    Object.values(entriesByEventIds).forEach((entriesByAthleteId) => {
      this.addEntriesByAthlete(entriesByAthleteId);
    });
  }

  public addMetaByEventIds(
    metasByEventKeyMap: Record<number, Record<number, IPublicHeatGroupMeta>>
  ) {
    const keys = Object.keys(metasByEventKeyMap);
    //  @ts-ignore
    keys.forEach((eventId: number) => {
      const key = eventId.toString();
      this.eventGroupIdKeyMeta[key] = metasByEventKeyMap[eventId];
    });
  }

  public displayRankings(eventGroupId: number) {
    this.showTheseEntries = [];
    this.showTheseResultRows = [];
    //  Get from the cache.  Any "new" scores will come up the socket and get loaded to cache.
    this.showSection = this.sections.RANKING;
    // if (this.eventGroupIdKeyEntries[eventGroupId.toString()]) {
    //     this.getFromCache(eventGroupId);
    //     return;
    // }
    this.getLatestScoreData(this.r4sCompSchedule.compId, [eventGroupId]).then(
      () => {
        this.getFromCache(eventGroupId);
      }
    );
  }

  public getFromCache(eventGroupId: number) {
    const resultRowsData = createResultRowsFromLatestScores(
      eventGroupId.toString(),
      this.latestScoresQueryResponseType
    );

    this.resultRowsFromLatestScores = resultRowsData;
    this.showTheseResultRowsType = resultRowsData.eventType === "T" ? "T" : "D";
    // this.showTheseResultRows = resultRowsData.resultRows;
    // this.showTheseResultRowsType = resultRowsData.eventType === "T" ? "T" : "D";

    // const eventType: R4SEventType = this.r4sCompSchedule.schedule.reduce(
    //   (accum, schedule) => {
    //     if (schedule.id.toString() === eventGroupId.toString()) {
    //       accum = schedule.type;
    //     }
    //     return accum;
    //   },
    //   "T" as R4SEventType
    // );
    //
    // const r4sEntries = this.rs4Service.sortAndFilterResultEntries(
    //   R.clone(this.eventGroupIdKeyEntries[eventGroupId.toString()]),
    //   eventType
    // );
    // this.showTheseEntries = r4sEntries;
    //
    // if (eventType === "T") {
    //   this.showTheseResultRows =
    //     this.rs4Service.mapR4sEntriesToResultRowTracks(r4sEntries);
    // } else {
    //   this.showTheseResultRows = r4sEntries.map((entry) => {
    //     return this.rs4Service.mapR4sEntryToIResultRow(entry);
    //   });
    // }
    // this.showThisMeta = this.rs4Service.factoryPublicEventGroupMeta();
    // if (this.eventGroupIdKeyMeta[eventGroupId.toString()]) {
    //   this.showThisMeta = R.clone(this.eventGroupIdKeyMeta[eventGroupId]);
    // }
    // this.showTheseResultRowsType = eventType;
  }

  public exportToPof10() {
    window.open(
      CONFIG.E4S_HOST + "/" + this.r4sCompSchedule.compId + "/pof10",
      "Pof10-export"
    );
  }

  public goToScheduleQr() {
    let location: RawLocation;
    location = {
      path:
        "/" +
        LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC_QR +
        "/" +
        this.r4sCompSchedule.compId,
    };
    this.$router.push(location);
  }

  public doFilter(searchTerm: string) {
    this.defaultFilterValue = searchTerm;
    this.createScheduleTableRows();
  }

  public clearFilter() {
    this.doFilter("");
  }

  public showEntries(eventGroupId: number) {
    const scheduleTableRow: IScheduleTableRow | null =
      this.scheduleTableRows.reduce<IScheduleTableRow | null>((accum, row) => {
        if (row.eventGroupId === eventGroupId) {
          accum = row;
        }
        return accum;
      }, null);

    if (this.getIsThisAFeederComp && this.r4sCompScheduleTarget.compId === 0) {
      //  TODO, check the "true"...massive perf hit.
      const prom = this.scoreboardData.getCompSchedule(
        this.r4sCompSchedule.autoEntries.selectedTargetComp.id,
        true
      );
      handleResponseMessages(prom);
      prom.then((resp) => {
        if (resp.errNo === 0) {
          this.r4sCompScheduleTarget = resp.data;
        }
      });
    }

    if (scheduleTableRow) {
      this.isLoading = true;
      this.scoreboardData
        .getEntriesForEventGroup(
          this.r4sCompSchedule.compId,
          scheduleTableRow.eventGroupId
        )
        .then((response) => {
          if (response.errNo === 0) {
            const scheduleTableRowDisplay = R.clone(scheduleTableRow);
            scheduleTableRowDisplay.athleteEntries = response.data;
            this.scheduleTableRowDisplay = scheduleTableRowDisplay;
            this.showSection = this.sections.ENTRIES;
          }
        })
        .finally(() => {
          this.isLoading = false;
        });
    }
  }

  public get getIsThisAFeederComp() {
    return this.rs4Service.isFeederComp(this.r4sCompSchedule);
  }

  public goToListFormat() {
    window.open(
      CONFIG.E4S_HOST + "/" + this.r4sCompSchedule.compId + "/resultslist",
      "_e4slistformat"
    );
  }

  public get getHaveAnyResults() {
    const schedule = this.r4sCompSchedule.schedule;

    for (let i = 0; i < schedule.length; i++) {
      if (schedule[i].results) {
        return true;
        // break; // breaks the loop after 4 iterations
      }
      console.log("getHaveAnyResults: " + i);
    }
    return false;
  }

  public goToDetailedEntries() {
    window.open(this.getAllEntriesUrl, "_e4sentries");
  }

  public get getAllEntriesUrl() {
    return CONFIG.E4S_HOST + "/" + this.r4sCompSchedule.compId + "/entries";
  }
}
</script>

<style scoped>
.schedule-comp-public-v2--comp-date-link {
  padding: 0 0.5em;
  border-right: 1px solid #c5c5c5;
}

.schedule-comp-public-v2--comp-date-link-selected {
  color: black;
}

.schedule-comp-public-v2--pending-results {
  color: darkgray;
}

.schedule-comp-public-v2--grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  grid-gap: 4px;
}
</style>
