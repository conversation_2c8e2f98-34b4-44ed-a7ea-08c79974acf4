import { IR4sCompSchedule } from "../../../../../scoreboard/rs4/rs4-scoreboard-models";

export const scheduleMockDev369: IR4sCompSchedule = {
  compId: 369,
  org: {
    id: 54,
    name: "Nuneaton Opens",
  },
  name: "Results Test for Nick",
  date: "2023-05-12",
  location: "Pingles S'tadium 2",
  club: "Nuneaton Opens",
  logo: "/resources/nuneaton_opens_logo.jpg",
  autoEntries: {
    selectedTargetComp: {
      id: 0,
      name: "",
      timeTronicsUrl: "",
    },
    targetable: {
      allowedSources: [],
      enabled: false,
    },
  },
  schedule: [
    {
      id: 5462,
      results: true,
      eventNo: 1,
      name: "T1 : 100m",
      typeNo: "T1",
      startDate: "2023-05-31T10:00:00",
      eventName: "100m",
      wind: "E",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
        },
        seeded: false,
      },
      counts: {
        entries: 0,
        maxAthletes: 0,
        maxInHeat: 0,
      },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5464,
      results: true,
      eventNo: 3,
      name: "T3 : 75m Quad",
      typeNo: "T3",
      startDate: "2023-05-12T00:00:00",
      eventName: "75m Quad",
      wind: "",
      type: "T",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
        },
        seeded: false,
      },
      counts: {
        entries: 2,
        maxAthletes: -1,
        maxInHeat: 0,
      },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5463,
      results: true,
      eventNo: 2,
      name: "M2 : Quadrathlon",
      typeNo: "M2",
      startDate: "2023-05-12T00:00:00",
      eventName: "Quadrathlon",
      wind: "",
      type: "P",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
        },
        seeded: false,
      },
      counts: {
        entries: 2,
        maxAthletes: 0,
        maxInHeat: 0,
      },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
    {
      id: 5465,
      results: true,
      eventNo: 4,
      name: "F4 : Howler Quad",
      typeNo: "F4",
      startDate: "2023-05-12T00:00:00",
      eventName: "Howler Quad",
      wind: "",
      type: "D",
      seed: {
        firstLane: 1,
        laneCount: 0,
        gender: false,
        age: false,
        type: "O",
        waiting: false,
        qualifyToEg: {
          id: 0,
          compId: 0,
          name: "",
          eventNo: 0,
        },
        seeded: false,
      },
      counts: {
        entries: 2,
        maxAthletes: 0,
        maxInHeat: 0,
      },
      entries: [],
      isTeamEvent: false,
      resultsPossible: 1,
    },
  ],
} as any as IR4sCompSchedule;
