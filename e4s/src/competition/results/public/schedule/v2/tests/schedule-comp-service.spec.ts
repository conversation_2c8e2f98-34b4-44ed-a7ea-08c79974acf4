import { latestScoresMock313 } from "./latestScoresMock313";
import {
  createResultRowFromLatestScoresQuery,
  createResultRowsFromLatestScores,
} from "../schedule-comp-service";

describe("ScheduleCompService", () => {
  test("createResultRowFromLatestScoresQuery", () => {
    const athleteResult = latestScoresMock313["4977"]["1"];

    expect(athleteResult.athlete.name).toBe("Jessica Day");

    const res = createResultRowFromLatestScoresQuery(athleteResult);
    expect(res.athlete.name).toBe("Jessica Day");
    expect(res.result).toBe("25.01");
  });

  test("createResultRowsFromLatestScores", () => {
    const res = createResultRowsFromLatestScores("4977", latestScoresMock313);

    expect(res.resultRows.length).toBe(11);

    const athleteResult = res.resultRows[2];
    expect(athleteResult.athlete.name).toBe("Matilda Abbott");
  });
});
