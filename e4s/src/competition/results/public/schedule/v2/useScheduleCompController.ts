import {handleResponseMessages} from "../../../../../common/handle-http-reponse"
import {ScoreboardData} from "../../../../scoreboard/scoreboard-data"
import {BuilderService} from "../../../../../builder/builder-service"
import {IR4sCompSchedule} from "../../../../scoreboard/rs4/rs4-scoreboard-models"
import {computed, reactive} from "@vue/composition-api"
import {Rs4Service} from "../../../../scoreboard/rs4/rs4-service"

export interface IScheduleCompControllerState {
  compSchedule: IR4sCompSchedule;
  isLoading: boolean;
  isReady: boolean;
}
const rs4Service: Rs4Service = new Rs4Service();

export function useScheduleCompController() {

  const state = reactive<IScheduleCompControllerState>({
    compSchedule: rs4Service.factoryR4sCompSchedule(),
    isLoading: false,
    isReady: false
  })

  function getSchedule(compId: number) {

    state.isLoading = true;
    const prom = new ScoreboardData().getCompSchedule(
      compId,
      false
    );
    handleResponseMessages(prom);
    return prom
      .then((resp) => {
        if (resp.errNo === 0) {
          const r4sCompSchedule = resp.data;
          if (!r4sCompSchedule.autoEntries) {
            r4sCompSchedule.autoEntries =
              new BuilderService().factoryAutoEntries();
          }
          state.compSchedule = r4sCompSchedule;
          state.isReady = true;
        }
        return;
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  const getState = computed( () => {
    return state;
  })

  return {
    getState,
    getSchedule
  }
}
