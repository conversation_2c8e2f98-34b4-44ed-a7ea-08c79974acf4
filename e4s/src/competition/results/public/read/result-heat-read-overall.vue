<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <table class="result-heat-read--table">
          <tr class="result-heat-read--header">
            <th class="result-heat-read--narrow">
              <span
                :class="
                  sortBy === 'heatNo' ? 'result-event-read--sort-by-active' : ''
                "
                >Heat</span
              >
              <!--              <a href="#" v-on:click.prevent="setSortBy('heatNo')">-->
              <!--                <span-->
              <!--                  :class="-->
              <!--                    sortBy === 'heatNo'-->
              <!--                      ? 'result-event-read&#45;&#45;sort-by-active'-->
              <!--                      : ''-->
              <!--                  "-->
              <!--                  >Heat</span-->
              <!--                >-->
              <!--                <i-->
              <!--                  class="material-icons"-->
              <!--                  v-if="sortBy === 'heatNo'"-->
              <!--                  style="vertical-align: middle"-->
              <!--                  >arrow_upward</i-->
              <!--                >-->
              <!--              </a>-->
            </th>
            <!--                        <th class="result-heat-read&#45;&#45;narrow">Pos#</th>-->
            <th class="result-heat-read--narrow">Bib</th>
            <th class="result-heat-read--athlete">Athlete</th>
            <th class="result-heat-read--club">Club</th>
            <th
              class="result-heat-read--narrow"
              v-if="resultEvent.eventGroup.type === 'T'"
            >
              Lane
            </th>

            <th class="result-heat-read--result">
              <a href="#" v-on:click.prevent="setSortBy('')">
                <span
                  :class="
                    sortBy === '' ? 'result-event-read--sort-by-active' : ''
                  "
                  >Result</span
                >
                <i
                  class="material-icons"
                  v-if="sortBy === ''"
                  style="vertical-align: middle"
                  >arrow_upward</i
                >
              </a>
            </th>

            <th class="result-heat-read--q" v-if="getShowQualify">
              <div class="right">
                Q<FieldHelp
                  title="Qualify"
                  message="Use this to toggle an athlete qualification.
                                Q=fastest, q=fastest loser"
                ></FieldHelp>
              </div>
            </th>
          </tr>

          <tr
            v-for="(resultOverall, index) in getResults"
            :key="resultOverall.id"
            :class="getRowClass(resultOverall)"
          >
            <td class="result-heat-read--narrow">
              <span v-text="resultOverall.heatName"></span>
            </td>
            <!--                        <td class="result-heat-read&#45;&#45;narrow"><span v-text="getPosition(resultOverall)"></span></td>-->
            <td class="result-heat-read--narrow">
              <span v-text="getBibNo(resultOverall)"></span>
            </td>
            <td class="result-heat-read--athlete">
              <span v-text="trimColumnText(resultOverall.athlete)"></span>
            </td>
            <td class="result-heat-read--club">
              <span v-text="trimColumnText(resultOverall.clubName)"></span>
            </td>
            <td
              class="result-heat-read--narrow"
              v-if="resultEvent.eventGroup.type === 'T'"
            >
              <span v-text="getLane(resultOverall)"></span>
            </td>
            <td class="result-heat-read--result">
              <EaAward
                :ea-level="resultOverall.eaAward"
                class="e4s-force-inline-block"
                style="width: 35px"
              />
              <span v-text="getScore(resultOverall)"></span>
              <span v-if="resultEvent.eventGroup.wind === 'A'">
                <i class="material-icons result-event-read--wind-icon">flag</i>
                <span v-text="getWind(resultOverall)"></span>
              </span>

              <!--                            <a v-if="resultAthlete.urn && resultAthlete.urn.toString().length > 0"-->
              <!--                               :href="'https://www.thepowerof10.info/athletes/profile.aspx?ukaurn=' + resultAthlete.urn"-->
              <!--                               target="e4s-po10"-->
              <!--                               class="po10-link right">-->
              <!--                                <img :src="require('../../../../images/po10.ico')"/>-->
              <!--                            </a>-->
              <PowerOfTenLink :urn="resultOverall.urn"></PowerOfTenLink>
            </td>

            <td class="result-heat-read--q" v-if="getShowQualify">
              <button
                class="e4s-button e4s-button--green e4s-button--pad right"
                v-on:click="setQ(resultOverall)"
              >
                Q
              </button>
            </td>
          </tr>
        </table>
      </div>
    </div>

    <!--        <div v-if="getShowQualify">-->
    <!--            <div class="e4s-section-padding-separator"></div>-->

    <!--            <div class="row">-->
    <!--                <div class="col s12 m12 l12">-->
    <!--                    <button-->
    <!--                        :disabled="isLoading"-->
    <!--                        class="e4s-button e4s-button&#45;&#45;green e4s-button&#45;&#45;pad right"-->
    <!--                        v-on:click="submitEvent"-->
    <!--                    >-->
    <!--                        Submit-->
    <!--                    </button>-->
    <!--                </div>-->
    <!--            </div>-->
    <!--        </div>-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
// import {format, parse} from "date-fns";
import { FormController } from "../../../../common/ui/form/form-controller/form-controller";
import {
  IResultAthlete,
  IResultEvent,
  IResultHeat,
  IResultOverall,
} from "../../results-models";
import { ValidationController } from "../../../../validation/validation-controller";
import { ResultsService } from "../../results-service";
import { CommonService } from "../../../../common/common-service";
import { POWER_OF_TEN_ATHLETE_LINK } from "../../../../common/config";
import PowerOfTenLink from "../../../../common/ui/power-of-ten-link.vue";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import { IConfigApp } from "../../../../config/config-app-models";
import { ConfigService } from "../../../../config/config-service";
import { IR4sCompSchedule } from "../../../scoreboard/rs4/rs4-scoreboard-models";
import { Rs4Service } from "../../../scoreboard/rs4/rs4-service";
import FieldHelp from "../../../../common/ui/field/field-help/field-help.vue";
import EaAward from "../../../../common/ui/ea-award/EaAward.vue";

const resultsService: ResultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();

@Component({
  name: "result-heat-read-overall",
  components: { EaAward, FieldHelp, PowerOfTenLink },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class ResultHeatReadOverall extends Vue {
  public readonly isAdmin!: boolean;
  public configApp!: IConfigApp;

  @Prop({
    default: () => {
      return resultsService.factoryResultEvent();
    },
  })
  public readonly resultEvent: IResultEvent;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: false,
  })
  public readonly isLoading: boolean;

  public configService: ConfigService = new ConfigService();
  public validationController: ValidationController =
    new ValidationController();
  public formController: FormController = new FormController({}, {});
  public resultsService = resultsService;
  public commonService: CommonService = new CommonService();
  public wind: string = "";
  public resultEventInternal: IResultEvent =
    resultsService.factoryResultEvent();

  public POWER_OF_TEN_ATHLETE_LINK = POWER_OF_TEN_ATHLETE_LINK;

  public resultOverallsInitial: IResultOverall[] = [];
  public resultOveralls: IResultOverall[] = [];

  public sortBy: keyof IResultOverall | "" = "";

  public created() {
    this.init();
  }

  @Watch("resultEvent")
  public onResultHeatChanged(newValue: IResultHeat, oldValue: IResultHeat) {
    this.init();
  }

  public init() {
    // this.formController.setSources(this.resultHeat, this.resultHeatInternal);
    this.resultEventInternal = R.clone(this.resultEvent);
    this.resultOveralls =
      this.resultsService.createResultOverallsFromResultEvent(this.resultEvent);
    this.resultOverallsInitial = R.clone(this.resultOveralls);
  }

  public trimColumnText(content: string): string {
    if (content.length > 30) {
      return content.slice(0, 31) + "...";
    }
    return content;
  }

  public getLane(result: IResultOverall) {
    return result.laneNo === 0 ? "" : result.laneNo;
  }

  public getWind(resultOverall: IResultOverall) {
    const wind = resultOverall.wind;
    if (this.commonService.isEmpty(wind)) {
      return "";
    }
    return typeof wind === "string" ? wind : wind + "m/s";
  }

  public get getResults(): IResultOverall[] {
    const splitIntoWithAndWithOutScore = this.resultOveralls.reduce(
      (accum, result) => {
        const isNullScore =
          result.score === 0 ||
          result.score.toString().length === 0 ||
          typeof result.score === "string";

        if (isNullScore) {
          accum.noScore.push(result);
        } else {
          accum.withScore.push(result);
        }

        return accum;
      },
      {
        withScore: [] as IResultOverall[],
        noScore: [] as IResultOverall[],
      }
    );

    let resultOveralls;
    // let resultOveralls = this.resultOveralls.filter(
    //   (resultOverall: IResultOverall) => {
    //     const isNullScore =
    //       resultOverall.score === 0 ||
    //       resultOverall.score.toString().length === 0;
    //     // return !(resultOverall.position === 0 && isNullScore);
    //     return !isNullScore;
    //   }
    // );

    const scoreComparator = (scoreA: number, scoreB: number) => {
      return this.resultEvent.eventGroup.type === "T"
        ? (scoreA as number) - (scoreB as number)
        : (scoreB as number) - (scoreA as number);
    };

    // resultOveralls = resultOveralls.sort((a, b): number => {
    //   //order === "ASC" ? propValueA - propValueB : propValueB - propValueA;.
    //   if (this.sortBy === "heatNo") {
    //     return a.heatNo - b.heatNo || scoreComparator(a.score, b.score);
    //   } else {
    //     return scoreComparator(a.score, b.score);
    //   }
    // });
    // return resultOveralls;

    resultOveralls = splitIntoWithAndWithOutScore.withScore.sort((a, b) => {
      if (this.sortBy === "heatNo") {
        return a.heatNo - b.heatNo || scoreComparator(a.score, b.score);
      } else {
        return scoreComparator(a.score, b.score);
      }
    });

    return [...resultOveralls, ...splitIntoWithAndWithOutScore.noScore];
  }

  public getBibNo(result: IResultOverall): string {
    return this.resultsService.getBibNo(result as IResultAthlete);
  }

  public getScore(result: IResultOverall) {
    return result.scoreValue;
    // let score = typeof result.score === "number" ?
    //   this.commonService.roundNumberToDecimalPlaces(result.score, 2, false) :
    //   result.score;
    //
    // return score + (result.qualify.length > 0 ? " " + result.qualify : "") + (result.scoreText ? " " + result.scoreText : "");
  }

  public get hasResultsPermissionForComp() {
    return this.configService.hasResultsPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.resultEvent.comp.id
    );
  }

  public setQ(result: IResultOverall) {
    const resultOverallMatch: IResultOverall | null =
      this.resultOveralls.reduce((accum, res) => {
        if (res.id === result.id) {
          accum = res;
        }
        return accum;
      }, null as IResultOverall | null);

    if (!resultOverallMatch) {
      //  result can't be found...eh?!?
      return;
    }

    const hasBigQPos = this.isBigQ(resultOverallMatch);
    const hasLittleQPos = this.isLittleQ(resultOverallMatch);
    const hasNoQ = !hasBigQPos && !hasLittleQPos;

    if (hasNoQ) {
      resultOverallMatch.qualify = "Q";
    }

    if (hasBigQPos) {
      resultOverallMatch.qualify = "q";
    }

    if (hasLittleQPos) {
      resultOverallMatch.qualify = "";
    }

    this.resultOveralls = this.resultOveralls.map((res) => {
      return res.id === result.id ? resultOverallMatch : res;
    });

    const mapOveralls: Record<number, IResultOverall[]> =
      this.commonService.convertArrayToObjectArray(
        "heatNo",
        this.resultOveralls
      );
    const resultHeats: IResultHeat[] = R.clone(this.resultEvent).heats.map(
      (heat) => {
        let mapped = mapOveralls[heat.heatNo];
        heat.results = mapped;
        return heat;
      }
    );

    this.$emit("editEvent", resultHeats);
  }

  public isBigQ(result: IResultOverall | IResultAthlete): boolean {
    return result.qualify === "Q";
  }

  public isLittleQ(result: IResultOverall | IResultAthlete): boolean {
    return result.qualify === "q";
  }

  public getRowClass(
    result: IResultOverall | IResultAthlete
  ): "result-event-read--big-q" | "result-event-read--little-q" | "" {
    if (this.isBigQ(result)) {
      return "result-event-read--big-q";
    }
    if (this.isLittleQ(result)) {
      return "result-event-read--little-q";
    }
    return "";
  }

  public submitEvent() {
    // const resultHeat = R.clone(this.resultHeatInternal);
    // this.$emit("submitHeat", resultHeat);
  }

  public get getShowQualify(): boolean {
    if (!resultsService.canQualificationsBeDone(this.resultEvent)) {
      return false;
    }
    return this.isAdmin || this.hasResultsPermissionForComp;
  }

  public setSortBy(propName: keyof IResultOverall | "") {
    this.sortBy = propName;
  }
}
</script>

<style>
/*.result-heat-edit--table td, th{*/
/*    padding: 0 !important;*/
/*}*/

/*.result-heat-edit--table-action {*/
/*    width: 10%;*/
/*}*/

/*.result-heat-edit--table-pos {*/
/*    width: 10%;*/
/*}*/
/*.result-heat-edit--table-bib {*/
/*    width: 10%;*/
/*}*/
/*.result-heat-edit--table-ath {*/
/*    !*width: 10%;*!*/
/*}*/
/*.result-heat-edit--table-club {*/
/*    !*width: 10%;*!*/
/*}*/
/*.result-heat-edit--table-lane {*/
/*    width: 10%;*/
/*}*/
/*.result-heat-edit--table-sco {*/
/*    width: 10%;*/
/*}*/

/*.result-heat-edit--validation {*/
/*    font-weight: 600;*/
/*    color: darkred;*/
/*}*/
.result-heat-read--header {
  font-size: 1.25rem;
}

.result-heat-read--narrow {
  width: 10%;
}

.result-event-read--wind-icon {
}

.result-event-read--big-q {
  background-color: #c9e6c9;
}

.result-event-read--little-q {
  background-color: #ecf1ec;
}

.result-event-read--sort-by-active {
  color: red;
}
</style>
