<template>
    <auto-complete-mat
            field-label="Age Group"
            label-prop="name"
            :data="data"
            iconClassName=""
            :custom="getCustomForAgeGroupAutoComplete"
            :is-loading="isLoading"
            v-on:searchTermChanged="searchTermChanged"
            v-on:autoSelectionMade="onSelected">
    </auto-complete-mat>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {AgeGroupService} from "./agegroup-service";
    import {AgeGroupData} from "./agegroup-data";
    import {ICustom, IAutoCompleteValue} from "../common/ui/autocomplete/auto-complete-mat-models";
    import {IAgeGroup} from "./agegroup-models";
    import AutoCompleteMat from "../common/ui/autocomplete/auto-complete-mat.vue";
    import {IServerResponse} from "../common/common-models";
    import {messageDispatchHelper} from "../user-message/user-message-store";
    import {USER_MESSAGE_LEVEL} from "../user-message/user-message-models";

    @Component({
        name: "age-group-select",
        components: {
            "auto-complete-mat": AutoCompleteMat
        }
    })
    export default class AgeGroupSelect extends Vue {

        public ageGroupService: AgeGroupService = new AgeGroupService();
        public ageGroupData: AgeGroupData = new AgeGroupData();
        public isLoading: boolean = false;
        public data: IAgeGroup[] = [];

        public searchTermChanged(searchKey: string) {
            if (searchKey.length === 0) {
                return;
            }
            const listParams = this.ageGroupData.getListParamsDefault(searchKey);
            listParams.pagesize = 20;
            this.isLoading = true;
            this.ageGroupData.list(listParams)
                .then( (response: IServerResponse<IAgeGroup[]>) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }
                    this.data = response.data;
                    this.isLoading = false;
                    return;
                })
                .catch((error) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    this.isLoading = false;
                    return;
                });

        }

        public get getCustomForAgeGroupAutoComplete(): ICustom {
            return {
                dropDownLabelFunc: this.ageGroupService.getDropDownLabel
            } as ICustom;
        }

        public onSelected(autoComplete: IAutoCompleteValue) {
            this.$emit("onSelected", R.clone(autoComplete.value) as IAgeGroup);
        }
    }

</script>
