// import "jsdom-global/register";
// import { shallowMount } from "@vue/test-utils";
// import AgeGroupCheckBox from "./agegroup-checkbox.vue";
// import {IAgeGroup} from "../../agegroup-models";

describe("agegroup.AgeGroupCheckBox", () => {

    test("addAgeGroup array empty", () => {
        expect(1).toBe(1);
    });

    /*
    const ageGroups = [
        {
            id: 1,
            minAge: 9,
            minAtDay: 31,
            minAtMonth: 8,
            keyName: "Under 11",
            name: "Under 11",
            maxAge: 10,
            maxAtDay: 31,
            maxAtMonth: 8,
            maxAtYear: 0,
            minAtYear: 0
        },
        {
            id: 2,
            minAge: 11,
            minAtDay: 31,
            minAtMonth: 8,
            keyName: "Under 13",
            name: "Under 13",
            maxAge: 12,
            maxAtDay: 31,
            maxAtMonth: 8,
            maxAtYear: 0,
            minAtYear: 0
        },
        {
            id: 16,
            minAge: 40,
            minAtDay: 0,
            minAtMonth: 0,
            keyName: "VET 40",
            options: [
                {
                    aocode: "EA",
                    default: false
                },
                {
                    aocode: "IRL",
                    default: false
                },
                {
                    aocode: "ANI",
                    default: false
                }
            ],
            name: "VET 40",
            maxAge: 44,
            maxAtDay: 0,
            maxAtMonth: 0,
            maxAtYear: 0,
            minAtYear: 0
        },
        {
            id: 17,
            minAge: 45,
            minAtDay: 0,
            minAtMonth: 0,
            keyName: "VET 45",
            options: [
                {
                    aocode: "EA",
                    default: false
                },
                {
                    aocode: "IRL",
                    default: false
                },
                {
                    aocode: "ANI",
                    default: false
                }
            ],
            name: "VET 45",
            maxAge: 49,
            maxAtDay: 0,
            maxAtMonth: 0,
            maxAtYear: 0,
            minAtYear: 0
        }
    ] as any as IAgeGroup[];

    test("AgeGroupCheckBox", () => {
        const wrapper = shallowMount(AgeGroupCheckBox);
        const options = wrapper.findAll(".e4s-checkbox");
        expect(options.length).toBe(0);
        wrapper.destroy();
    });

    test("AgeGroupCheckBox select event-teams-filter and click", () => {

        const wrapper = shallowMount(AgeGroupCheckBox, {
            propsData: {
                ageGroups
            }
        });
        let options = wrapper.findAll(".e4s-checkbox");
        expect(options.length).toBe(4);

        options = wrapper.findAll(".check-box-highlight");
        expect(options.length).toBe(0);

        const  textInput = wrapper.find('input[type="text"]');

        // textInput.setValue("Under 13");
        // options = wrapper.findAll(".check-box-highlight");
        // expect(options.length).toBe(1);

        textInput.setValue("VET");
        // options = wrapper.findAll(".check-box-highlight");
        options = wrapper.findAll("row");
        // expect(options.selector).toBe(".check-box-highlight");
        // expect(options).toBe(".check-box-highlight");

        // textInput.setValue("vet");
        // options = wrapper.findAll(".check-box-highlight");
        // expect(options.length).toBe(2);

        // @ts-ignore
        expect(wrapper.vm.ageGroupsInternal.length).toBe(4);

        // @ts-ignore
        expect(wrapper.vm.checkedAgeGroups.length).toBe(0);

        const buttonSelectAll = wrapper.find(".btn-select-all");
        buttonSelectAll.trigger("click");

        // @ts-ignore
        expect(wrapper.vm.checkedAgeGroups.length).toBe(2);

        wrapper.destroy();
    });

    test("AgeGroupCheckBox check dups", () => {

        const wrapper = shallowMount(AgeGroupCheckBox, {
            propsData: {
                ageGroups
            }
        });
        let options = wrapper.findAll(".e4s-checkbox");
        expect(options.length).toBe(4);

        options = wrapper.findAll(".check-box-highlight");
        expect(options.length).toBe(0);

        let textInput = wrapper.find('input[type="text"]');

        //  Check we have 4 checkboxes...
        const checkBoxInputs = wrapper.findAll("input[type='checkbox']");
        expect(checkBoxInputs.length).toBe(4);

        //  get 1st itema nd select it, U11...
        const checkBoxInput = wrapper.findAll("input[type='checkbox']").at(0);
        // @ts-ignore
        checkBoxInput.setChecked();

        //  Check only 1 value selected
        // @ts-ignore
        expect(wrapper.vm.checkedAgeGroups.length).toBe(1);

        //  now event-teams-filter for U11
        textInput = wrapper.find('input[type="text"]');
        textInput.setValue("Under 11");

        //  Select all...
        const buttonSelectAll = wrapper.find(".btn-select-all");
        buttonSelectAll.trigger("click");

        //  Should stillonly be 1
        // @ts-ignore
        expect(wrapper.vm.checkedAgeGroups.length).toBe(1);

        wrapper.destroy();
    });

    test("AgeGroupCheckBox", () => {
        const wrapper = shallowMount(AgeGroupCheckBox);
        const options = wrapper.findAll(".e4s-checkbox");
        expect(options.length).toBe(0);
        wrapper.destroy();
    });
    */

});
