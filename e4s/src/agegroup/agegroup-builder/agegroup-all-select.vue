<template>
    <auto-complete-mat
            :field-label="fieldLabel"
            label-prop="name"
            :data="ageGroupsAll"
            iconClassName=""
            :custom="getCustomForAgeGroupAutoComplete"
            :is-loading="ageGroupsAllLoading"
            v-on:searchTermChanged="searchTermChanged"
            v-on:autoSelectionMade="onSelected">
    </auto-complete-mat>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {IAgeGroup} from "../agegroup-models";
    import {AgeGroupService} from "../agegroup-service";
    import {IBuilderStoreState} from "../../builder/builder-store";
    import {BUILDER_STORE_CONST} from "../../builder/builder-store-constants";
    import {mapState} from "vuex";
    import {ICustom, IAutoCompleteValue} from "../../common/ui/autocomplete/auto-complete-mat-models";
    import AutoCompleteMat from "../../common/ui/autocomplete/auto-complete-mat.vue";
    // import {IListParams} from "../../common/resource/resource-service";
    import {BuilderService} from "../../builder/builder-service";
    import {Prop} from "vue-property-decorator";

    @Component({
        name: "age-group-all-select",
        components: {
            "auto-complete-mat": AutoCompleteMat
        },
        computed: {
            ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
                ageGroupsAll: (state: IBuilderStoreState) => state.ageGroupsAll,
                ageGroupsAllLoading: (state: IBuilderStoreState) => state.ageGroupsAllLoading
            })
        }
    })
    export default class AgeGroupAllSelect extends Vue {
        @Prop({default: "Age Selector"}) public fieldLabel: string;

        public ageGroupService: AgeGroupService = new AgeGroupService();
        public builderService: BuilderService = new BuilderService();

        public searchTermChanged(searchKey: string) {
            if (searchKey.length === 0) {
                return;
            }
            const listParams = this.builderService.getListParamsDefault(searchKey);
            listParams.pagesize = 20;
            this.$store.dispatch(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
                BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_AGE_GROUPS_ALL, {listParams});
        }

        public get getCustomForAgeGroupAutoComplete(): ICustom {
            return {
                dropDownLabelFunc: this.ageGroupService.getDropDownLabel
            } as ICustom;
        }

        public onSelected(autoComplete: IAutoCompleteValue) {
            this.$emit("onSelected", R.clone(autoComplete.value) as IAgeGroup);
        }
    }

</script>
