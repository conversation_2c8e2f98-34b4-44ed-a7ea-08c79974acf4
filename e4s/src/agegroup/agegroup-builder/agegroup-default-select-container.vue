<template>
  <div>
    <age-group-default-select
      :default-ao="configApp.defaultao"
      :ao-age-groups-prop="ageGroupsDefault"
      :comp-age-groups-prop="compAgeGroups"
      :builder-competition="builderCompetition"
      v-on:onChanged="onDefaultAgeGroupSelected"
    />

    <div class="row" v-if="configApp.theme !== ''">
      <div class="col s12 m12 l12">
        <div class="e4s-form-wrapper">
          <CompeventAgeGroupCoverage
            :ao-code="configApp.defaultao.code"
            :age-group-comp-coverage-models="getAgeGroupCompCoverageModels"
            :comp-id="compId"
            :show-create-button="showCreateButton"
            style="overflow-x: auto"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { IAgeGroup } from "../agegroup-models";
import { Prop, Watch } from "vue-property-decorator";
import { BUILDER_STORE_CONST } from "../../builder/builder-store-constants";
import { IBuilderStoreState } from "../../builder/builder-store";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp } from "../../config/config-app-models";
import AgeGroupDefaultSelect from "./agegroup-default-select.vue";
import { IBuilderCompetition } from "../../builder/builder-models";
import CompeventAgeGroupCoverage from "../../compevent/compeventschedule/compevent-age-group-coverage/compevent-age-group-coverage.vue";

@Component({
  name: "age-group-default-select-container",
  computed: {
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      compAgeGroups: (state: IBuilderStoreState) =>
        state.builderCompetition.meta.ageGroups,
      ageGroupsDefault: (state: IBuilderStoreState) => state.ageGroupsDefault,
      builderCompetition: (state: IBuilderStoreState) =>
        state.builderCompetition,
      ageGroupsForComp: (state: IBuilderStoreState) => state.ageGroupsForComp,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
  components: {
    CompeventAgeGroupCoverage,
    "age-group-default-select": AgeGroupDefaultSelect,
  },
})
export default class AgeGroupDefaultSelectContainer extends Vue {
  public readonly builderCompetition: IBuilderCompetition;
  public readonly ageGroupsForComp: IAgeGroup[];
  public readonly configApp: IConfigApp;
  public readonly ageGroupsDefault: IAgeGroup[];

  @Prop({ default: 0 }) public readonly compId: number;
  @Prop({ default: () => [] }) public aoAgeGroupsProp: IAgeGroup[];
  @Prop({ default: () => [] }) public compDefaultAgeGroupsProp: IAgeGroup[];
  @Prop({
    default: false,
  })
  public readonly showCreateButton: boolean;

  public created() {
    this.getDefaultAgeGroups();
  }

  @Watch("compId")
  public onCompIdChanged(newValue: number) {
    this.getDefaultAgeGroups();
  }

  @Watch("configApp")
  public onConfigApp(newValue: IAgeGroup[]) {
    this.getDefaultAgeGroups();
  }

  public getDefaultAgeGroups() {
    if (!this.configApp.defaultao.code) {
      return;
    }

    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_AGE_GROUPS_DEFAULT,
      {
        aocode: this.configApp.defaultao.code,
        compId: this.compId,
      }
    );
  }

  public onDefaultAgeGroupSelected(ageGroups: IAgeGroup[]) {
    this.$store.commit(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_MUTATIONS_AGE_GROUPS_FOR_COMP,
      ageGroups
    );
  }

  public get getAgeGroupCompCoverageModels() {
    return this.ageGroupsForComp;
  }
}
</script>
