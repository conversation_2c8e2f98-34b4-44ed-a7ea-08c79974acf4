<template>
  <ul class="e4s-flex-row e4s-gap--small">

<!--    <a :href="v1Path">V1</a>-->
<!--    <a :href="v2Path">V2</a>-->

    <template v-if="currentUiVersion === 'v1'">
      <span>V1</span>
      <a :href="v2Path">V2</a>
<!--      <a :href="v2Path.value" v-on:click.prevent="goto('v2')">V2</a>-->
    </template>

    <template v-if="currentUiVersion === 'v2'">
      <a :href="v1Path">V1</a>
<!--      <a :href="v1Path.value" v-on:click.prevent="goto('v1')">V1</a>-->
      <span>V2</span>
    </template>
  </ul>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { UiVersion } from "./config-app-models";
import { LAUNCH_ROUTES_PATHS_V2_BASE } from "../launch/v2/launch-routes-v2";
import { useRouter } from "../router/migrateRouterVue3";
import { RawLocation } from "vue-router";
import {LAUNCH_ROUTES_PATHS} from "../launch/launch-routes"

export default defineComponent({
  name: "config-version-switch",
  components: {},
  props: {
    currentUiVersion: {
      type: String as PropType<UiVersion>,
      default: () => {
        const uiVersion: UiVersion = "v1";
        return uiVersion;
      },
    },
  },
  setup(props: { currentUiVersion: UiVersion }, context: SetupContext) {
    const routerInternal = useRouter();

    const v1Path = computed(() => {
      return "/#/" + LAUNCH_ROUTES_PATHS.SHOW_ENTRIES;
    });

    const v2Path = computed(() => {
      return "/" + LAUNCH_ROUTES_PATHS_V2_BASE;
    });

    function goto(uiVersion: UiVersion) {
      let location: RawLocation = {

        path: uiVersion === "v1" ? v1Path.value : v2Path.value,
      };
      routerInternal.push(location);
    }
    return { v1Path, v2Path, goto };
  },
});
</script>
