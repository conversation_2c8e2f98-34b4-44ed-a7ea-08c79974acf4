<template>
  <select
    class="generic-select"
    v-model="entityDropDown"
    v-on:change="onSelectedDropDown"
  >
    <!--        <option v-if="pleaseSelect" :value="{}" selected>Please Select</option>-->
    <option
      v-for="entity in userEntitiesInternal"
      :disabled="entity.isDisabled"
      :value="entity"
    >
      <span v-text="getEntityDropDownLabel(entity)"></span>
    </option>
  </select>
</template>

<script lang="ts">
import Vue from "vue";
import { Prop, Watch } from "vue-property-decorator";
import Component from "vue-class-component";
import { IEntity } from "../config-app-models";
import * as R from "ramda";
import { IEntityDropDown } from "../../athletecompsched/comp-event-teams/user-team-access/user-team-access-models";
import { ConfigService } from "../config-service";
import {simpleClone} from "../../common/common-service-utils"

const configService: ConfigService = new ConfigService();

@Component({
  name: "entity-select",
})
export default class EntitySelect extends Vue {
  @Prop({
    default: false,
  })
  public readonly pleaseSelect: boolean;
  @Prop({}) public readonly userEntities: IEntity[] | IEntityDropDown[];

  @Prop({
    default: () => {
      return configService.factoryEntity();
    }
  }) public readonly userEntityDefault: IEntity | IEntityDropDown;

  public userEntitiesInternal: IEntity[] | IEntityDropDown[] = [];
  public entityDropDown: IEntity = simpleClone(this.userEntityDefault);

  public created() {
    this.setUpEntities(this.userEntities);
  }

  @Watch("userEntities")
  public onUserEntitiesChanged(newValue: IEntity[] | IEntityDropDown[]) {
    this.setUpEntities(newValue);
  }

  @Watch("userEntityDefault")
  public onUserEntityDefaultChanged(newValue: IEntity | IEntityDropDown) {
    if (newValue.id === this.entityDropDown.id) {
      return;
    }
    this.entityDropDown = simpleClone(newValue);
  }

  public setUpEntities(entities: IEntity[] | IEntityDropDown[]) {
    let ents = R.clone(entities);
    if (this.pleaseSelect && this.entityDropDown.id === 0) {
      const pleaseEntity = configService.factoryEntity();
      pleaseEntity.name = "Please Select";
      ents = [pleaseEntity, ...ents];
      this.entityDropDown = pleaseEntity;
    }
    this.userEntitiesInternal = ents;
    this.setDefault();
  }

  public setDefault() {
    this.userEntitiesInternal.forEach((ent) => {
      if (ent.id === this.userEntityDefault.id) {
        this.entityDropDown = ent;
      }
    })
  }

  public getEntityDropDownLabel(entity: IEntity) {
    return (
      (R.isEmpty(entity.entityName)
        ? ""
        : (entity.entityName === "Club" && entity.clubType === "S"
            ? "School"
            : entity.entityName) + " - ") + entity.name
    );
  }

  public onSelectedDropDown() {
    // if (this.entityDropDown.id > 0) {
    this.$emit("onSelected", R.clone(this.entityDropDown));
    this.$emit("input", R.clone(this.entityDropDown));
    // }
  }
}
</script>
