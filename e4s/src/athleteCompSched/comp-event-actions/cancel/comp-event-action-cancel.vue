<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-form-sub-header left">
          <i
            class="
              material-icons
              e4s-force-img-vert-align-middle
              e4s-force-img--pad-right
            "
            >highlight_off</i
          >Cancel Entry
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div v-if="!getCanUserCancelEntry">
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-info-text--error">
            Please note: The Organiser is not issuing refunds.
          </div>
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <span class="active"> Reason for cancellation </span>
        <TextareaExpand v-model="reason"></TextareaExpand>
      </div>
    </div>

    <div>
      <div class="row" v-if="!getHasZeroPrice">
        <div class="col s12 m6 l6">
          <div class="e4s-section--padding">
            <div class="row">
              <div class="col s12 m12 l12">
                <div class="left">
                  <i
                    class="
                      material-icons
                      e4s-force-img-vert-align-middle
                      e4s-force-img--pad-right
                    "
                  >
                    call_split </i
                  ><span class="e4s-form-sub-header"
                    >Refund Entry
                    <span
                      v-text="
                        getCanUserCancelEntry ? '' : ' (Disabled by Organiser)'
                      "
                    ></span>
                  </span>
                </div>
              </div>
            </div>

            <div class="row" v-if="getCanUserCancelEntry">
              <div class="col s12 m12 l12">
                <span v-text="getRefundMessage"></span>
              </div>
            </div>
          </div>
        </div>

        <div class="col s12 m6 l6">
          <div class="e4s-section--padding">
            <div class="row">
              <div class="col s12 m12 l12">
                <div class="left">
                  <i
                    class="
                      material-icons
                      e4s-force-img-vert-align-middle
                      e4s-force-img--pad-right
                    "
                  >
                    card_giftcard
                  </i>
                  <span class="e4s-form-sub-header">
                    Credit Entry
                    <span
                      v-text="
                        getCanUserCredit ? '' : ' (Disabled by Organiser)'
                      "
                    ></span>
                  </span>
                </div>
              </div>
            </div>

            <div class="row" v-if="getCanUserCancelEntry">
              <div class="col s12 m12 l12">
                <span v-text="getCreditMessage"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="comp-event-actions--action-section"
        v-if="getCanUserCancelEntry"
      >
        <div class="row">
          <div class="col s12 m12 l12">
            <!--            <span class="e4s-form-sub-header">Select Option:</span>-->
            <div class="right">
              <label v-if="configApp.theme === 'gbr' && getCanUserCancelEntry">
                <input
                  type="radio"
                  id="comp-event-cancel"
                  class="browser-default"
                  value="Refund"
                  v-model="cancelType"
                  v-on:change="cancelTypeChanged"
                />
                <span
                  class="
                    comp-event-actions--credit-cancel-radio-label
                    e4s-form-sub-header
                  "
                >
                  Refund <span v-text="getRefundValue"></span>
                </span>
              </label>

              <label v-if="getCanUserCancelEntry">
                <input
                  type="radio"
                  id="comp-event-credit"
                  class="browser-default"
                  value="Credit"
                  v-model="cancelType"
                  v-on:change="cancelTypeChanged"
                />
                <span
                  class="
                    comp-event-actions--credit-cancel-radio-label
                    e4s-form-sub-header
                  "
                >
                  Credit <span v-text="getCreditValue"></span>
                </span>
              </label>

              <label v-if="!getCanUserCancelEntry">
                <input
                  type="radio"
                  id="comp-event-remove-only"
                  class="browser-default"
                  value="Remove Only"
                  v-model="cancelType"
                  v-on:change="cancelTypeChanged"
                />
                <span
                  class="
                    comp-event-actions--credit-cancel-radio-label
                    e4s-form-sub-header
                  "
                >
                  Remove Only
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div
          v-text="getValidationMessages"
          class="right e4s-info-text--error"
          style="margin-bottom: 8px"
        ></div>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div slot="buttons">
          <div class="left">
            <button
              class="e4s-button e4s-button--green e4s-button--10-wide"
              :disabled="getIsDisableButtons"
              v-on:click="proceed"
              v-if="askConf"
            >
              Confirm
            </button>
          </div>

          <div class="right">
            <LoadingSpinner v-if="isLoading"></LoadingSpinner>
            <button
              class="e4s-button e4s-button--red e4s-button--10-wide"
              v-on:click.stop="cancel"
            >
              <span>Back</span>
            </button>
            <button
              class="e4s-button e4s-button--green e4s-button--10-wide"
              :disabled="getIsDisableButtons"
              v-on:click="askForConf"
              v-if="!askConf"
            >
              Proceed
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { ICompEventActionsResult } from "../comp-event-actions-controller";

import { CommonService } from "../../../common/common-service";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import { PaymentsData } from "../../../admin/payments/payments.data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { IRefundEventPayloadUser } from "../../../admin/payments/payments.models";
import TextareaExpand from "../../../common/ui/field/textarea/textarea-expand.vue";
import { AthleteCompSchedData } from "../../athletecompsched-data";
import { ConfigService } from "../../../config/config-service";

import * as CancelEventActionService from "./cancel-event-action-service";
import { IBaseNameValue } from "../../../common/common-models";
import { CancelType } from "./cancel-event-action-service";
import * as CommonServiceUtils from "../../../common/common-service-utils";

@Component({
  name: "comp-event-action-cancel",
  components: { TextareaExpand },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class CompEventActionCancel extends Vue {
  public readonly configApp!: IConfigApp;

  @Prop({
    required: true,
  })
  public readonly compEventActionsResult!: ICompEventActionsResult;

  public commonService: CommonService = new CommonService();
  public configService: ConfigService = new ConfigService();

  public cancelType: "Credit" | "Refund" | "Remove Only" | "" =
    CancelEventActionService.getCancelType(this.compEventActionsResult);
  public reason = "";
  public testMode = false;
  public askConf = false;
  public isLoading = false;
  public validationMessages: string[] = [];

  created() {
    this.init();
  }

  public get getHasZeroPrice(): boolean {
    return CancelEventActionService.getHasZeroPrice(
      this.compEventActionsResult.compEvent
    );
  }

  public get getCanUserCancelEntry(): boolean {
    return CancelEventActionService.getCanUserCancelEntry(
      this.compEventActionsResult
    );
  }

  // public get getCanUserRefund(): boolean {
  //   return CancelEventActionService.canUserRefund(this.compEventActionsResult);
  // }
  //
  // public get getCanUserCredit(): boolean {
  //   return CancelEventActionService.canUserCredit(this.compEventActionsResult);
  // }
  //
  // public get getCanUserRefundOrCredit(): boolean {
  //   return CancelEventActionService.canUserRefundOrCredit(
  //     this.compEventActionsResult
  //   );
  // }

  public init(): void {
    this.cancelType = CancelEventActionService.getCancelType(
      this.compEventActionsResult
    );

    this.reason = "";
    this.testMode = false;
    this.askConf = false;
    this.isLoading = false;
  }

  public get getHasBuilderPermissionForComp(): boolean {
    return this.configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.compEventActionsResult.competitionSummaryPublic.compOrgId,
      this.compEventActionsResult.competitionSummaryPublic.compId
    );
  }

  public get getRefundValue(): string {
    return CommonServiceUtils.getAmountAsCurrency(
      CancelEventActionService.getRefundValue(
        this.compEventActionsResult,
        this.configApp.options
      ),
      this.configApp.currency
    );
  }

  public get getCreditValue(): string {
    return CommonServiceUtils.getAmountAsCurrency(
      this.compEventActionsResult.compEvent.order.wcLineValue,
      this.configApp.currency
    );
  }

  public get getIsDisableButtons(): boolean {
    return this.isLoading || this.cancelType === "";
  }

  public get getRefundMessage() {
    return CancelEventActionService.getRefundMessage(
      this.compEventActionsResult,
      this.configApp.options
    );
  }

  public get getCreditMessage() {
    return CancelEventActionService.getCreditMessage(
      this.compEventActionsResult,
      this.configApp.currency
    );
  }

  public get getCancelTypes(): IBaseNameValue<CancelType>[] {
    return CancelEventActionService.getCancelTypes(this.compEventActionsResult);
  }

  public proceed() {
    if (this.cancelType === "Remove Only") {
      this.onlyRemoveUserFromEvent();
      return;
    }

    const isCredit = this.cancelType === "Credit";
    const refundEventUser: IRefundEventPayloadUser = {
      compid: this.compEventActionsResult.competitionSummaryPublic.compId,
      orderid: this.compEventActionsResult.compEvent.order.orderId,
      reason: this.reason,
      productid: this.compEventActionsResult.compEvent.order.productId,
      credit: isCredit,
    };
    this.isLoading = true;
    const prom = new PaymentsData().refundEventUser(refundEventUser);
    const successMessage = this.cancelType + " processed.";
    handleResponseMessages(prom, successMessage);
    prom
      .then((response) => {
        if (response.errNo === 0) {
          this.$emit("submitted");
        }
      })
      .finally(() => {
        this.isLoading = false;
        this.askConf = false;
      });
  }

  public onlyRemoveUserFromEvent() {
    this.isLoading = true;
    const prom = new AthleteCompSchedData().cancelEvent(
      this.compEventActionsResult.compEvent.entryId,
      this.reason
    );
    const successMessage = "Cancel processed.";
    handleResponseMessages(prom, successMessage);
    prom
      .then((response) => {
        if (response.errNo === 0) {
          this.$emit("submitted");
        }
      })
      .finally(() => {
        this.isLoading = false;
        this.askConf = false;
      });
  }

  public get getValidationMessages(): string {
    return this.validationMessages.join("<br/>");
  }

  public askForConf() {
    this.validate();
    if (this.validationMessages.length > 0) {
      return;
    }
    this.askConf = true;
  }

  public validate() {
    this.validationMessages = [];
    if (this.cancelType === "") {
      this.validationMessages.push("Please select an option.");
    }
    if (this.reason === "") {
      this.validationMessages.push("Please enter a reason.");
    }
  }

  public cancelTypeChanged() {
    this.askConf = false;
  }

  public cancel() {
    this.$emit("close");
  }
}
</script>
