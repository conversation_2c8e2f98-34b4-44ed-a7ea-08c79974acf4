import { AthleteCompSchedData } from "../athletecompsched-data";
import {
  IAthleteCompSchedResponse,
  IAthleteCompSchedRuleEvent,
  ICompShedRuleConfig,
} from "../athletecompsched-models";
import { AthleteService } from "../../athlete/athlete-service";
import { IAthlete } from "../../athlete/athlete-models";
import { CompetitionService } from "../../competition/competiton-service";
import { ICompetitionSummaryPublic } from "../../competition/competition-models";
import { AthleteCompSchedService } from "../athletecompsched-service";
import { AthleteDataService } from "../../athlete/athlete-data-service";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import { CompetitionData } from "../../competition/competition-data";
import { IUserApplication } from "../../config/config-app-models";
import { ConfigService } from "../../config/config-service";
import * as R from "ramda";

export interface ICompEventActionsResult {
  competitionSummaryPublic: ICompetitionSummaryPublic;
  athlete: IAthlete;
  compEvent: IAthleteCompSchedRuleEvent;
  compEvents: IAthleteCompSchedRuleEvent[];
  compRule: ICompShedRuleConfig | null;
  userApplication: IUserApplication;
}

export class CompEventActionsController {
  private entryId = 0;
  private userApplication: IUserApplication =
    new ConfigService().factoryUserApplication();
  public isLoading = false;
  private athleteCompSchedService = new AthleteCompSchedService();
  private athleteCompSchedData: AthleteCompSchedData =
    new AthleteCompSchedData();
  private athleteCompSchedResponse: IAthleteCompSchedResponse =
    this.athleteCompSchedService.factoryAthleteCompSchedResponse();
  private athleteService: AthleteService = new AthleteService();
  private competitionService: CompetitionService = new CompetitionService();

  public compEventActionsResult: ICompEventActionsResult =
    this.factoryCompEventActionsResult();

  public factoryCompEventActionsResult(): ICompEventActionsResult {
    return {
      competitionSummaryPublic: this.competitionService.factorySummaryPublic(),
      athlete: this.athleteService.factoryGetAthlete(),
      compEvent:
        this.athleteCompSchedService.factoryAthleteCompSchedRuleEvent(),
      compEvents: [],
      compRule: null,
      userApplication: new ConfigService().factoryUserApplication(),
    };
  }

  public loadData(
    compId: number,
    athleteId: number,
    clubId: number,
    entryId: number,
    userApplication: IUserApplication
  ) {
    this.entryId = entryId;
    this.userApplication = R.clone(userApplication);

    this.isLoading = true;
    const promEvents = this.athleteCompSchedData.getCompSchedEventsForAthlete(
      compId,
      athleteId,
      clubId
    );
    promEvents.then((response) => {
      if (response.errNo === 0) {
        this.athleteCompSchedResponse = response;
      }
    });

    const promAthlete = new AthleteDataService().read(athleteId);
    handleResponseMessages(promAthlete);
    promAthlete.then((response) => {
      if (response.errNo === 0) {
        this.compEventActionsResult.athlete = response.data;
      }
    });

    const promComp = new CompetitionData().getCompById(compId);
    handleResponseMessages(promComp);
    promComp.then((response) => {
      if (response.errNo === 0) {
        this.compEventActionsResult.competitionSummaryPublic = response.data;
      }
    });

    return Promise.all([promEvents, promAthlete, promComp]).then(() => {
      const res = this.processData();
      this.isLoading = false;
      return res;
    });
  }

  public createCompEventActionsResult(
    compEvent: IAthleteCompSchedRuleEvent,
    compEvents: IAthleteCompSchedRuleEvent[],
    entryId: number,
    rule: ICompShedRuleConfig,
    userApplication: IUserApplication,
    athlete: IAthlete
  ): ICompEventActionsResult {
    const compEventActionsResult = this.factoryCompEventActionsResult();

    compEventActionsResult.compEvent = compEvents.reduce(
      (accum, cEvent) => {
        if (cEvent.entryId === entryId) {
          accum = { ...cEvent };
        }
        return accum;
      },
      { ...compEventActionsResult.compEvent }
    );

    const inCartOrPaid = compEvents.filter((cEvent) => {
      return cEvent.order.productId > 0;
    });

    compEventActionsResult.compRule = rule;
    compEventActionsResult.userApplication = userApplication;

    const eventsWithout: IAthleteCompSchedRuleEvent[] = compEvents.filter(
      (cEvent) => {
        return cEvent.ceid !== compEventActionsResult.compEvent.ceid;
      }
    );

    // const eventsSelected: IAthleteCompSchedRuleEvent[] = [];
    // compEventActionsResult.compEvents =  this.athleteCompSchedService.processEvents(
    //   compEventActionsResult.athlete,
    //   pbKey,
    //   rule,
    //   null,
    //   eventsSelected,
    //   eventsFromFactory,
    //   configStore.configApp.userInfo.user,
    //   ageInfo,
    //   state.competitionSummaryPublic,
    //   configStore.configApp.userInfo
    // );

    compEventActionsResult.compEvents =
      this.athleteCompSchedService.processEventsNew(
        compEventActionsResult.athlete,
        rule,
        compEventActionsResult.compEvent,
        inCartOrPaid,
        eventsWithout,
        userApplication,
        compEventActionsResult.competitionSummaryPublic
      );

    return compEventActionsResult;
  }

  public processData(): ICompEventActionsResult {
    const eventsFromFactory: IAthleteCompSchedRuleEvent[] =
      this.athleteCompSchedService.factoryEventsToEventRule(
        this.athleteCompSchedResponse.events
      );

    this.compEventActionsResult.compEvent = eventsFromFactory.reduce(
      (accum, compEvent) => {
        if (compEvent.entryId === this.entryId) {
          accum = { ...compEvent };
        }
        return accum;
      },
      { ...this.compEventActionsResult.compEvent }
    );

    const inCartOrPaid = eventsFromFactory.filter((compEvent) => {
      return compEvent.order.productId > 0;
    });

    let compRule: ICompShedRuleConfig | null = null;
    if (this.athleteCompSchedResponse.rules) {
      compRule = this.athleteCompSchedResponse.rules;
    }
    this.compEventActionsResult.compRule = compRule;
    this.compEventActionsResult.userApplication = this.userApplication;

    const eventsWithout: IAthleteCompSchedRuleEvent[] =
      eventsFromFactory.filter((compEvent) => {
        return compEvent.ceid !== this.compEventActionsResult.compEvent.ceid;
      });

    this.compEventActionsResult.compEvents =
      this.athleteCompSchedService.processEventsNew(
        this.compEventActionsResult.athlete,
        compRule,
        this.compEventActionsResult.compEvent,
        inCartOrPaid,
        eventsWithout,
        this.userApplication,
        this.compEventActionsResult.competitionSummaryPublic
      );

    return this.compEventActionsResult;
  }
}
