<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <!--    <div style="background: deeppink">showSection: {{ showSection }}</div>-->
    <SectionLinksWrapper class="e4s-tab-links--sub-x" v-if="false">
      <template>
        <SectionLink
          class="e4s-header--500"
          :section-link="mapSectionLinks.HEADERS"
          :is-active="showSection === 'HEADERS'"
          v-on:selected="sectionClick('HEADERS')"
        />

        <SectionLink
          class="e4s-header--500"
          :section-link="mapSectionLinks.CREATE"
          v-if="showSection !== 'HEADERS'"
          :is-active="showSection === 'CREATE'"
          v-on:selected="sectionClick('CREATE')"
        />

        <!--You can only click back to this if you are editing a team-->
        <!--        <SectionLink-->
        <!--          class="e4s-header&#45;&#45;500"-->
        <!--          v-if="showSection === 'HEADER_TEAMS' || showSection === 'EDIT_TEAM'"-->
        <!--          :section-link="mapSectionLinks.HEADER_TEAMS"-->
        <!--          :is-active="showSection === 'HEADER_TEAMS'"-->
        <!--          :is-disabled="eventTeamHeaderSelected.id === 0"-->
        <!--          v-on:selected="sectionClick('HEADER_TEAMS')"-->
        <!--        />-->

        <!--This only appears if you editing a team-->
        <SectionLink
          class="e4s-header--500"
          v-if="showSection === 'EDIT_TEAM'"
          :section-link="mapSectionLinks.EDIT_TEAM"
          :is-active="showSection === 'EDIT_TEAM'"
          v-on:selected="sectionClick('EDIT_TEAM')"
        />
      </template>
    </SectionLinksWrapper>

    <!--    <div class="e4s-vertical-spacer&#45;&#45;large"></div>-->

    <!--HEADERS-->
    <div
      class="e4s-flex-column e4s-gap--large"
      v-show="showSection === 'HEADERS'"
    >
      <!--Create Wizard Used to be here-->

      <div>
        <div slot="all" class="e4s-flex-column e4s-gap--standard">
          <template v-if="getEnteredTeams.length === 0">
            <InfoSectionV2 info-type="warn">
              <p>You currently have no teams entered.</p>
              <!--              <ButtonGenericV2 text="Create" v-on:click="createAthlete" />-->
            </InfoSectionV2>
          </template>

          <div class="e4s-flex-row e4s-justify-flex-space-between-x">
            <div v-if="false" class="e4s-flex-row e4s-gap--standard">
              <CounterV2 text="Events" :counter="getEnteredTeams.length" />
              <CounterV2 text="Teams" :counter="getEntryCount.teams" />
            </div>

            <ButtonGenericV2
              text="Create Team"
              class="e4s-button--auto e4s-flex-row--end"
              @click="showSection = 'CREATE'"
            />
          </div>

          <!--          v-if="-->
          <!--          eventTeamHeadersInternal.length > 0 &&-->
          <!--          !filterOptions.showAdvancedFilter-->
          <!--          "-->
          <FilterGenericWrapperV2
            v-if="!filterOptions.showAdvancedFilter"
            :filter-text="eventTeamHeaderFilter.freeText"
            :filter-count="getFilterCount"
            filter-placeholder="E.g. Team Name, athlete name"
            @filterButtonClick="filterOptions.showAdvancedFilter = true"
            @filterTextChange="setFilterText"
          />

          <InfoSectionV2 info-type="error" v-if="isClubCompInfoCompetition">
            <div
              class="e4s-subheader--200"
              v-text="getTeamsEnteredMessage"
            ></div>
          </InfoSectionV2>

          <CardGenericV2 v-if="filterOptions.showAdvancedFilter">
            <template slot="all">
              <TeamsFilterV2
                v-if="filterOptions.showAdvancedFilter"
                :event-team-header-filter="eventTeamHeaderFilter"
                :event-team-headers="eventTeamHeadersInternal"
                @onChange="setFilter"
              />

              <!--        Put a cancel and apply button here, row off to right hand side    -->
              <div class="e4s-flex-row">
                <ButtonGenericV2
                  text="Hide Filters"
                  button-type="tertiary"
                  @click="filterOptions.showAdvancedFilter = false"
                />
                <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
                  <ButtonGenericV2
                    text="Reset"
                    button-type="tertiary"
                    class="e4s-flex-row--end"
                    @click="resetFilter"
                  />

                  <ButtonGenericV2
                    text="Apply"
                    class="e4s-flex-row--end"
                    @click="doFilter"
                  />
                </div>
              </div>
            </template>
          </CardGenericV2>

          <!--          {{ this.eventTeamHeaderFilter }}-->

          <div
            class="e4s-flex-column e4s-gap--large"
            v-show="displayEventGrouping === 'FLAT'"
          >
            <EventTeamHeaderShowTeamsV2
              v-for="(eventTeamHeader, index) in getEnteredTeams"
              :key="eventTeamHeader.id"
              class="e4s-card--well"
              :competition="competition"
              :event-team-header="eventTeamHeader"
              :user-entity="userEntitySelected"
              :user-entities="getEntitiesForEvent(eventTeamHeaderSelected)"
              v-on:addTeamNew="editTeamNew"
              v-on:editTeamNew="editTeamNew"
              v-on:deleteTeam="deleteTeam"
              v-on:cancel="showSection = 'HEADERS'"
              v-on:onUserEntitySelected="onUserEntitySelected"
            />
          </div>

          <!--From here down, prob not needed-->
          <!--From here down, prob not needed-->
          <!--From here down, prob not needed-->
          <!--From here down, prob not needed-->
          <!--From here down, prob not needed-->
          <!--From here down, prob not needed-->
          <!--From here down, prob not needed-->
          <!--From here down, prob not needed-->
          <!--From here down, prob not needed-->

          <div
            class="e4s-flex-column e4s-gap--large"
            v-show="displayEventGrouping === 'GROUP'"
            v-for="(evtTeamHeaders, key) in getEnteredTeamsGrouped"
            :key="key"
          >
            <AccordionV3>
              <div
                class="
                  e4s-flex-row
                  e4s-full-width
                  e4s-justify-flex-row-vert-center
                  e4s-justify-flex-space-between
                "
                slot="summary"
              >
                <div v-text="key"></div>

                <div class="e4s-flex-row e4s-flex-nowrap e4s-gap--standard">
                  <CounterV2 text="Events" :counter="evtTeamHeaders.length" />
                  <CounterV2
                    text="Teams"
                    :counter="getEventTeamEntryCount(evtTeamHeaders).teams"
                  />
                </div>
              </div>
              <div slot="content" class="e4s-flex-column e4s-gap--large">
                <EventTeamHeaderShowTeamsV2
                  v-for="(eventTeamHeader, index) in evtTeamHeaders"
                  :key="eventTeamHeader.id"
                  class="e4s-card--well"
                  :competition="competition"
                  :event-team-header="eventTeamHeader"
                  :user-entity="userEntitySelected"
                  :user-entities="getEntitiesForEvent(eventTeamHeaderSelected)"
                  v-on:addTeamNew="editTeamNew"
                  v-on:editTeamNew="editTeamNew"
                  v-on:deleteTeam="deleteTeam"
                  v-on:cancel="showSection = 'HEADERS'"
                  v-on:onUserEntitySelected="onUserEntitySelected"
                />
              </div>
            </AccordionV3>
          </div>

          <!--/From here down, prob not needed-->
          <!--/From here down, prob not needed-->
          <!--/From here down, prob not needed-->
          <!--/From here down, prob not needed-->
          <!--/From here down, prob not needed-->
          <!--/From here down, prob not needed-->
          <!--/From here down, prob not needed-->
          <!--/From here down, prob not needed-->
          <!--/From here down, prob not needed-->

          <!--          <AccordionV2-->
          <!--            v-show="displayEventGrouping === 'GROUP'"-->
          <!--            v-for="(evtTeamHeaders, key) in getEnteredTeamsGrouped"-->
          <!--            :key="key"-->
          <!--          >-->
          <!--            <div-->
          <!--              slot="summary"-->
          <!--              class="-->
          <!--                e4s-flex-row-->
          <!--                e4s-full-width-->
          <!--                e4s-justify-flex-space-between-->
          <!--                e4s-flex-center-->
          <!--              "-->
          <!--            >-->
          <!--              <span v-text="key" class="e4s-subheader&#45;&#45;200"></span>-->
          <!--              &lt;!&ndash;              <span v-text="value.length + ' events'" class="e4s-subheader&#45;&#45;200"></span>&ndash;&gt;-->
          <!--              <div-->
          <!--                class="-->
          <!--                  e4s-flex-row e4s-flex-nowrap-->
          <!--                  e4s-card-competition__participant-overview-container-->
          <!--                  e4s-align-self-flex-end-x-->
          <!--                "-->
          <!--              >-->
          <!--                <CounterV2 text="Events" :counter="evtTeamHeaders.length" />-->
          <!--                <CounterV2-->
          <!--                  text="Teams"-->
          <!--                  :counter="getEventTeamEntryCount(evtTeamHeaders).teams"-->
          <!--                />-->
          <!--              </div>-->
          <!--            </div>-->

          <!--            <div slot="content" class="e4s-flex-column e4s-gap&#45;&#45;large">-->
          <!--              <EventTeamHeaderV2-->
          <!--                v-for="(eventTeamHeader, index) in evtTeamHeaders"-->
          <!--                :key="eventTeamHeader.id"-->
          <!--                :index="index"-->
          <!--                :competition="competition"-->
          <!--                :event-team-header="eventTeamHeader"-->
          <!--                :user-entity="userEntitySelected"-->
          <!--                :user-entities="getEntitiesForEvent(eventTeamHeader)"-->
          <!--                v-on:selected="setEventTeamHeaderSelected"-->
          <!--              />-->
          <!--            </div>-->
          <!--          </AccordionV2>-->
        </div>
      </div>
    </div>
    <!--/HEADERS-->

    <div
      class="e4s-flex-column e4s-gap--large"
      v-show="showSection === 'CREATE'"
    >
      <!--CreateTeam-->
      <CardGenericV2 class="e4s-card--well">
        <div slot="all" class="e4s-flex-column e4s-gap--standard">
          <!--          <div class="e4s-flex-row e4s-justify-flex-end">-->
          <!--            <ButtonGenericV2-->
          <!--              text="Hide Add Team"-->
          <!--              button-type="tertiary"-->
          <!--              v-on:click="showTeamAdderWizard = false"-->
          <!--            />-->
          <!--          </div>-->

          <FormGenericSectionTitleV2
            class="e4s-flex-end"
            title-size="400"
            section-title="Select Team Criteria"
            :show-cancel-button="false"
          >
            <div
              slot="right-content"
              class="e4s-flex-row e4s-align-self-flex-end"
            >
              <!--              <PillV2 text="Security: Club" pill-type="closed" />-->
              <PrimaryLink
                link-text="Back to My Teams"
                @onClick="showSection = 'HEADERS'"
              />
            </div>
          </FormGenericSectionTitleV2>

          <div>
            <div
              v-if="areAnyEventsSecure"
              class="e4s-flex-column e4s-gap--standard"
            >
              <div>
                Please note: some teams are <strong>"secure"</strong>. Only
                representatives of the entity that looks after the athlete, e.g.
                club representative, can enter these teams. If you think you
                should have access, please
                <PrimaryLink
                  link-text="contact the organiser"
                  @onClick="contactOrganiser"
                />
                <!--                <a href="#" v-on:click="contactOrganiser"-->
                <!--                  >contact the organiser.</a-->
                <!--                >-->
              </div>

              <div class="e4s-flex-column" v-if="userEntities.length > 0">
                <span class="e4s-info-text--error"
                  >You have more than one entity assigned to you, please select
                  if entering a secure team.</span
                >
                <EntitySelectV2
                  :please-select="userEntities.length > 1"
                  :user-entities="userEntities"
                  :default-entity="userEntitySelected"
                  v-on:onSelected="onUserEntitySelected"
                />
              </div>
            </div>

            <!--The team create stepper -->
            <EventTeamsAddV2
              :competition="competition"
              :event-team-headers="eventTeamHeaders"
              :user-entity="userEntitySelected"
              v-on:addTeam="addTeamNew"
            />
            <!--/The team create stepper -->
          </div>
        </div>
      </CardGenericV2>
      <!--/CreateTeam-->
    </div>

    <!--HEADER_TEAMS-->
    <div
      v-if="showSection === 'HEADER_TEAMS'"
      class="e4s-flex-column e4s-gap--standard"
    >
      <!--      <div class="e4s-flex-row e4s-justify-flex-end">-->
      <!--        <ButtonGenericBackV2 v-on:click="showSection = 'HEADERS'" />-->
      <!--      </div>-->
      <EventTeamHeaderShowTeamsV2
        class="e4s-card--well"
        :competition="competition"
        :event-team-header="eventTeamHeaderSelected"
        :user-entity="userEntitySelected"
        :user-entities="getEntitiesForEvent(eventTeamHeaderSelected)"
        v-on:addTeam="editTeam"
        v-on:editTeam="editTeam"
        v-on:deleteTeam="deleteTeam"
        v-on:cancel="showSection = 'HEADERS'"
        v-on:onUserEntitySelected="onUserEntitySelected"
      />
    </div>
    <!--/HEADER_TEAMS-->

    <!--EDIT_TEAM-->
    <div
      v-if="showSection === 'EDIT_TEAM'"
      class="e4s-flex-column e4s-gap--standard"
    >
      <!--      <div class="e4s-flex-row e4s-justify-flex-end">-->
      <!--        <ButtonGenericBackV2 v-on:click="showSection = 'HEADER_TEAMS'" />-->
      <!--      </div>-->
      <EventTeamDefaultEditV2
        v-if="displayWhichFormType === 'DEFAULT'"
        class="e4s-card--well"
        :isLoading="isLoading"
        :competition="competition"
        :event-team-header="eventTeamHeaderSelected"
        :comp-event-team-prop="editCompEventTeam"
        :user-entity="userEntitySelected"
        :is-new="eventTeamHeaderSelected.id === 0"
        v-on:editTeam="editTeam"
        v-on:deleteTeam="deleteTeam"
        v-on:submit="submitDefault"
        v-on:cancel="cancelEditTeam"
      >
        <PrimaryLink
          slot="top-right-back"
          link-text="Back to My Teams"
          @onClick="showSection = 'HEADERS'"
        />
      </EventTeamDefaultEditV2>

      <!--      v-on:submit="submitSchool"-->
      <SchoolTeamFormV2
        v-if="displayWhichFormType === 'SCHOOL'"
        :isLoading="isLoading"
        :competition="competition"
        :event-team-header="eventTeamHeaderSelected"
        :event-team-headers="eventTeamHeadersInternal"
        :comp-event-team-school-prop="editCompEventTeam"
        :user-entity="userEntitySelected"
        :is-new="eventTeamHeaderSelected.id === 0"
        :edit-mode="true"
        @editTeam="editTeam"
        @deleteTeam="deleteTeam"
        @cancel="cancelEditTeam"
        @submit="submitSchool"
      >
        <PrimaryLink
          slot="top-right-back"
          link-text="Back to My Teams"
          @onClick="showSection = 'HEADERS'"
        />
      </SchoolTeamFormV2>

      <LeagueTeamFormV2
        v-if="displayWhichFormType === 'LEAGUE'"
        :isLoading="isLoading"
        :competition="competition"
        :event-team-header="eventTeamHeaderSelected"
        :event-team-headers="eventTeamHeadersInternal"
        :comp-event-team-prop="editCompEventTeam"
        :user-entity="userEntitySelected"
        :edit-mode="true"
        :is-new="eventTeamHeaderSelected.id === 0"
        v-on:editTeam="editTeam"
        v-on:deleteTeam="deleteTeam"
        v-on:submit="submitLeague"
        v-on:cancel="cancelEditTeam"
      >
        <PrimaryLink
          slot="top-right-back"
          link-text="Back to My Teams"
          @onClick="showSection = 'HEADERS'"
        />
      </LeagueTeamFormV2>
    </div>
    <!--/EDIT_TEAM-->

    <LoadingSpinnerV2 v-if="eventTeamHeadersLoading || isLoading" />
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { ConfigService } from "../../../config/config-service";
import { EventTeamService } from "../event-team-service";
import { CompetitionService } from "../../../competition/competiton-service";
import SchedInfoV2 from "../../sched-info/v2/sched-info-v2.vue";
import UserTeamAccessV2 from "./user-team-access-v2.vue";
import {
  ENTRY_STORE_CONST,
  IEntryStoreState,
} from "../../../entry/entry-store";
import {
  ICompEventTeam,
  ICompEventTeamBase,
  IEventTeamHeader,
} from "../event-teams-models";
import { ISchedInfo, ISchedInfoDetail } from "../../athletecompsched-models";
import {
  COMP_EVENT_TEAMS_STORE_CONST,
  ICompEventTeamsStoreState,
} from "../comp-event-store";
import {
  IConfigApp,
  IEntity,
  IUserInfo,
} from "../../../config/config-app-models";
import {
  ICompetitionInfo,
  ICompetitionSummaryPublic,
} from "../../../competition/competition-models";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import EventTeamHeaderV2 from "./event-team-header-v2.vue";
import {
  convertArrayToObjectArray,
  simpleClone,
} from "../../../common/common-service-utils";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import CardGenericV2 from "../../../common/ui/layoutV2/card-generic-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import EntitySelectV2 from "../../../config/entity/entity-select-v2.vue";
import PillV2 from "../../../common/ui/layoutV2/pills/pill-v2.vue";
import EventTeamHeaderShowTeamsV2, {
  EventTeamHeaderShowTeamsV2EmitEdit,
} from "./teams/event-team-header-show-teams-v2.vue";
import EventTeamDefaultEditV2 from "./teams/event-team-default-edit-v2.vue";
import { CompEventTeamData } from "../comp-event-team-data";
import { ResourceData } from "../../../common/resource/resource-service";
import { IServerResponse } from "../../../common/common-models";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import EventTeamHeaderStatusPillV2 from "./event-team-header-status-pill-v2.vue";
import FormGenericSectionTitleV2 from "../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import EventTeamsAddV2 from "./filter/event-teams-add-v2.vue";
import CounterV2 from "../../../common/ui/layoutV2/counter/counter-v2.vue";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import AccordionV2 from "../../../common/ui/layoutV2/accordion/accordion-v2.vue";
import FieldRadioV2 from "../../../common/ui/layoutV2/fields/field-radio-v2.vue";
import { ATH_COMP_SCHED_STORE_CONST } from "../../store/athleteCompSched-store";
import SectionLinks from "../../../common/ui/layoutV2/tabs/section-links.vue";
import SectionLink from "../../../common/ui/layoutV2/tabs/section-link.vue";
import { ISectionLink } from "../../../common/ui/layoutV2/tabs/section-links-models";
import SectionLinksWrapper from "../../../common/ui/layoutV2/tabs/section-links-wrapper.vue";
import * as ClubCompInfoService from "../../../entry/v2/schools/clubCompInfoService";
import { hasClubCompInfoCompetition } from "../../../entry/v2/schools/clubCompInfoService";
import { IClubCompInfo } from "../../../entry/v2/schools/clubCompInfo-models";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import AccordionV3 from "../../../common/ui/layoutV2/accordion/AccordionV3.vue";
import InfoSectionV2 from "../../../common/ui/layoutV2/info-section-v2.vue";
import SchoolTeamFormV2 from "./teams/school/school-team-form-v2.vue";
import { ICompEventTeamSchool } from "../school/school-team-models";
import { SchoolTeamData } from "../school/school-team-data";
import LeagueTeamFormV2 from "./teams/league-team-form-v2.vue";
import TeamsFilterV2 from "./event-teams-filter/TeamsFilterV2.vue";
import FilterGenericWrapperV2 from "../../../common/ui/layoutV2/form/filter-generic-wrapper-v2.vue";
import { IEventTeamHeaderFilterV2 } from "../event-teams-filter/event-teams-filter-models";
import { EventTeamsFilterService } from "../event-teams-filter/event-teams-service";
import { filterEventTeamHeadersV2 } from "./event-teams-filter/models/teams-filter-service-v2";

const configService: ConfigService = new ConfigService();
const eventTeamService: EventTeamService = new EventTeamService();
const competitionService: CompetitionService = new CompetitionService();

// | "HEADER_TEAMS"
type EventTeamPanelSectionName = "HEADERS" | "CREATE" | "EDIT_TEAM";

@Component({
  name: "event-teams-panel-v2",
  components: {
    FilterGenericWrapperV2,
    TeamsFilterV2,
    LeagueTeamFormV2,
    SchoolTeamFormV2,
    InfoSectionV2,
    AccordionV3,
    PrimaryLink,
    SectionLinksWrapper,
    SectionLink,
    SectionLinks,
    FieldRadioV2,
    AccordionV2,
    ButtonGenericBackV2,
    CounterV2,
    EventTeamsAddV2,
    FormGenericSectionTitleV2,
    EventTeamHeaderStatusPillV2,
    EventTeamDefaultEditV2,
    EventTeamHeaderShowTeamsV2,
    PillV2,
    EntitySelectV2,
    ButtonGenericV2,
    CardGenericV2,
    LoadingSpinnerV2,
    EventTeamHeaderV2,
    UserTeamAccessV2,
    SchedInfoV2,
  },
  computed: {
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      clubCompInfo: (state: IEntryStoreState) => state.entryForm.clubCompInfo,
    }),
    ...mapState(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME,
      {
        eventTeamHeaders: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeaders,
        eventTeamHeadersLoading: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeadersLoading,
      }
    ),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamsPanel extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;
  public readonly userInfo: IUserInfo;
  public readonly userEntity: IEntity;
  public readonly eventTeamHeaders: IEventTeamHeader[];
  public readonly eventTeamHeadersLoading: boolean;
  public readonly clubCompInfo: IClubCompInfo;

  @Prop() public readonly competition: ICompetitionSummaryPublic;

  public competitionInfo: ICompetitionInfo =
    competitionService.factoryCompetitionInfo();

  public eventTeamService: EventTeamService = new EventTeamService();

  public userEntities: IEntity[] = [];
  public userEntitiesForEvent: IEntity[] = [];
  public userEntitySelected: IEntity = configService.factoryEntity();
  public eventTeamHeadersInternal: IEventTeamHeader[] = [];
  public eventTeamHeadersFiltered: IEventTeamHeader[] = [];

  public eventTeamHeadersCanAddInternal: IEventTeamHeader[] = [];
  public eventTeamHeaderSelected: IEventTeamHeader =
    eventTeamService.factoryEventTeamHeader();
  public editCompEventTeam: ICompEventTeam =
    eventTeamService.factoryGetCompEventTeam(
      this.eventTeamHeaderSelected,
      this.userEntity
    );

  public showFilters = true;
  public filterTerm: string = "";

  public filterOptions = {
    showSimpleFilter: true,
    showAdvancedFilter: false,
  };

  public eventTeamHeaderFilter: IEventTeamHeaderFilterV2 = {
    freeText: "",
    eventName: "",
    gender: "ALL",
    ageGroup: {
      id: 0,
      name: "ALL",
    },
    hasTeams: "ALL",
  };

  public showSection: EventTeamPanelSectionName = "HEADERS";
  public isLoading = false;
  public displayEventGrouping: "FLAT" | "GROUP" = "FLAT";
  public teamsEnteredMessage = "";

  public mapSectionLinks: Record<
    EventTeamPanelSectionName,
    ISectionLink<EventTeamPanelSectionName>
  > = {
    HEADERS: {
      iconId: "H",
      uniqueDesc: "HEADERS",
      title: "My Teams",
    },
    CREATE: {
      iconId: "C",
      uniqueDesc: "CREATE",
      title: "Select Event",
    },
    // HEADER_TEAMS: {
    //   iconId: "T",
    //   uniqueDesc: "HEADER_TEAMS",
    //   title: "Entries",
    // },
    EDIT_TEAM: {
      iconId: "E",
      uniqueDesc: "EDIT_TEAM",
      title: "Team Details",
    },
  };

  public created() {
    this.eventTeamHeadersInternal = simpleClone(this.eventTeamHeaders);
    this.doFilter();
  }

  public sectionClick(section: EventTeamPanelSectionName | "CREATE") {
    // if (section === "CREATE") {
    //   this.showSection = "HEADERS";
    //   return;
    // }
    if (section === "HEADERS") {
      //  reset any team you prev` selected
      this.eventTeamHeaderSelected = eventTeamService.factoryEventTeamHeader();
    }
    this.showSection = section;
  }

  @Watch("eventTeamHeaders")
  public onEventTeamHeadersChanged(newValue: IEventTeamHeader[]) {
    this.eventTeamHeadersInternal = simpleClone(newValue);
    this.doFilter();
    if (this.getEntryCount.teams === 0) {
      // this.showSection = "CREATE";
    }
    this.setTeamsEnteredMessage();
  }

  @Watch("competition", { immediate: true })
  public onCompetitionChanged(
    newValue: ICompetitionSummaryPublic,
    oldValue: ICompetitionSummaryPublic
  ) {
    if (newValue && newValue.compId && newValue.compId > 0) {
      //  mucking abut because of legacy crap
      const compInfo = simpleClone(newValue);
      compInfo.id = newValue.compId;
      this.competitionInfo = compInfo as any as ICompetitionInfo;

      this.reloadTeamEvents();
    }
  }

  @Watch("userInfo", { immediate: true })
  public onUserInfoChanged(newValue: IUserInfo) {
    const userEntities = configService.getEntitiesFromUserInfo(newValue);
    // this.userEntities = configService.getEntitiesFromUserInfo(newValue);
    if (userEntities.length === 1 && userEntities[0].id > 0) {
      this.userEntitySelected = simpleClone(userEntities[0]);
    }
    this.userEntities = simpleClone(userEntities);
  }

  @Watch("clubCompInfo")
  public onClubCompInfoChanged(newValue: IEventTeamHeader[]) {
    this.setTeamsEnteredMessage();
  }

  public getEntitiesForEvent(eventTeamHeader: IEventTeamHeader): IEntity[] {
    if (
      eventTeamService.doesUserHaveAccessToThisEvent(
        this.userInfo,
        eventTeamHeader
      )
    ) {
      const isSchool = competitionService.isSchool(
        this.competition as any as ICompetitionInfo
      );
      return eventTeamService.getEntitiesForEvent(
        this.userInfo,
        isSchool,
        eventTeamHeader
      );
    }
    return [configService.factoryEntity()];
  }

  public onUserEntitySelected(entity: IEntity) {
    this.userEntitySelected = { ...entity };
  }

  public get areAnyEventsSecure() {
    return eventTeamService.getTeamEventsHaveSecurity(this.eventTeamHeaders);
  }

  public reloadTeamEvents() {
    //  Why?!?!?   what on the comp has changed?
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ENTRYFORM_SET_COMPETITION,
      this.competitionInfo
    );
    this.$store
      .dispatch(
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
          "/" +
          COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS,
        { compId: this.competition.compId }
      )
      .then(() => {
        //  If a team has been added/edited, then need to load that specific
        //  header from fresh server data.
        if (this.eventTeamHeaderSelected.id > 0) {
          const eventTeamHeader = this.eventTeamHeaders.find((evtHeader) => {
            return evtHeader.id === this.eventTeamHeaderSelected.id;
          });
          if (eventTeamHeader) {
            this.eventTeamHeaderSelected = simpleClone(eventTeamHeader);
          }
        }
      });
  }

  public get getSchedInfo() {
    const schedInfoDetails: ISchedInfoDetail[] = [];

    const schedInfoDetail: ISchedInfoDetail = {
      title: "",
      body:
        this.competition &&
        this.competition.compId > 0 &&
        this.competition.options.helpText &&
        this.competition.options.helpText.teams
          ? this.competition.options.helpText.teams
          : "",
    };

    schedInfoDetails.push(schedInfoDetail);

    // const userAccessMessage: string = eventTeamService.userAccessMessage(this.userInfo);
    //
    // if (userAccessMessage.length > 0 ) {
    //     const schedInfoDetailAccess: ISchedInfoDetail = {
    //         title: "User Access",
    //         body: userAccessMessage
    //     };
    //     schedInfoDetails.push(schedInfoDetailAccess);
    // }

    const schedInfo: ISchedInfo = {
      title: "Team Events",
    } as ISchedInfo;
    schedInfo.schedInfoDetails = schedInfoDetails;
    schedInfo.autoExpand = true;
    schedInfo.shortDescription = "";
    schedInfo.showLinks = false;
    return schedInfo;
  }

  public setFilterText(filterText: string) {
    this.eventTeamHeaderFilter.freeText = filterText;
    this.doFilter();
  }

  public setFilter(payload: {
    options: IEventTeamHeaderFilterV2;
    startSearch: boolean;
  }) {
    console.log("EventTeamsPanelV2.setFilter", payload);
    this.eventTeamHeaderFilter = simpleClone(payload.options);

    if (payload.startSearch) {
      this.doFilter();
    }
  }

  public resetFilter() {
    const eventTeamsFilterService: EventTeamsFilterService =
      new EventTeamsFilterService();
    this.eventTeamHeaderFilter =
      eventTeamsFilterService.factoryEventTeamHeaderFilterV2();
    this.doFilter();
  }

  public doFilter() {
    const result = filterEventTeamHeadersV2(
      this.eventTeamHeaderFilter,
      this.eventTeamHeaders
    );
    console.log("EventTeamsPanelV2.doFilter", result);
    this.eventTeamHeadersFiltered = result;
  }

  public get getFilterCount(): number {
    const isFreeTextOn = this.eventTeamHeaderFilter.freeText.length > 0 ? 1 : 0;
    const isEventNameOn =
      this.eventTeamHeaderFilter.eventName.length > 0 ? 1 : 0;
    const isGenderOn = this.eventTeamHeaderFilter.gender !== "ALL" ? 1 : 0;
    const isAgeGroupOn = this.eventTeamHeaderFilter.ageGroup.id !== 0 ? 1 : 0;
    const isHasTeamsOn = this.eventTeamHeaderFilter.hasTeams !== "ALL" ? 1 : 0;

    return (
      isFreeTextOn + isEventNameOn + isGenderOn + isAgeGroupOn + isHasTeamsOn
    );
  }

  public get getEnteredTeams(): IEventTeamHeader[] {
    console.log(
      "EventTeamsPanelV2.getEnteredTeams",
      this.eventTeamHeadersFiltered
    );
    // Filters will have been applied to the base set of event teams.
    const filteredTeams = this.eventTeamHeadersFiltered;

    // We are only displaying teams where the user has some teams entered.
    return filteredTeams.filter((eventTeamHeader) => {
      return eventTeamHeader.compEventTeams.length > 0;
    });
  }

  public get getEnteredTeamsGrouped(): Record<string, IEventTeamHeader[]> {
    const eventsWithEntries = this.eventTeamHeaders.filter(
      (eventTeamHeader) => {
        return eventTeamHeader.compEventTeams.length > 0;
      }
    );

    const groupedByEventName = convertArrayToObjectArray(
      "eventName",
      eventsWithEntries
    );
    return groupedByEventName;
  }

  // public onFilterChangeCanAdd(eventTeamHeaders: IEventTeamHeader[]) {
  //   this.eventTeamHeadersCanAddInternal = simpleClone(eventTeamHeaders);
  // }

  // public get getShowFilter(): boolean {
  //   return this.eventTeamHeaders.length > 0 && this.showFilters;
  // }

  // public selfServiceSubmitted() {
  //   this.$store.commit(
  //     ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
  //       "/" +
  //       ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ATHLETE_TRIGGER_REFRESH
  //   );
  // }

  public get getTeamEventsHaveSecurity(): boolean {
    return eventTeamService.getTeamEventsHaveSecurity(this.eventTeamHeaders);
  }

  public setEventTeamHeaderSelected(eventTeamHeader: IEventTeamHeader) {
    this.eventTeamHeaderSelected = simpleClone(eventTeamHeader);
    this.showSection = "HEADERS";
  }

  public contactOrganiser() {
    this.$emit("contactOrganiser");
  }

  // public addTeam() {
  //   if (this.eventTeamHeadersCanAddInternal.length !== 1) {
  //     return;
  //   }
  //   const compEventTeam = eventTeamService.factoryGetCompEventTeam(
  //     this.eventTeamHeadersCanAddInternal[0],
  //     this.userEntity
  //   );
  //   this.editTeam(compEventTeam);
  // }

  public addTeamNew(eventTeamHeader: IEventTeamHeader) {
    this.eventTeamHeaderSelected = simpleClone(eventTeamHeader);
    const compEventTeam = eventTeamService.factoryGetCompEventTeam(
      eventTeamHeader,
      this.userEntity
    );
    compEventTeam.teamName = eventTeamService.getTeamName(
      eventTeamHeader,
      this.userEntitySelected,
      hasClubCompInfoCompetition(this.competition.clubCompInfo)
        ? this.competition.clubCompInfo
        : null
    );
    this.editTeam(compEventTeam);
  }

  public editTeamNew(
    eventTeamHeaderShowTeamsV2EmitEdit: EventTeamHeaderShowTeamsV2EmitEdit
  ) {
    this.setEventTeamHeaderSelected(
      eventTeamHeaderShowTeamsV2EmitEdit.eventTeamHeader
    );
    this.editCompEventTeam = simpleClone(
      eventTeamHeaderShowTeamsV2EmitEdit.compEventTeam
    ) as ICompEventTeam;
    this.showSection = "EDIT_TEAM";
  }

  public editTeam(compEventTeam: ICompEventTeam) {
    this.editCompEventTeam = simpleClone(compEventTeam);
    this.showSection = "EDIT_TEAM";
  }

  public submitDefault(compEventTeam: ICompEventTeam) {
    const compEventTeamData: CompEventTeamData = new CompEventTeamData();
    const compEventTeamStripped: ICompEventTeam =
      eventTeamService.stripEventCompEventTeam(compEventTeam);
    console.log(compEventTeam);
    this.submitTeam(compEventTeamStripped, compEventTeamData);
  }

  public submitSchool(compEventTeam: ICompEventTeamSchool) {
    const schoolTeamData: SchoolTeamData = new SchoolTeamData();
    this.submitTeam(compEventTeam, schoolTeamData);
  }

  public submitLeague(compEventTeam: ICompEventTeam) {
    // const leagueTeamData: LeagueTeamData = new LeagueTeamData();
    // this.submitTeam(compEventTeam, leagueTeamData);
    this.submitDefault(compEventTeam);
  }

  public reloadCart() {
    this.$store.dispatch(
      ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
        "/" +
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
    );
  }

  public reloadClubInfo() {
    if (
      ClubCompInfoService.hasClubCompInfoCompetition(
        this.clubCompInfo as IClubCompInfo
      )
    ) {
      this.$store.dispatch(
        ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
          "/" +
          ENTRY_STORE_CONST.ENTRY_STORE_ACTIONS_GET_CLUB_COMP_INFO,
        { compId: this.competition.compId }
      );
    }
  }

  public submitTeam(
    compEventTeam: ICompEventTeamBase,
    resourceData: ResourceData<ICompEventTeamBase>
  ) {
    let prom;
    this.isLoading = true;
    if (compEventTeam.id > 0) {
      prom = resourceData.update(compEventTeam);
    } else {
      compEventTeam.entityLevel = this.userEntitySelected.entityLevel;
      compEventTeam.entityId = this.userEntitySelected.id;
      prom = resourceData.create(compEventTeam);
    }
    prom
      .then((response: IServerResponse<ICompEventTeamBase>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
          return;
        }
        messageDispatchHelper(
          response.message && response.message.length > 0
            ? response.message
            : "Team " + compEventTeam.teamName + " saved to Order Summary",
          USER_MESSAGE_LEVEL.INFO
        );
        // this.closeAddCompEventTeam();
        // this.reloadTeamEvents();
        // this.reloadCart();
        // this.reloadClubInfo();
        this.reloadData();
      })
      .catch((error: any) => {
        messageDispatchHelper("Error: " + error, USER_MESSAGE_LEVEL.ERROR);
        return;
      })
      .finally(() => {
        this.isLoading = false;
        this.showSection = "HEADERS";
      });
  }

  public deleteTeam(compEventTeam: ICompEventTeam) {
    const compEventTeamData: CompEventTeamData = new CompEventTeamData();

    if (!this.isAdmin && this.configApp.userId !== compEventTeam.userId) {
      messageDispatchHelper(
        "Team " +
          compEventTeam.teamName +
          " not deleted, entered by: " +
          compEventTeam.userName,
        USER_MESSAGE_LEVEL.WARN
      );
      return false;
    }

    this.isLoading = true;
    const prom = compEventTeamData.delete(compEventTeam.id);

    handleResponseMessages(prom);

    return prom
      .then((response: IServerResponse<ICompEventTeam>) => {
        this.isLoading = false;
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
          return;
        }
        messageDispatchHelper(
          "Team " + compEventTeam.teamName + " deleted.",
          USER_MESSAGE_LEVEL.INFO
        );
        // this.reloadTeamEvents();
        // this.reloadCart();
        this.reloadData();
        return;
      })
      .finally(() => {
        this.isLoading = false;
        this.showSection = "HEADERS";
      });
  }

  public reloadData() {
    this.reloadTeamEvents();
    this.reloadCart();
    this.reloadClubInfo();
  }

  public get getCanAddTeam() {
    if (this.eventTeamHeaderSelected.id === 0) {
      return false;
    }
    const eventTeamHeader = this.eventTeamHeaderSelected;

    const teamLimits = eventTeamService.getTeamLimits(
      eventTeamHeader,
      this.userEntitySelected,
      this.configApp.userInfo
    );

    return eventTeamService.canAddTeamFromTeamLimits(
      eventTeamHeader,
      teamLimits
    );
  }

  public get getEntryCount() {
    // const data = this.eventTeamHeaders.reduce<{
    //   events: Record<string, string>;
    //   teams: number;
    // }>(
    //   (accum, eventTeamHeader) => {
    //     if (
    //       eventTeamHeader.compEventTeams.length > 0 &&
    //       !accum.events[eventTeamHeader.eventName]
    //     ) {
    //       accum.events[eventTeamHeader.eventName] = "";
    //     }
    //     accum.teams += eventTeamHeader.compEventTeams.length;
    //     return accum;
    //   },
    //   {
    //     events: {},
    //     teams: 0,
    //   }
    // );
    const data = this.getEventTeamEntryCount(this.eventTeamHeaders);

    return {
      events: Object.keys(data.events).length,
      teams: data.teams,
    };
  }

  public getEventTeamEntryCount(eventTeamHeaders: IEventTeamHeader[]): {
    events: number;
    teams: number;
  } {
    const data = eventTeamHeaders.reduce<{
      events: Record<string, string>;
      teams: number;
    }>(
      (accum, eventTeamHeader) => {
        if (
          eventTeamHeader.compEventTeams.length > 0 &&
          !accum.events[eventTeamHeader.eventName]
        ) {
          accum.events[eventTeamHeader.eventName] = "";
        }
        accum.teams += eventTeamHeader.compEventTeams.length;
        return accum;
      },
      {
        events: {},
        teams: 0,
      }
    );

    return {
      events: Object.keys(data.events).length,
      teams: data.teams,
    };
  }

  public cancelEditTeam() {
    // if (this.eventTeamHeaderSelected.compEventTeams.length === 0) {
    //   this.showSection = "HEADERS";
    //   return;
    // }
    this.showSection = "HEADERS";
  }

  public get isClubCompInfoCompetition() {
    return ClubCompInfoService.isClubCompInfoCompetition(
      this.competition.clubCompInfo
    );
  }

  public get getTeamsEnteredMessage() {
    if (
      ClubCompInfoService.isClubCompInfoCompetition(
        (this.competition as any as ICompetitionSummaryPublic).clubCompInfo
      )
    ) {
      const entityName = this.competition.clubCompInfo.configData.clubName;

      return (
        entityName +
        ": " +
        ClubCompInfoService.userMessageTeams(
          this.competition.clubCompInfo,
          this.eventTeamHeadersInternal
        )
      );
    }

    return this.getEntryCount.teams > 0
      ? this.getEntryCount.teams + " teams entered"
      : "No teams entered";
  }

  public setTeamsEnteredMessage() {
    if (
      ClubCompInfoService.isClubCompInfoCompetition(
        (this.competition as any as ICompetitionSummaryPublic).clubCompInfo
      )
    ) {
      this.teamsEnteredMessage = ClubCompInfoService.userMessageTeams(
        this.clubCompInfo,
        this.eventTeamHeadersInternal
      );
      return;
    }

    this.teamsEnteredMessage =
      this.getEntryCount.teams > 0
        ? this.getEntryCount.teams + " teams entered"
        : "No teams entered";
  }

  public get displayWhichFormType() {
    return this.eventTeamService.displayWhichFormType(
      this.eventTeamHeaderSelected
    );
  }
}
</script>
