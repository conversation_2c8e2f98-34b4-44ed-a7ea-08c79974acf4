<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <!--    <CardGenericV2 v-if="showSection === 'EXISTING'">-->
    <!--      <template slot="all">-->
    <!--    <SectionThickLine />-->
    <div class="e4s-flex-column e4s-full-width e4s-gap--standard">
      <div
        class="
          e4s-flex-row
          e4s-gap--standard
          e4s-input--label
          e4s-justify-flex-row-vert-center
        "
      >
        <!--        Name:-->
        <!--        <img :src="require('../../../../images/icons8-team-24.png')" />-->
        <img
          :src="require('../../../../images/icons8-team-32.png')"
          style="height: 20px; width: 20px"
        />

        <div v-text="compEventTeam.teamName"></div>
      </div>

      <hr class="dat-e4s-hr-small" />

      <template
        v-if="eventTeamHeader.ceoptions.eventTeam.formType === 'DEFAULT'"
      >
        <div
          class="e4s-flex-column"
          v-for="(athlete, index) in getTeamAthletes"
        >
          <div class="e4s-flex-row e4s-justify-flex-space-between">
            <div class="e4s-flex-row e4s-gap--standard">
              <div v-text="getTeamPositionNameEditMode(index)"></div>
              <div v-text="athlete.firstName"></div>
              <div v-text="athlete.surName"></div>
            </div>

            <div v-text="getAthleteDescriptorRight(athlete)"></div>
          </div>

          <hr class="dat-e4s-hr-only dat-e4s-hr--lighter" />
        </div>
      </template>

      <DisplayTeamLeague
        :form-rows="compEventTeam.formRows"
        v-if="eventTeamHeader.ceoptions.eventTeam.formType === 'LEAGUE'"
      />

      <DisplayTeamSchool
        :event-team-header="eventTeamHeader"
        :athletes="compEventTeam.athletes"
        v-if="eventTeamHeader.ceoptions.eventTeam.formType === 'SCHOOL'"
      />

      <!--LEAGUE-->
      <!--      <div-->
      <!--        class="e4s-flex-column"-->
      <!--        v-for="(athlete, index) in eventTeamHeader.formRows"-->
      <!--        v-if="eventTeamHeader.ceoptions.eventTeam.teamType === 'LEAGUE'"-->
      <!--      >-->
      <!--        <div class="e4s-flex-row e4s-justify-flex-space-between">-->
      <!--          <div class="e4s-flex-row e4s-gap&#45;&#45;standard">-->
      <!--            <div v-text="getTeamPositionNameEditMode(index)"></div>-->
      <!--            <div v-text="athlete.firstName"></div>-->
      <!--            <div v-text="athlete.surName"></div>-->
      <!--          </div>-->

      <!--          <div v-text="getAthleteDescriptorRight(athlete)"></div>-->
      <!--        </div>-->

      <!--        <hr class="dat-e4s-hr-only" />-->
      <!--      </div>-->
      <!--/LEAGUE-->

      <!--      <hr class="dat-e4s-hr-small" />-->

      <div v-if="compEventTeam.id > 0" class="e4s-flex-row">
        <p
          v-if="compEventTeam.entity && compEventTeam.entity.entityLevel > 0"
          class="e4s-subheader--general e4s-subheader--500"
          v-text="compEventTeam.entity.name"
        ></p>

        <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
          <div class="e4s-flex-row e4s-gap--small e4s-subheader--general">
            Entered by: <span v-text="compEventTeam.userName"></span>
          </div>

          <div v-if="showDeleteConfirmation" class="e4s-flex-column">
            <span class="e4s-info-text--error"
              >Are you sure you would like to delete?</span
            >
            <div class="e4s-flex-row e4s-flex-row--end">
              <div class="e4s-flex-row e4s-gap--small e4s-justify-flex-end-x">
                <ButtonGenericV2
                  class="e4s-button--60 e4s-button--slim"
                  text="cancel"
                  button-type="tertiary"
                  v-on:click="showDeleteConfirmation = false"
                />
                <!--On base team object-->
                <ButtonGenericV2
                  class="e4s-button--60 e4s-button--slim"
                  text="OK"
                  button-type="destructive"
                  v-on:click="deleteCompEventTeam"
                />
              </div>
            </div>
          </div>

          <div
            v-if="!showDeleteConfirmation"
            class="e4s-flex-row e4s-gap--standard"
          >
            <PrimaryLink link-text="Edit" @onClick="doEdit" />
            <PrimaryLink
              style="color: var(--red-600)"
              v-if="canDelete"
              link-text="Delete"
              @onClick="showDeleteConfirmation = true"
            />
            <!--            <ButtonGenericV2-->
            <!--              class="e4s-button&#45;&#45;60 e4s-button&#45;&#45;slim"-->
            <!--              text="Delete"-->
            <!--              button-type="destructive"-->
            <!--              v-if="canDelete"-->
            <!--              v-on:click="showDeleteConfirmation = true"-->
            <!--            />-->
            <!--            <ButtonGenericV2-->
            <!--              class="e4s-button&#45;&#45;60 e4s-button&#45;&#45;slim"-->
            <!--              text="Edit"-->
            <!--              button-type="secondary"-->
            <!--              v-on:click="doEdit"-->
            <!--            />-->
          </div>
        </div>
      </div>
    </div>
    <!--      </template>-->
    <!--    </CardGenericV2>-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { EventTeamService } from "../../event-team-service";
import { CompetitionService } from "../../../../competition/competiton-service";
import { AthleteService } from "../../../../athlete/athlete-service";
import { ConfigService } from "../../../../config/config-service";
import {
  ENTRY_STORE_CONST,
  IEntryStoreState,
} from "../../../../entry/entry-store";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import EventTeamBase from "../../event-team-base";
import { ICompetitionInfo } from "../../../../competition/competition-models";
import {
  ICompEventTeam,
  IEventTeamFormField,
  IEventTeamHeader,
} from "../../event-teams-models";
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue";
import FormGenericInputTextV2 from "../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { IUserMessage } from "../../../../user-message/user-message-models";
import { IAthleteSummary } from "../../../../athlete/athlete-models";
import { IConfigApp, IEntity } from "../../../../config/config-app-models";
import { IEventTeam } from "../../../athletecompsched-models";
import EventAthleteEligibleV2 from "./event-athlete-eligible-v2.vue";
import ButtonGenericBackV2 from "../../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import AthleteTypeAheadV2 from "../athlete-type-ahead-v2.vue";
import { simpleClone } from "../../../../common/common-service-utils";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import PrimaryLink from "../../../../common/ui/layoutV2/href/PrimaryLink.vue";
import SectionThickLine from "../../../../common/ui/layoutV2/form/SectionThickLine.vue";
import DisplayTeamLeague from "./league/display-team-league.vue";
import DisplayTeamSchool from "./school/display-team-school.vue";

const eventTeamService: EventTeamService = new EventTeamService();
const competitionService: CompetitionService = new CompetitionService();
const athleteService: AthleteService = new AthleteService();
// const athleteData: AthleteData = new AthleteData();
const configService: ConfigService = new ConfigService();

// type SectionName = "NEW" | "EXISTING" | "EDIT";
type SectionName = "EXISTING";

@Component({
  name: "event-team-default-v2",
  components: {
    DisplayTeamSchool,
    DisplayTeamLeague,
    SectionThickLine,
    PrimaryLink,
    FormGenericInputTemplateV2,
    AthleteTypeAheadV2,
    ButtonGenericBackV2,
    EventAthleteEligibleV2,
    ButtonGenericV2,
    FormGenericInputTextV2,
    CardGenericV2,
  },
  computed: {
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedCompetition: (state: IEntryStoreState) =>
        state.entryForm.selectedCompetition,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
      userEntityStore: (state: IConfigStoreState) => state.userEntity,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamDefault extends EventTeamBase {
  public readonly selectedCompetition: ICompetitionInfo;
  public readonly userEntityStore: IEntity;
  public readonly configApp: IConfigApp;
  public readonly eventTeamHeaders: IEventTeamHeader[];
  public readonly isAdmin: boolean;

  @Prop()
  public eventTeamHeader: IEventTeamHeader;

  @Prop({
    required: true,
  })
  public compEventTeamProp: ICompEventTeam;

  @Prop({ default: false })
  public readonly isLoading: boolean;

  @Prop({
    default: () => {
      return configService.factoryEntity();
    },
  })
  public readonly userEntity: IEntity;

  @Prop({ default: false })
  public readonly isNew: boolean;

  public compEventTeam: ICompEventTeam = simpleClone(this.compEventTeamProp);
  // public compEventTeam: ICompEventTeam;
  public showEligibility: boolean = false;
  public showAddAthleteDialog: boolean = false;

  public athleteForAddButtonToShow = 0;
  public athleteForAdd = athleteService.factoryGetAthlete();
  public athleteForAddIndex: number = -1;

  // public showSection: SectionName = this.isNew ? "NEW" : "EXISTING";
  public showSection: SectionName = "EXISTING";

  public created() {
    console.log("EventTeamDefault.created() userEntity: ", this.userEntity);
    // this.compEventTeam = simpleClone(this.compEventTeamProp);
  }

  // public mounted() {
  //   this.initForm();
  // }

  public initForm() {
    this.compEventTeam = simpleClone(this.compEventTeamProp);
    this.compEventTeam.ceid = this.eventTeamHeader.id;
    this.compEventTeam.athletes = eventTeamService.setUpAthleteArray(
      this.eventTeamHeader,
      this.compEventTeam
    );
    this.setClubSchoolForAthleteAdd(this.userEntity);
  }

  @Watch("compEventTeamProp")
  public onCompEventTeamProp(newValue: ICompEventTeam) {
    this.compEventTeam = simpleClone(newValue);
    this.reset();
  }

  @Watch("userEntity")
  public onUserEntityPropChanged(newValue: IEntity) {
    this.setClubSchoolForAthleteAdd(newValue);
  }

  public setClubSchoolForAthleteAdd(entity: IEntity) {
    const athlete = athleteService.factoryGetAthlete();
    if (this.isSchool) {
      athlete.schoolid = entity.id;
      athlete.school = entity.name;
    } else {
      athlete.clubid = entity.id;
      athlete.club = entity.name;
    }
    athlete.aocode = this.configApp.defaultao.code;
    this.athleteForAdd = simpleClone(athlete);
  }

  public reset() {
    this.editingLine = -1;
    this.hasBeenEdited = false;
    this.editTeamName = false;
    this.showDeleteConfirmation = false;
    this.initForm();
  }

  public get getAthleteSlots(): number {
    const eventTeam: IEventTeam = eventTeamService.getEventTeamOptions(
      this.eventTeamHeader
    );
    return eventTeam ? eventTeam.max : 0;
  }

  public get isSchool() {
    return competitionService.isSchool(this.competition);
  }

  // public get allowAddAthletes(): boolean {
  //   return this.isSchool;
  // }

  // public addAthlete(index: number) {
  //   this.showAddAthleteDialog = true;
  //   this.athleteForAddIndex = index;
  // }

  // public onAddAthleteSubmit(athlete: IAthlete) {
  //   console.log("EventTeamDefault.onAddAthleteSubmit()", athlete);
  //   this.showAddAthleteDialog = false;
  //
  //   const athleteSearch: IAthleteSearch =
  //     athleteService.factoryGetAthleteSearch();
  //   athleteSearch.athleteid = athlete.id;
  //   messageDispatchHelper(
  //     "Validating athlete.",
  //     USER_MESSAGE_LEVEL.INFO.toString()
  //   );
  //   athleteData
  //     .findAthletes(this.competition.id, 0, 1, 10, "surname", athleteSearch)
  //     .then((response: IServerPagingResponseList<IAthleteSummary>) => {
  //       console.log(
  //         "onAddAthleteSubmit() athleteData.findAthletes()",
  //         response
  //       );
  //       if (response.errNo > 0) {
  //         messageDispatchHelper(
  //           response.error,
  //           USER_MESSAGE_LEVEL.ERROR.toString()
  //         );
  //         return null;
  //       }
  //       if (response.data.length !== 1) {
  //         messageDispatchHelper(
  //           "A unique record with same demographics could not be found, please use search box to add name.",
  //           USER_MESSAGE_LEVEL.ERROR.toString()
  //         );
  //         return null;
  //       }
  //       return response.data[0];
  //     })
  //     .then((athleteSummary: IAthleteSummary | null) => {
  //       if (!athleteSummary) {
  //         return;
  //       }
  //       return athleteData
  //         .checkAthleteEventEligible(athleteSummary.id, this.eventTeamHeader.id)
  //         .then((response: IServerGenericResponse) => {
  //           if (response.errNo > 0) {
  //             messageDispatchHelper(
  //               response.error,
  //               USER_MESSAGE_LEVEL.ERROR.toString()
  //             );
  //             return;
  //           }
  //           const message = response.data;
  //           messageDispatchHelper(message, USER_MESSAGE_LEVEL.INFO.toString());
  //           if (message.indexOf("is eligible for this event") > -1) {
  //             const position = this.athleteForAddIndex - 1;
  //             this.setAthlete(athleteSummary, position);
  //           }
  //           return;
  //         })
  //         .catch((error) => {
  //           messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
  //           return;
  //         });
  //     })
  //     .catch((error) => {
  //       messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
  //       return;
  //     });
  // }
  //
  // public onAddAthleteCancel() {
  //   this.showAddAthleteDialog = false;
  // }

  // public athleteSelected(payload: {
  //   autoCompleteValue: IAutoCompleteValue;
  //   position: number;
  // }) {
  //   this.userMessages = [];
  //   console.log("CompEventTeam.athleteSelected", payload);
  //   if (payload.autoCompleteValue.value) {
  //     const athlete: IAthleteSummary = payload.autoCompleteValue.value;
  //     this.setAthlete(athlete, payload.position);
  //   }
  // }

  public athleteSelected(payload: {
    athlete: IAthleteSummary;
    position: number;
  }) {
    this.userMessages = [];
    console.log("CompEventTeam.athleteSelected", payload);
    this.setAthlete(payload.athlete, payload.position);
  }

  public athleteRowSelected(index: number) {
    console.log("athleteRowSelected() index: " + index);
    this.athleteForAddButtonToShow = index;
  }

  public setAthlete(athlete: IAthleteSummary, position: number) {
    const result: IUserMessage[] = eventTeamService.canAddAthlete(
      this.eventTeamHeader,
      this.compEventTeam,
      athlete,
      position
    );
    this.userMessages = result;
    const athletes = R.clone(this.compEventTeam.athletes);
    athletes[position] = R.clone(athlete);
    this.compEventTeam.athletes = athletes;
    this.onFieldChanged();
    this.editLine(-1);
  }

  public athleteRemoved(position: number) {
    this.userMessages = [];
    console.log("CompEventTeam.athleteRemoved: " + position);
    const athletes = R.clone(this.compEventTeam.athletes);
    athletes[position] = this.athleteService.factoryGetAthlete();
    this.compEventTeam.athletes = athletes;
    this.onFieldChanged();
  }

  public get getTeamPositionName() {
    return eventTeamService.getOptionsTeamPositionName(this.eventTeamHeader);
  }

  public get getTeamPositionSubName() {
    return eventTeamService.getOptionsTeamSubstituteName(this.eventTeamHeader);
  }

  public getTeamPositionNameReadMode(index: number) {
    const position: number = index + 1;
    return this.getTeamPositionName.length > 0
      ? this.getTeamPositionName + " " + position
      : position;
  }

  public getTeamPositionNameEditMode(index: number) {
    const position: number = index + 1;
    const isOptional: boolean =
      position > eventTeamService.getOptionsMin(this.eventTeamHeader);
    if (isOptional) {
      const substituteName = eventTeamService.getOptionsTeamSubstituteName(
        this.eventTeamHeader
      );
      // return (
      //   (substituteName === "" ? "Substitute" : substituteName) + " (Optional)"
      // );
      return substituteName === "" ? "Substitute" : substituteName;
    } else {
      return this.getTeamPositionNameReadMode(index);
    }
  }

  public getAthleteDescriptorRight(athlete: IAthleteSummary): string {
    return eventTeamService.getAthleteDescriptorRightV2(
      athlete,
      this.eventTeamHeader.ceoptions.eventTeam
    );
  }

  public editLine(index: number) {
    this.editingLine = index;
    this.hasBeenEdited = true;
  }

  public get canShowEligibility() {
    return eventTeamService.getOptionsMin(this.eventTeamHeader) > 0;
  }

  public get getTeamAthletes() {
    return this.compEventTeam.athletes.filter((athlete) => {
      return athlete.id > 0;
    });
  }

  public get getAgeGroupIds(): number[] {
    return eventTeamService.getOptionsAgeGroupIdsForAthleteSearch(
      this.eventTeamHeader
    );
  }

  public get canDelete() {
    if (this.isAdmin) {
      return true;
    }
    if (competitionService.isClosed(this.selectedCompetition.entriesClose)) {
      return false;
    }

    if (!this.isUserOwner) {
      return false;
    }
    // return this.compEventTeam.id > 0 && !this.showDeleteConfirmation;
    return this.compEventTeam.id > 0;
  }

  // public submitCompEventTeam() {
  //   console.log("CompEventTeam.submitCompEventTeam()");
  //   this.userMessages = [];
  //   const userMessages: IUserMessage[] = eventTeamService.validateCompEventTeam(
  //     this.eventTeamHeader,
  //     this.compEventTeam,
  //     this.competition,
  //     this.eventTeamHeaders
  //   );
  //   if (userMessages.length > 0) {
  //     this.userMessages = userMessages;
  //     return;
  //   }
  //
  //   this.$emit("submit", this.compEventTeam);
  // }

  // public cancelEdit() {
  //   if (this.compEventTeam.id == 0) {
  //     this.$emit("cancel");
  //     return;
  //   }
  //   this.showSection = "EXISTING";
  // }

  public getTeamNamesSimple(compEventTeam: ICompEventTeam): string[] {
    return eventTeamService.getAthleteNames(compEventTeam);
  }

  public cancel() {
    this.$emit("cancel");
  }

  public eventFormChange(formInfo: IEventTeamFormField[]) {
    this.compEventTeam.formInfo = formInfo;
  }

  public doEdit() {
    this.$emit("edit", simpleClone(this.compEventTeam));
  }

  // public doAdd() {
  //   this.$emit("add", simpleClone(this.compEventTeam));
  // }
}
</script>

<style scoped>
.e4s-name-edit {
  margin-left: 1rem !important;
}
</style>
