<template>
    <auto-complete-mat
            :field-label="getFieldLabel"
            :custom="getCustomForAthleteAutoComplete"
            :data = "athletes"
            iconClassName=""
            :placeholder="getFieldPlaceHolder"
            :is-loading="isLoadingAthletes"
            :user-input-preload="getAthleteDefaultName"
            v-on:onUserFocus="onUserFocus"
            v-on:searchTermChanged="athleteSearchTermChanged"
            v-on:autoSelectionMade="onAthleteSelected">
    </auto-complete-mat>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {AthleteData} from "../../athlete/athlete-data";
    import {
        IAthleteSearch,
        IAthleteSummary,
    } from "../../athlete/athlete-models";
    import {AthleteService} from "../../athlete/athlete-service";
    import {debounce} from "../../common/debounce";
    import AutoCompleteMat from "../../common/ui/autocomplete/auto-complete-mat.vue";
    import {ICompetition} from "../../competition/competition-models";
    import {EventTeamService} from "./event-team-service";
    import {IEventTeamHeader} from "./event-teams-models";
    import {IAutoCompleteValue, ICustom} from "../../common/ui/autocomplete/auto-complete-mat-models";
    import {IEntity} from "../../config/config-app-models";
    import { ConfigService } from "../../config/config-service";
    import {IServerPagingResponseList} from "../../common/common-models";

    const configService: ConfigService = new ConfigService();

    @Component({
        name: "comp-event-name-picker",
        components: {
            "auto-complete-mat": AutoCompleteMat
        }
    })
    export default class CompEventNamePicker extends Vue {
        public athleteService: AthleteService = new AthleteService();

        @Prop() public readonly position: number;
        @Prop() public readonly competition: ICompetition;
        @Prop() public readonly eventTeamHeader: IEventTeamHeader;
        @Prop({default: ""}) public readonly teamPositionName: string;
        @Prop({
            default: () => {
                return {
                    id: 0,
                    URN: "",
                    aocode: "",
                    firstName: "",
                    surName: ""
                } as IAthleteSummary;
            }
        }) public readonly athleteDefault: IAthleteSummary;
        @Prop({
            default: true
        }) public readonly showAllAthletes: boolean;
        @Prop({
            default: () => {
                return configService.factoryEntity();
            }
        }) public readonly userEntity: IEntity;
        @Prop({
            default: 0
        }) public readonly eventId: number;

        public eventTeamService: EventTeamService = new EventTeamService();
        public athletes: IAthleteSummary[] = [] as IAthleteSummary[];
        public athleteData: AthleteData = new AthleteData();
        public isLoadingAthletes: boolean = false;
        public selectedAthlete: IAthleteSummary = this.athleteService.factoryGetAthlete();
        public searchText: string = "";
        public searchTextEntered: string = "";

        public debounceSearch: any;

        public mounted() {
            console.log("CompEventNamePicker.mounted()...");
            this.debounceSearch =  debounce((
                id: number,
                teamId: number,
                pageNumber: number,
                pageSize: number,
                orderByProperty: string,
                athleteSearch: IAthleteSearch
            ) => {

                this.athleteData.findAthletes(this.competition.id, teamId, pageNumber, pageSize, orderByProperty, athleteSearch)
                    .then((response: IServerPagingResponseList<IAthleteSummary>) => {
                        this.athletes = R.clone(    response.data as IAthleteSummary[]);
                        this.isLoadingAthletes = false;
                    });
            }, 100);
        }

        @Watch("athleteDefault")
        public athletePropChanged(newValue: IAthleteSummary) {
            // this.athletes = R.clone([newValue]);
            // this.selectedAthlete = this.athletes[0];
        }


        @Watch("searchText")
        public searchTextChanged(newValue: string) {
            console.log("CompEventNamePicker.searchTextChanged()...newValue: " + newValue);
        }

        public athleteSearchTermChanged(searchKey: string) {
            console.log("CompEventNamePicker.athleteSearchTermChanged()...searchKey: >" + searchKey + "<");
            if (searchKey.length === 0) {
                console.log("CompEventNamePicker.athleteSearchTermChanged()...searchKey: >" + searchKey + "<  DO NOT SEARCH");
                this.$emit("athleteRemoved", this.position);
                return;
            }

            this.searchTextEntered = searchKey;
            const ageGroupIds = this.eventTeamService.getOptionsAgeGroupIdsForAthleteSearch(this.eventTeamHeader);

            const athleteSearch: IAthleteSearch = this.athleteService.factoryGetAthleteSearch();
            athleteSearch.search = searchKey;
            athleteSearch.gender = this.eventTeamHeader.gender;
            athleteSearch.ageGroupId = ageGroupIds.join(",");
            athleteSearch.ceid = this.eventTeamHeader.id;
            athleteSearch.showAllAthletes = this.showAllAthletes ? "1" : "0";
            athleteSearch.eventId = this.eventId;

            if (this.userEntity.id > 0) {
                // @ts-ignore
                athleteSearch[this.userEntity.entityName.toLowerCase()] = this.userEntity.name;
            }

            this.isLoadingAthletes = true;
            const teamId = 0;
            const pageNumber = 1;
            const pageSize = 5;
            const orderByProperty = "surname";

            this.debounceSearch(this.competition.id, teamId, pageNumber, pageSize, orderByProperty, athleteSearch);
        }

        public getLabelForAthleteAutoComplete(athlete: IAthleteSummary): string {
           return this.eventTeamService.getLabelForAthleteAutoComplete(athlete);
        }

        public get getCustomForAthleteAutoComplete(): ICustom {
            return {
                dropDownLabelFunc: this.getLabelForAthleteAutoComplete
            } as ICustom;
        }

        public get getFieldLabel() {
            // return "Athlete " + (this.teamPositionName.length  > 0 ? this.teamPositionName  + " " : "") + (this.position + 1);
            return this.teamPositionName;
        }

        public get getFieldPlaceHolder() {
            return "Enter URN, name...";
        }

        public get getAthleteDefaultName() {
            if (this.athleteDefault.firstName.length > 0 || this.athleteDefault.surName.length > 0) {
                // return this.athleteDefault.firstName + " " + this.athleteDefault.surName;
                return this.eventTeamService.getLabelForAthleteAutoComplete(this.athleteDefault);
            }
            return "";
        }

        public onAthleteSelected(autoCompleteValue: IAutoCompleteValue) {
            // console.log("CompEventNamePicker.athleteSelected", autoCompleteValue);
            this.$emit("athleteSelected", {autoCompleteValue, position: this.position});
        }

        public onUserFocus() {
            this.$emit("onUserFocus", this.position);
        }

        // public onAthleteSelected(athlete: IAthleteSummary) {
        //     // console.log("CompEventNamePicker.athleteSelected", autoCompleteValue);
        //     const autoCompleteValue: IAutoCompleteValue = {
        //         value: athlete
        //     } as IAutoCompleteValue;
        //     this.$emit("athleteSelected", {autoCompleteValue, position: this.position});
        // }

        public get getPlaceholder() {
            return this.selectedAthlete && (this.selectedAthlete.id >  0) ? "" : "Enter athlete name";
        }
    }

</script>

<style scoped>
    .IZ-select__input {
        box-shadow: 0 0px 0px 0px rgba(0,0,0,.2), 0 0px 0px 0 rgba(0,0,0,.14), 0 0px 0px 0 rgba(0,0,0,.12) !important;
    }
</style>
