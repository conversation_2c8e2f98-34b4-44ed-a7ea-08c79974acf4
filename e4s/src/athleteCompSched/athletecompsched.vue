<template>
    <div>
        <div v-if="isLoadingInternal" class="schedule-loading-wrapper">
            <loading-spinner></loading-spinner>
        </div>
        <div class="schedule-grid-wrapper">
            <events-card-grid
                    :isLoading="isLoading"
                    :compEvents="eventsProcessed"
                    :selected-athlete="selectedAthlete"
                    :selected-competition="selectedCompetition"
                    :events-server-response="eventsServerResponse"
                    v-on:onSelectedEventGrid="onSelectedGrid"
                    v-on:openShop="openShop"
            >
            </events-card-grid>
        </div>
    </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import {Prop, Watch} from "vue-property-decorator";
import {mapState} from "vuex";

import {IAthlete} from "../athlete/athlete-models";
import {AthleteService} from "../athlete/athlete-service";
import LoadingSpinner from "../common/ui/loading-spinner.vue";
import {ICompetition, ICompetitionInfo} from "../competition/competition-models"
import AgeInfo from "./ageinfo.vue";
import {
    IAgeInfo,
    IAthleteCompSchedResponse,
    IAthleteCompSchedRuleEvent,
    ICompShedRuleOptions,
    IPbKey
} from "./athletecompsched-models";
import {AthleteCompSchedService} from "./athletecompsched-service";
import CompRules from "./comp-rules.vue";
import EventsCardGrid from "./events-card-grid.vue";
import {ATH_COMP_SCHED_STORE_CONST, IAthCompSchedStoreState} from "./store/athletecompsched-store";
import {ENTRY_STORE_CONST, IEntryStoreState} from "../entry/entry-store";

@Component({
    name: "athlete-comp-sched-grid",
    computed: {
        ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME,
            {
                eventsSelected: (state: IAthCompSchedStoreState) => state.eventsSelected,
                eventsProcessed: (state: IAthCompSchedStoreState) => state.eventsProcessed,
                isLoading: (state: IAthCompSchedStoreState) => state.eventsLoading,
                eventsServerResponse: (state: IAthCompSchedStoreState) => state.eventsServerResponse
            }
        ),
        ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
            selectedAthlete: (state: IEntryStoreState) => state.entryForm.selectedAthlete,
            selectedCompetition: (state: IEntryStoreState) => state.entryForm.selectedCompetition
        })
    },
    components: {
        "loading-spinner": LoadingSpinner,
        "events-card-grid": EventsCardGrid,
        "comp-rules": CompRules,
        "ageinfo": AgeInfo
    }
})
export default class AthleteCompSchedGrid extends Vue {
    public readonly isLoading: boolean;
    public readonly eventsServerResponse: IAthleteCompSchedResponse;
    public readonly eventsProcessed: IAthleteCompSchedRuleEvent[];
    public readonly eventsSelected: IAthleteCompSchedRuleEvent[];
    public readonly selectedCompetition: ICompetitionInfo;
    public readonly selectedAthlete: IAthlete;

    @Prop() public competition: ICompetition;
    @Prop() public athlete: IAthlete;

    public edited: boolean = false;
    public isLoadingInternal: boolean = false;
    public athleteCompSchedService: AthleteCompSchedService = {} as AthleteCompSchedService;
    public athleteService: AthleteService = {} as AthleteService;
    public pbKey: IPbKey = {} as IPbKey;
    public athleteCompSchedResponse: IAthleteCompSchedResponse = {} as IAthleteCompSchedResponse;
    public ageInfo: IAgeInfo = {
        ageGroup: "",
        currentAge: 0,
        vetAgeGroup: ""
    } as IAgeInfo;
    public compShedRuleOptions: ICompShedRuleOptions = {} as ICompShedRuleOptions;

    public athleteCompSchedRuleEvents: IAthleteCompSchedRuleEvent[] = [];

    public mounted() {
        this.athleteCompSchedService = new AthleteCompSchedService();
        this.athleteService = new AthleteService();
    }

    @Watch("competition")
    public onCompetitionChanged(newValue: ICompetition, oldValue: ICompetition) {
        if (newValue.id === oldValue.id) {
            return;
        }
        this.getAthleteCompSched(newValue.id, this.athlete.id);
    }

    @Watch("athlete")
    public onAthleteChanged(athleteNew: IAthlete, athleteOld: IAthlete) {
        if (athleteNew.id === athleteOld.id) {
            return;
        }
        this.getAthleteCompSched(this.competition.id, athleteNew.id);
    }

    @Watch("isLoading")
    public onIsLoadingChanged(newValue: boolean) {
        console.log("..............AthleteCompSchedGrid.isLoading: " + newValue);
    }

    public getAthleteCompSched(competitionId: number, athleteId: number) {

        if (competitionId === undefined || athleteId === undefined ) {
            return;
        }
        if (this.edited) {
            //  TODO reloading, you will lsoe any current chnages.
            //  e.g. this.userMessage()...which will allow user to continue, set edited = false;
        }
        this.$store.dispatch(
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_INITIALISE_EVENTS
        );

    }

    public onSelectedGrid(compEvent: IAthleteCompSchedRuleEvent) {
        this.edited = true; //  If user browses away, detect here.

        // const isEventAlreadySelected = compEvent.userSelected;
        // const athleteCompSchedData: AthleteCompSchedData = new AthleteCompSchedData();

        if (!this.athleteCompSchedService.isEventAvailableForClicking(compEvent)) {
            console.log("onRowSelected()" + compEvent.ceid + " - " + compEvent.eventid + "...CAN'T");
            return;
        }
        console.log("onRowSelected()" + compEvent.ceid + " - " + compEvent.eventid + "...CAN...userSelected: " + compEvent.userSelected);

        // if (this.athleteCompSchedService.isEventOverSubscribed(compEvent) ) {
        //     //  We can do this from schedule sent up.  If any changes to schedule
        //     //  happen while user "sits" at their screen, response will indicate this.
        //     this.showOverSubscribed = true;
        //     return;
        // }

        this.proceedWithSelection(compEvent);

        // if (isEventAlreadySelected) {
        //
        //     this.isLoadingInternal = true;
        //     this.$store.dispatch(
        //         ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
        //         ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_REMOVE_FROM_SELECTED_NEW,
        //         {
        //             compEvent: compEvent
        //         }
        //     )
        //         .finally(() => {
        //             this.isLoadingInternal = false;
        //         });
        //
        // } else {
        //     this.isLoadingInternal = true;
        //     this.$store.dispatch(
        //         ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
        //         ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SELECTED,
        //         {
        //             event: compEvent
        //         }
        //     )
        //         .then(() => {
        //             //  Until other parts of local state get moved to.
        //             this.recalc(compEvent)
        //                 .then( () => {
        //                     this.isLoadingInternal = false;
        //                 });
        //         });
        // }
    }

    public proceedWithSelection(compEvent: IAthleteCompSchedRuleEvent) {
        this.edited = true; //  If user browses away, detect here.
        const isEventAlreadySelected = compEvent.userSelected;

        if (isEventAlreadySelected) {

            this.isLoadingInternal = true;
            this.$store.dispatch(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_REMOVE_FROM_SELECTED_NEW,
                {
                    compEvent: compEvent
                }
            )
                .finally(() => {
                    this.isLoadingInternal = false;
                });

        } else {
            this.isLoadingInternal = true;
            this.$store.dispatch(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SELECTED,
                {
                    event: compEvent
                }
            )
                .then(() => {
                    //  Until other parts of local state get moved to.
                    this.recalc(compEvent)
                        .then( () => {
                            this.isLoadingInternal = false;
                        });
                });
        }
    }

    public recalc(compEvent: null | IAthleteCompSchedRuleEvent) {
        console.log("recalc()" + (compEvent ? compEvent.userSelected : "undefined"));
        return this.$store.dispatch(
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
            ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_PROCESS_EVENTS,
            {
                compEvent
            }
        );
    }

    public openShop() {
        this.$emit("openShop");
    }

}
</script>
