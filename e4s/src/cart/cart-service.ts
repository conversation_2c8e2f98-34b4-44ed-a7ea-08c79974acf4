import * as R from "ramda";
import {IAthleteCompSchedRuleEvent} from "../athletecompsched/athletecompsched-models";
import {ICartEvent} from "./cart-models";
import {AthleteCompSchedService} from "../athletecompsched/athletecompsched-service";

const athleteCompSchedService: AthleteCompSchedService = new AthleteCompSchedService();

export class CartService {

    public factoryCartEvent(): ICartEvent {
        return {
            ...athleteCompSchedService.factoryAthleteCompSchedRuleEvent(),
            prodId: 0
        };
    }

    public factoryEventRulesToCartEvents = (events: IAthleteCompSchedRuleEvent[]): ICartEvent[] => {
        return events.map(this.factoryEventRuleToCartEvent);
    }

    public factoryEventRuleToCartEvent = (event: IAthleteCompSchedRuleEvent): ICartEvent => {
        return {
            ...event,
            prodId: -1
        };
    }

    public sortCart(prop: string, cartEvents: ICartEvent[]): ICartEvent[] {
        const sortCart: any = R.sortWith([
            R.ascend( R.prop( prop ) as any )
        ]) as any;
        return sortCart(cartEvents) as ICartEvent[];
    }

    public getPaidToggleMessage(cartEvent: ICartEvent): string {
        return cartEvent.paid ? "Set Not Paid" : "Set Paid";
    }

}
