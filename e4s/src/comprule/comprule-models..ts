import { IAgeGroup } from "../agegroup/agegroup-models";
import { IEventSummary } from "../event/event-models";

export interface ICompUnique {
  ids: number[]; //  TODO split this "16,17,14,15"
  events: IEventSummary[];
  text: string; //  ui description of rule "Only 1 of 800m, 1500m"
  type: string; //  DAY/ COMP
}

export interface ICompRuleOptions {
  maxEvents: number; //  Max events in 1 day.
  maxField: number; //  Max number of field events an athlete can do, currently across comp as not had a multi day.
  maxTrack: number; //  Max number of track events the athlete can do ( see maxField note ).
  maxCompEvents: number; //  Max events across competition
  maxCompField: number; //  Max Field events across competition
  maxCompTrack: number; //  Max Track events across competition
  unique: ICompUnique[]; //  Same as event unique, disable any others with id's listed.
  maxExcludedEvents: number; //  What is the max number of events that a user can enter that are
  //  excluded from "count". @see eoptions\ ceoptions.excludeFromCntRule
  type: ""; //  Not used
  displayMaxDayFields: boolean;
  maxTeamEvents: number; //  Max number of team events an athlete can enter. 0 = no limit
}

export interface ICompRule {
  id: number;
  compId: number;
  options: ICompRuleOptions;
  ageGroup: IAgeGroup;
  ageGroups: IAgeGroup[];
}
