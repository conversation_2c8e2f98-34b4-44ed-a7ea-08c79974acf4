<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-form-header">Comp Rule</div>
      </div>
    </div>

    <RulesAgeGroups
      v-if="displaySelectAgeGroup"
      :rule-base="compRule"
      :age-groups="ageGroups"
      v-on:onAgeGroupsSelected="ageGroupsSelected"
    >
    </RulesAgeGroups>

    <div class="row" v-if="!displaySelectAgeGroup">
      <div class="input-field col s10 m10 l10">
        <label class="active" :for="PREFIX + 'age-group'">Age Group</label>
        <span v-text="getCompRuleName" :id="PREFIX + 'age-group'"></span>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputNumberV2
              form-label="Max events an athlete can enter"
              help-text=""
              v-model="compRule.options.maxCompEvents"
            />

            <FormGenericInputNumberV2
              form-label="Max field events an athlete can enter"
              help-text=""
              v-model="compRule.options.maxCompField"
            />
          </template>
        </FormGenericFieldGridV2>
      </div>

      <!--      <div class="input-field col s10 m6 l6">-->
      <!--        <input-->
      <!--          :id="PREFIX + 'max-comp-events'"-->
      <!--          :name="PREFIX + 'max-comp-events'"-->
      <!--          type="number"-->
      <!--          v-model.number="compRule.options.maxCompEvents"-->
      <!--          placeholder="Max track events an athlete can enter"-->
      <!--        />-->
      <!--        <label class="active" :for="PREFIX + 'max-comp-events'"-->
      <!--          >Max Comp Events</label-->
      <!--        >-->
      <!--      </div>-->

      <!--      <div class="input-field col s12 m6 6">-->
      <!--        <input-->
      <!--          :id="PREFIX + 'max-comp-field'"-->
      <!--          :name="PREFIX + 'max-comp-field'"-->
      <!--          type="number"-->
      <!--          v-model.number="compRule.options.maxCompField"-->
      <!--          placeholder="Max field events an athlete can enter"-->
      <!--        />-->
      <!--        <label class="active" :for="PREFIX + 'max-comp-field'"-->
      <!--          >Max Comp Field</label-->
      <!--        >-->
      <!--      </div>-->
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputNumberV2
              form-label="Max track events an athlete can enter"
              help-text=""
              v-model="compRule.options.maxCompTrack"
            />

            <FormGenericInputNumberV2
              form-label="Max number of events not counted towards comp total."
              help-text=""
              v-model="compRule.options.maxExcludedEvents"
            />
          </template>
        </FormGenericFieldGridV2>

        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputNumberV2
              form-label="Max team events an athlete can enter"
              help-text=""
              v-model="compRule.options.maxTeamEvents"
            />

            <div></div>
          </template>
        </FormGenericFieldGridV2>
      </div>

      <!--      <div class="input-field col s10 m6 l6">-->
      <!--        <input-->
      <!--          :id="PREFIX + 'max-comp-track'"-->
      <!--          :name="PREFIX + 'max-comp-track'"-->
      <!--          type="number"-->
      <!--          v-model.number="compRule.options.maxCompTrack"-->
      <!--          placeholder="Max track events an athlete can enter"-->
      <!--        />-->
      <!--        <label class="active" :for="PREFIX + 'max-comp-track'"-->
      <!--          >Max Comp Track</label-->
      <!--        >-->
      <!--      </div>-->

      <!--      <div class="input-field col s12 m6 6">-->
      <!--        <input-->
      <!--          :id="PREFIX + 'max-excl'"-->
      <!--          :name="PREFIX + 'max-excl'"-->
      <!--          type="number"-->
      <!--          v-model.number="compRule.options.maxExcludedEvents"-->
      <!--          placeholder="Max number of events not counted towards comp total."-->
      <!--        />-->
      <!--        <label class="active" :for="PREFIX + 'max-excl'">Max Exclude</label>-->
      <!--      </div>-->

      <!--      <div class="input-field col s10 m6 l6">-->
      <!--        <input-->
      <!--          :id="PREFIX + 'max-team-events'"-->
      <!--          :name="PREFIX + 'max-team-events'"-->
      <!--          type="number"-->
      <!--          v-model.number="compRule.options.maxTeamEvents"-->
      <!--          placeholder="Max team events an athlete can enter"-->
      <!--        />-->
      <!--        <label class="active" :for="PREFIX + 'max-track'"-->
      <!--          >Max team events an athlete can enter</label-->
      <!--        >-->
      <!--      </div>-->
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <InputCheckboxV2
          class="e4s-align-self-flex-start"
          v-model="compRule.options.displayMaxDayFields"
          value-label="Is multi day comp?'"
        />
      </div>

      <!--      <div class="input-field col s10 m6 l6">-->
      <!--        <p>-->
      <!--          <label>-->
      <!--            <input-->
      <!--              class="e4s-checkbox"-->
      <!--              type="checkbox"-->
      <!--              v-model="compRule.options.displayMaxDayFields"-->
      <!--            />-->
      <!--            <span v-html="'Is multi day comp?'"></span>-->
      <!--          </label>-->
      <!--        </p>-->
      <!--      </div>-->
    </div>

    <div class="row" v-show="compRule.options.displayMaxDayFields">
      <div class="col s12 m12 l12">
        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputNumberV2
              form-label="Max events an athlete can enter"
              help-text=""
              v-model="compRule.options.maxEvents"
            />

            <FormGenericInputNumberV2
              form-label="Max field events an athlete can enter"
              help-text=""
              v-model="compRule.options.maxField"
            />
          </template>
        </FormGenericFieldGridV2>
      </div>

      <!--      <div class="input-field col s10 m6 l6">-->
      <!--        <input-->
      <!--          :id="PREFIX + 'max-events'"-->
      <!--          :name="PREFIX + 'max-events'"-->
      <!--          type="number"-->
      <!--          v-model.number="compRule.options.maxEvents"-->
      <!--          placeholder="Max events an athlete can enter"-->
      <!--        />-->
      <!--        <label class="active" :for="PREFIX + 'max-events'">Max Events</label>-->
      <!--      </div>-->

      <!--      <div class="input-field col s12 m6 6">-->
      <!--        <input-->
      <!--          :id="PREFIX + 'max-field'"-->
      <!--          :name="PREFIX + 'max-field'"-->
      <!--          type="number"-->
      <!--          v-model.number="compRule.options.maxField"-->
      <!--          placeholder="Max field events an athlete can enter"-->
      <!--        />-->
      <!--        <label class="active" :for="PREFIX + 'max-field'">Max Field</label>-->
      <!--      </div>-->
    </div>

    <div class="row" v-show="compRule.options.displayMaxDayFields">
      <div class="col s12 m12 l12">
        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputNumberV2
              form-label="Max track events an athlete can enter"
              help-text=""
              v-model="compRule.options.maxTrack"
            />

            <div></div>
          </template>
        </FormGenericFieldGridV2>
      </div>

      <!--      <div class="input-field col s10 m6 l6">-->
      <!--        <input-->
      <!--          :id="PREFIX + 'max-track'"-->
      <!--          :name="PREFIX + 'max-track'"-->
      <!--          type="number"-->
      <!--          v-model.number="compRule.options.maxTrack"-->
      <!--          placeholder="Max track events an athlete can enter"-->
      <!--        />-->
      <!--        <label class="active" :for="PREFIX + 'max-track'">Max Track</label>-->
      <!--      </div>-->
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-form-header">Unique Event Rules</div>
        <ButtonGenericV2
          text="Add"
          @click="addUnique"
          v-if="!displayUniqueForm"
        />
      </div>
    </div>

    <div class="row" v-if="displayUniqueForm">
      <div class="col s12 m12 l12">
        <comp-rule-unique
          class="e4s-form-wrapper"
          v-on:onCancel="onCompRuleUniqueCancel"
          v-on:onSubmit="onCompRuleUniqueAdded"
        >
        </comp-rule-unique>
      </div>
    </div>

    <div class="row">
      <div v-for="(option, index) in compRule.options.unique" :key="option.id">
        <div class="col s112 m4 l4">
          <span v-text="option.text"></span>
        </div>

        <div class="col s10 m6 l6">
          <span v-text="getCompRuleUniqueRowText(option)"></span>
        </div>

        <div class="col s2 m2 l2">
          <button
            class="btn xxx-btn-small btn-flat red-text e4s-bold"
            :disabled="showCompRuleForm"
            v-on:click.stop="removeCompRuleUnique(index)"
          >
            <span v-text="$t('buttons.x')"></span>
          </button>
        </div>
      </div>
    </div>

    <div class="e4s-flex-row" v-if="!displayUniqueForm">
      <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
        <ButtonGenericV2
          button-type="tertiary"
          text="Cancel"
          @click="onCancel"
        />
        <ButtonGenericV2 button-type="primary" text="Save" @click="onSubmit" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import UserValidationMessages from "../user-message/user-validation-messages.vue";
import { IValidationResult } from "../common/common-models";
import { AgeGroupService } from "../agegroup/agegroup-service";
import { IAgeGroup } from "../agegroup/agegroup-models";
import { ICompRule, ICompUnique } from "./comprule-models.";
import { CompRuleService } from "./comprule-service";
import AgeGroupSelect from "../agegroup/agegroup-select.vue";
import CompRuleUnique from "./comprule-unique.vue";
import ButtonsCancelSave from "../common/ui/buttons-cancel-save.vue";
import { Prop, Watch } from "vue-property-decorator";
import AgeGroupCheckBox from "../agegroup/agegroup-builder/checkbox/agegroup-checkbox.vue";
import RulesAgeGroups from "../builder/form/rules/rules-age-groups.vue";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FormGenericFieldGridV2 from "../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputNumberV2 from "../common/ui/layoutV2/form/form-generic--input-number-v2.vue";
import InputCheckboxV2 from "../common/ui/layoutV2/fields/input-checkbox-v2.vue";

const compRuleService: CompRuleService = new CompRuleService();
const ageGroupService: AgeGroupService = new AgeGroupService();

@Component({
  name: "comp-rule-form",
  components: {
    InputCheckboxV2,
    FormGenericInputNumberV2,
    FormGenericFieldGridV2,
    ButtonGenericV2,
    "age-group-select": AgeGroupSelect,
    "user-validation-messages": UserValidationMessages,
    "comp-rule-unique": CompRuleUnique,
    "buttons-cancel-save": ButtonsCancelSave,
    AgeGroupCheckBox,
    RulesAgeGroups,
  },
})
export default class CompRuleForm extends Vue {
  @Prop({ default: false }) public readonly isLoading: boolean;
  @Prop({
    default: () => {
      return compRuleService.factory();
    },
  })
  public readonly compRuleProp: ICompRule;
  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageGroups: IAgeGroup[];
  public compRuleService: CompRuleService = new CompRuleService();
  public PREFIX = Math.random().toString(36).substring(2);
  public ageGroupService: AgeGroupService = new AgeGroupService();
  public validationResults: IValidationResult[] = [];
  public compRule: ICompRule = compRuleService.factory();

  public displaySelectAgeGroup: boolean = true;
  public displayUniqueForm: boolean = false;
  // public displayMaxDayFields: boolean = false;

  public allAgeGroups: boolean = false;

  public created() {
    //  default to show if zero.
    this.compRule = R.clone(this.compRuleProp);
    // this.displaySelectAgeGroup = this.compRule.ageGroup.id === 0;
    this.init();
  }

  public get getCompRuleName() {
    return compRuleService.getCompRuleName(this.compRule);
  }
  //
  // public get canEditAgeGroup(): boolean {
  //     return this.compRule.id === 0;
  // }

  public init() {
    this.displaySelectAgeGroup = this.compRule.id === 0;
    if (typeof this.compRule.options.maxTeamEvents === "undefined") {
      this.compRule.options.maxTeamEvents = 0; //  No limit.
    }
  }

  @Watch("compRuleProp")
  public onCompRulePropChanged(newValue: ICompRule) {
    this.compRule = R.clone(newValue);
    this.init();
  }

  public onAllAgeGroupsChanged() {
    if (this.allAgeGroups) {
      this.compRule.ageGroups = [ageGroupService.factoryGetAgeGroup()];
    }
  }

  // public onAgeGroupSelected(ageGroup: IAgeGroup) {
  //     this.compRule.ageGroup = R.clone(ageGroup);
  //     this.displaySelectAgeGroup = false;
  // }

  public onCompRuleUniqueCancel() {
    this.displayUniqueForm = false;
  }

  public onCompRuleUniqueAdded(compRuleUnique: ICompUnique) {
    this.compRule.options.unique.push(R.clone(compRuleUnique));
    this.displayUniqueForm = false;
  }

  public getCompRuleUniqueRowText(compRuleUnique: ICompUnique): string {
    return compRuleService.getCompRuleUniqueRowText(compRuleUnique);
  }

  public removeCompRuleUnique(index: number) {
    const uniques: ICompUnique[] = R.clone(this.compRule.options.unique);
    this.compRule.options.unique = uniques.filter(
      (unique: ICompUnique, idx: number) => {
        return index !== idx;
      }
    );
  }

  public addUnique() {
    this.displayUniqueForm = true;
  }

  public ageGroupsSelected(ageGroups: IAgeGroup[]) {
    this.compRule.ageGroups = R.clone(ageGroups);
  }

  public reset() {
    this.compRule = this.compRuleService.factory();
  }

  public onCancel() {
    this.$emit("onCancel");
  }

  public onSubmit() {
    const compRule = R.clone(this.compRule);

    if (!compRule.options.displayMaxDayFields) {
      compRule.options.maxEvents = compRule.options.maxCompEvents;
      compRule.options.maxField = compRule.options.maxCompField;
      compRule.options.maxTrack = compRule.options.maxCompTrack;
    }

    this.$validator.validateAll().then((result) => {
      if (!result) {
        // do stuff if not valid.
        return;
      }
      this.validationResults = this.compRuleService.validate(compRule);
      if (this.validationResults.length > 0) {
        return;
      }
      this.$emit("onSubmit", R.clone(compRule));
    });
  }
}
</script>
