import * as R from "ramda";
import { CompRuleService } from "./comprule-service";
import { ICompRule } from "./comprule-models.";

describe("CompRuleService", () => {
  const compRuleService: CompRuleService = new CompRuleService();

  test("mapFromServer", () => {
    const compRule: ICompRule = {
      id: 9,
      compID: 141,
      options: {
        maxEvents: 1,
        maxField: 0,
        maxTrack: 0,
        maxCompEvents: 0,
        maxCompField: 0,
        maxCompTrack: 0,
        maxExcludedEvents: 0,
        type: "",
        unique: [
          {
            ids: [30, 31],
            text: "test",
          },
        ],
      },
      ageGroup: {
        id: 65,
        minAge: 10,
        minAtDay: 31,
        minAtMonth: 12,
        keyName: "Under 11 XC Eire",
        name: "Under 11",
        maxAge: 10,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
      },
    } as any as ICompRule;

    expect(R.isNil(compRule.options.unique[0].events)).toBe(true);
    const result: ICompRule = compRuleService.mapRuleFromServer(compRule);
    // console.log("", result.options.unique[0]);
    expect(R.isNil(result.options.unique[0].events)).toBe(false);
  });

  test("mapToServer", () => {
    const compRule: ICompRule = {
      id: 145,
      compID: 114,
      options: {
        maxCompEvents: 2,
        maxCompField: 0,
        maxCompTrack: 0,
        maxExcludedEvents: 0,
        displayMaxDayFields: false,
        maxTeamEvents: 5,
        unique: [],
        maxEvents: 2,
        maxTrack: 0,
        maxField: 0,
      },
      ageGroup: {
        id: 0,
        name: "All Age Groups",
        keyName: "All Age Groups",
        shortName: "All",
      },
    } as any as ICompRule;
    const ruleSummary = compRuleService.getCompRuleSummaryLabel(compRule);

    expect(ruleSummary).toBe(
      "Max Comp: 2 (Trk: 0, Fld: 0) Max Day: 2 (Trk: 0, Fld: 0) Max Exc: 0 Max Team: 5"
    );
  });
});
