<template>
  <div>
    <div v-show="!registrationSuccess">
      <div class="row">
        <div class="col s12 m12 l12">
          <label class="active" for="username">
            User name
            <FieldValidationLabel
              :validation-controller="validationController"
              :prop-path="'userName'"
            />
          </label>
          <input
            id="username"
            name="username"
            type="text"
            v-on:keyup.enter="doRegister"
            v-model="userName"
          />
        </div>
      </div>
      <div class="row">
        <div class="col s12 m12 l12">
          <label class="active" for="email">
            Email
            <FieldValidationLabel
              :validation-controller="validationController"
              :prop-path="'email'"
            />
          </label>
          <input
            id="email"
            name="email"
            type="email"
            v-on:keyup.enter="doRegister"
            v-model="email"
          />
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-bold">
            <div v-text="message"></div>
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-bold">
            <div>
              Registration confirmation will be emailed to:
              <span v-text="email"></span> If you do not receive this email,
              please check your Junk/Spam folder. Failing that, please email:
              <a href="mailto:<EMAIL>"
                ><EMAIL></a
              >
            </div>
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="right">
            <LoadingSpinner v-if="isLoading"></LoadingSpinner>
            <button
              class="btn waves-effect waves grey"
              v-on:click.prevent="doCancel"
              :disabled="isLoading"
            >
              Cancel
            </button>
            <button
              class="btn waves-effect waves green"
              v-on:click.prevent="doRegister"
              :disabled="isLoading"
            >
              Register
            </button>
          </div>
        </div>
      </div>
    </div>

    <div v-show="registrationSuccess">
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-bold">
            <a href="#/login">Proceed to login</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import { Prop } from "vue-property-decorator";
import { LoginData } from "../login-data";
import { ValidationService } from "../../validation/validation-service";
import FieldValidationLabel from "../../validation/validation-field-lable.vue";
import { ValidationController } from "../../validation/validation-controller";
import {
  IObjectKeyType,
  IServerGenericResponse,
} from "../../common/common-models";
import { IValidationProp } from "../../validation/validation-models";

const validationService: ValidationService = new ValidationService();

@Component({
  name: "register",
  components: {
    FieldValidationLabel,
  },
})
export default class Register extends Vue {
  @Prop({ default: "" }) public readonly redirectedFrom: string;

  public userName: string = "";
  public email: string = "";
  public message: string = "";
  public isLoading: boolean = false;
  public registrationSuccess: boolean = false;

  public validationController: ValidationController =
    new ValidationController();

  public doRegister() {
    this.validate();
    if (!this.validationController.isValid) {
      return;
    }

    const loginData: LoginData = new LoginData();
    this.message = "";
    this.isLoading = true;
    loginData
      .register(this.userName, this.email)
      .then((response: IServerGenericResponse) => {
        if (response.errNo > 0) {
          this.message = response.error;
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }

        this.registrationSuccess = true;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        this.message = "Error occurred during registration.";
        return;
      })
      .finally(() => {
        this.isLoading = false;
        this.$emit("onRegistered");
      });
  }

  public doCancel() {
    this.$emit("onCancel");
  }

  public validate() {
    let errors: IObjectKeyType<IValidationProp> = {};
    if (this.userName.length === 0) {
      errors = validationService.addMessage("userName", "Required", errors);
    }

    if (!validationService.isEmailValid(this.email)) {
      errors = validationService.addMessage(
        "email",
        "Valid email required",
        errors
      );
    }

    this.validationController.setErrors(errors);
  }
}
</script>
