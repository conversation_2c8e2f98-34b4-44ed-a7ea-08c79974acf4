import { IBase } from "../../common/common-models";
import { ICompEventSchedGrid } from "../compeventschedule/compeventschedule-models";

export enum BULK_EVENT_MOVE_ACTION {
  BLANK = "",
  INSERT = "INSERT",
  ADD = "ADD",
}

export interface IBulkEventMove extends IBase {
  ceids: number[];
  action: string;
  eventNo: number;
}

export type BulkActionType =
  | "PLEASE_SELECT"
  | "EDIT"
  | "EDIT_SECTION"
  | "EDIT_PARENT_SECTION"
  | "DELETE"
  | "DELETE_SECTION"
  | "ADD"
  | "INSERT"
  | "SELECT_ALL"
  | "DE_SELECT_ALL";

export interface IBulkAction {
  label: string;
  value: BulkActionType;
  requiresSelected: boolean;
  dropDown: boolean;
  eventsMustBeSame: boolean;
}

export const BULK_ACTIONS: Record<BulkActionType, IBulkAction> = {
  PLEASE_SELECT: {
    label: "Please Select",
    value: "PLEASE_SELECT",
    requiresSelected: true,
    dropDown: false,
    eventsMustBeSame: true,
  },
  EDIT: {
    label: "Edit",
    value: "EDIT",
    requiresSelected: true,
    dropDown: true,
    eventsMustBeSame: true,
  },
  EDIT_SECTION: {
    label: "Edit Section",
    value: "EDIT_SECTION",
    requiresSelected: false,
    dropDown: true,
    eventsMustBeSame: true,
  },
  EDIT_PARENT_SECTION: {
    label: "Edit Parent",
    value: "EDIT_PARENT_SECTION",
    requiresSelected: false,
    dropDown: false,
    eventsMustBeSame: true,
  },
  DELETE: {
    label: "Delete",
    value: "DELETE",
    requiresSelected: true,
    dropDown: true,
    eventsMustBeSame: false,
  },
  DELETE_SECTION: {
    label: "Delete Section",
    value: "DELETE_SECTION",
    requiresSelected: false,
    dropDown: true,
    eventsMustBeSame: false,
  },
  ADD: {
    label: "Add To",
    value: "ADD",
    requiresSelected: true,
    dropDown: true,
    eventsMustBeSame: false,
  },
  INSERT: {
    label: "Move To",
    value: "INSERT",
    requiresSelected: true,
    dropDown: true,
    eventsMustBeSame: false,
  },
  SELECT_ALL: {
    label: "Select all",
    value: "SELECT_ALL",
    requiresSelected: false,
    dropDown: false,
    eventsMustBeSame: false,
  },
  DE_SELECT_ALL: {
    label: "De-select all",
    value: "DE_SELECT_ALL",
    requiresSelected: false,
    dropDown: false,
    eventsMustBeSame: false,
  },
};

export type sortScheduleByProp = keyof Pick<
  ICompEventSchedGrid,
  "startDateTime" | "eventGroup" | "eventName" | "gender" | "eventNo"
>;

export interface ISortBy {
  id: number;
  label: string;
  value: sortScheduleByProp;
}

export type ScheduleSortByType =
  | "START_TIME"
  | "EVENT_NAME"
  | "GENDER"
  | "EVENT_NUMBER"
  | "EVENT_GROUP";

export const SORT_BY: Record<ScheduleSortByType, ISortBy> = {
  START_TIME: { id: 1, label: "Start Time", value: "startDateTime" },
  EVENT_NAME: { id: 2, label: "Event Name", value: "eventName" },
  GENDER: { id: 3, label: "Gender", value: "gender" },
  EVENT_NUMBER: { id: 4, label: "Event Number", value: "eventNo" },
  EVENT_GROUP: { id: 5, label: "Event Group", value: "eventGroup" },
};
