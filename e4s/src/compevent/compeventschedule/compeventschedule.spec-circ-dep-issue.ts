import * as R from "ramda";
import { CommonService } from "../../common/common-service";
import { ICompEventSchedGrid } from "./compeventschedule-models";
import { CompEventScheduleService } from "./compeventschedule-service";
import { ICompEvent, IEventGroupSummary } from "../compevent-models";
import { CompEventService } from "../compevent-service";
const compEventScheduleService: CompEventScheduleService =
  new CompEventScheduleService();
const compEventService = new CompEventService();
const commonService: CommonService = new CommonService();

// tslint:disable
const testSched1: ICompEventSchedGrid[] = [
  {
    id: 36773,
    startDateTime: "2019-06-29 11:00:00",
    sortDateTime: "2019-06-29 11:00:00",
    gender: "F",
    eventName: "100m",
    eventNo: -1,
    maxGroup: "01",
    isEditEnabled: true,
    isSelected: false,
    compEvent: {
      maxGroup: 1,
      split: 0,
      options: {
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: true,
          showEntryCount: true,
        },
        isTeamEvent: false,
        secondarySpend: { isParent: false, parentCeid: 0 },
        unique: [],
        ageGroups: [],
        helpText: "",
        xeText: "",
        xiText: "",
      },
      tf: "T",
      eoptions: { min: 9.6, max: 20, helpText: "" },
      id: 36773,
      compId: 200,
      isOpen: 1,
      maxAthletes: 0,
      startDateTime: "2019-06-29 11:00:00",
      sortDateTime: "2019-06-29 11:00:00",
      event: { id: 3, name: "100m", eventnameextra: null, gender: "F" },
      ageGroup: {
        id: 45,
        minAge: 8,
        minAtDay: 31,
        minAtMonth: 12,
        keyName: "Under 9 Eire",
        name: "Under 9",
        maxAge: 8,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
      },
      price: {
        fee: 0.59,
        salefee: 0.59,
        price: 0,
        saleprice: 5,
        saleenddate: null,
        options: { displayFee: false },
        id: 831,
        name: "Event",
        description: "",
      },
      multiEventInfo: { id: 0 },
      uom: {
        id: 1,
        type: "T",
        options: [{ pattern: "s.SS", text: "seconds", short: "s" }],
      },
    },
  },
  {
    id: 36774,
    startDateTime: "2019-06-29 11:15:00",
    sortDateTime: "2019-06-29 11:15:00",
    gender: "F",
    eventName: "200m",
    eventNo: -1,
    maxGroup: "02",
    isEditEnabled: true,
    isSelected: false,
    compEvent: {
      maxGroup: 2,
      split: 0,
      options: {
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: true,
          showEntryCount: true,
        },
        isteamEvent: false,
        secondarySpend: { isParent: false, parentCeid: 0 },
        unique: [],
        ageGroups: [],
        helpText: "",
        xeText: "",
        xiText: "",
      },
      tf: "T",
      eoptions: { min: 20, max: 60 },
      id: 36774,
      compId: 200,
      isOpen: 1,
      maxAthletes: 0,
      startDateTime: "2019-06-29 11:15:00",
      sortDateTime: "2019-06-29 11:15:00",
      event: { id: 5, name: "200m", eventnameextra: null, gender: "F" },
      ageGroup: {
        id: 45,
        minAge: 8,
        minAtDay: 31,
        minAtMonth: 12,
        keyName: "Under 9 Eire",
        name: "Under 9",
        maxAge: 8,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
      },
      price: {
        fee: 0.59,
        salefee: 0.59,
        price: 0,
        saleprice: 5,
        saleenddate: null,
        options: { displayFee: false },
        id: 831,
        name: "Event",
        description: "",
      },
      multiEventInfo: { id: 0 },
      uom: {
        id: 1,
        type: "T",
        options: [{ pattern: "s.SS", text: "seconds", short: "s" }],
      },
    },
  },
  {
    id: 36775,
    startDateTime: "2019-06-29 11:15:00",
    sortDateTime: "2019-06-29 11:15:00",
    gender: "F",
    eventName: "200m",
    eventNo: -1,
    maxGroup: "02",
    isEditEnabled: true,
    isSelected: false,
    compEvent: {
      maxGroup: 2,
      split: 0,
      options: {
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: true,
          showEntryCount: true,
        },
        isteamEvent: false,
        secondarySpend: { isParent: false, parentCeid: 0 },
        unique: [],
        ageGroups: [],
        helpText: "",
        xeText: "",
        xiText: "",
      },
      tf: "T",
      eoptions: { min: 20, max: 60 },
      id: 36775,
      compId: 200,
      isOpen: 1,
      maxAthletes: 0,
      startDateTime: "2019-06-29 11:15:00",
      sortDateTime: "2019-06-29 11:15:00",
      event: { id: 5, name: "200m", eventnameextra: null, gender: "F" },
      ageGroup: {
        id: 46,
        minAge: 9,
        minAtDay: 31,
        minAtMonth: 12,
        keyName: "Under 10 Eire",
        name: "Under 10",
        maxAge: 9,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
      },
      price: {
        fee: 0.59,
        salefee: 0.59,
        price: 0,
        saleprice: 5,
        saleenddate: null,
        options: { displayFee: false },
        id: 831,
        name: "Event",
        description: "",
      },
      multiEventInfo: { id: 0 },
      uom: {
        id: 1,
        type: "T",
        options: [{ pattern: "s.SS", text: "seconds", short: "s" }],
      },
    },
  },
  {
    id: 36776,
    startDateTime: "2019-06-29 11:15:00",
    sortDateTime: "2019-06-29 11:15:00",
    gender: "F",
    eventName: "200m",
    eventNo: -1,
    maxGroup: "02",
    isEditEnabled: true,
    isSelected: false,
    compEvent: {
      maxGroup: 2,
      split: 0,
      options: {
        rowOptions: {
          autoExpandHelpText: false,
          showPB: true,
          showPrice: true,
          showEntryCount: true,
        },
        isteamEvent: false,
        secondarySpend: { isParent: false, parentCeid: 0 },
        unique: [],
        ageGroups: [],
        helpText: "",
        xeText: "",
        xiText: "",
      },
      tf: "T",
      eoptions: { min: 20, max: 60 },
      id: 36776,
      compId: 200,
      isOpen: 1,
      maxAthletes: 0,
      startDateTime: "2019-06-29 11:15:00",
      sortDateTime: "2019-06-29 11:15:00",
      event: { id: 5, name: "200m", eventnameextra: null, gender: "F" },
      ageGroup: {
        id: 47,
        minAge: 10,
        minAtDay: 31,
        minAtMonth: 12,
        keyName: "Under 11 Eire",
        name: "Under 11",
        maxAge: 10,
        maxAtDay: 31,
        maxAtMonth: 12,
        maxAtYear: 0,
        minAtYear: 0,
      },
      price: {
        fee: 0.59,
        salefee: 0.59,
        price: 0,
        saleprice: 5,
        saleenddate: null,
        options: { displayFee: false },
        id: 831,
        name: "Event",
        description: "",
      },
      multiEventInfo: { id: 0 },
      uom: {
        id: 1,
        type: "T",
        options: [{ pattern: "s.SS", text: "seconds", short: "s" }],
      },
    },
  },
] as any as ICompEventSchedGrid[];

describe("CompEventScheduleService", () => {
  test("R.equals", () => {
    const objCompEvt = { height: 192, age: 50, showPb: true } as any;
    const objEvt = { name: "bob", age: 34, showPb: false } as any;

    let result = R.equals(objCompEvt, objEvt);
    expect(result).toBe(false);
    result = R.equals(
      {
        name: "bob",
        age: 34,
      },
      {
        name: "bob",
        age: 34,
      }
    );
    expect(result).toBe(true);

    result = R.equals(
      {
        name: "bob",
        age: 34,
      },
      {
        name: "bobx",
        age: 34,
      }
    );
    expect(result).toBe(false);

    result = R.equals(
      {
        name: "bob",
        age: 34,
        add: {
          line1: "the",
          line2: "street",
        },
      },
      {
        age: 34,
        name: "bob",
        add: {
          line2: "street",
          line1: "the",
        },
      }
    );
    expect(result).toBe(true);
  });

  test("memoizeWith", () => {
    const objEvt = { name: "bob", age: 34, showPb: false };
    // @ts-ignore
    let func = R.memoizeWith(R.identity, (obj) => {
      return obj.age;
    });
    let result = func(objEvt);
    expect(result).toBe(34);
    result = func(objEvt);
    expect(result).toBe(34);

    const objEvt2 = { name: "bob", age: 35, showPb: false };
    result = func(objEvt2);
    expect(result).toBe(34);
  });

  test("convertEventTypeEnumToArray", () => {
    const objCompEvt = { height: 192, age: 50, showPb: true };
    const objEvt = { name: "bob", age: 34, showPb: false };

    const result = R.merge(objCompEvt, objEvt);
    // console.log(".......mergeRight.......", result);
    expect(result.height).toBe(192);
    expect(result.age).toBe(34);
    expect(result.showPb).toBe(false);
  });

  test("sortSchedule", () => {
    const data = [
      {
        maxGroup: 60,
        split: 0,
        options: {
          xiText: "",
          xbText: "",
        },
        saleenddate: null,
        tf: "T",
        eoptions: {
          min: 6,
          max: 14,
        },
        id: 32449,
        compId: 141,
        isOpen: 1,
        ageGroupID: 47,
        maxAthletes: 0,
        startDateTime: "2019-05-26 11:00:00",
        sortDateTime: "2019-05-26 11:00:00",
        event: {
          name: "60m",
          id: 157,
          eventNameExtra: null,
          gender: "M",
        },
        price: {
          fee: 0.59,
          saleFee: 0.59,
          salePrice: 3.59,
          price: 3.59,
          options: {
            displayFee: true,
          },
          id: 735,
          name: "_std",
          description: "",
        },
        multiEventInfo: {
          id: 0,
        },
        uom: {
          id: 1,
          type: "T",
          options: [
            {
              pattern: "s.SS",
              text: "seconds",
              short: "s",
            },
          ],
        },
      },
      {
        maxGroup: 60,
        split: 0,
        options: {
          xiText: "",
          xbText: "",
        },
        saleenddate: null,
        tf: "T",
        eoptions: {
          min: 6,
          max: 14,
        },
        id: 32448,
        compId: 141,
        isOpen: 1,
        ageGroupID: 46,
        maxAthletes: 0,
        startDateTime: "2019-05-26 11:00:00",
        sortDateTime: "2019-05-26 11:00:00",
        event: {
          name: "60m",
          id: 157,
          eventNameExtra: null,
          gender: "F",
        },
        price: {
          fee: 0.59,
          saleFee: 0.59,
          salePrice: 3.59,
          price: 3.59,
          options: {
            displayFee: true,
          },
          id: 735,
          name: "_std",
          description: "",
        },
        multiEventInfo: {
          id: 0,
        },
        uom: {
          id: 1,
          type: "T",
          options: [
            {
              pattern: "s.SS",
              text: "seconds",
              short: "s",
            },
          ],
        },
      },
      {
        maxGroup: 60,
        split: 0,
        options: {
          xiText: "",
          xbText: "",
        },
        saleenddate: null,
        tf: "T",
        eoptions: {
          min: 6,
          max: 14,
        },
        id: 32447,
        compId: 141,
        isOpen: 1,
        ageGroupID: 45,
        maxAthletes: 0,
        startDateTime: "2019-05-26 09:00:00",
        sortDateTime: "2019-05-26 09:00:00",
        event: {
          name: "60m",
          id: 157,
          eventNameExtra: null,
          gender: "F",
        },
        price: {
          fee: 0.59,
          saleFee: 0.59,
          salePrice: 3.59,
          price: 3.59,
          options: {
            displayFee: true,
          },
          id: 735,
          name: "_std",
          description: "",
        },
        multiEventInfo: {
          id: 0,
        },
        uom: {
          id: 1,
          type: "T",
          options: [
            {
              pattern: "s.SS",
              text: "seconds",
              short: "s",
            },
          ],
        },
      },
    ] as any as ICompEvent[];

    const compEventsSchedGrid: ICompEventSchedGrid[] =
      compEventScheduleService.mapCompEventsToSchedule(R.clone(data));
    expect(compEventsSchedGrid[0].gender).toBe("M");

    let result = compEventScheduleService.sortSchedule(
      "gender",
      R.clone(compEventsSchedGrid)
    );

    expect(result.length).toBe(3);
    expect(result[0].gender).toBe("F");

    // result = compEventScheduleService.sortSchedule(
    //   "sortDateTime",
    //   R.clone(compEventsSchedGrid)
    // );
    //
    // expect(result.length).toBe(3);
    // expect(result[0].sortDateTime).toBe("2019-05-26 09:00:00");
    //
    // const result2: IObjectKeyTypeArray<ICompEventSchedGrid> =
    //   commonService.convertArrayToObjectArray("gender", compEventsSchedGrid);
    // // console.log("", result2);
    // expect((result2.F as any).length).toBe(2);
  });

  test("sortSchedule2", () => {
    const data = [
      {
        id: 32449,
        startDateTime: "2019-05-26 11:00:00",
        sortDateTime: "2019-05-26 11:00:00",
        event: {
          name: "70m",
          id: 357,
          gender: "M",
        },
      },
      {
        id: 12449,
        startDateTime: "2019-05-26 09:00:00",
        sortDateTime: "2019-05-26 09:00:00",
        event: {
          name: "60m",
          id: 157,
          gender: "M",
        },
      },
      {
        id: 22449,
        startDateTime: "2019-05-26 10:00:00",
        sortDateTime: "2019-05-26 10:00:00",
        event: {
          name: "50m",
          id: 257,
          gender: "F",
        },
      },
    ] as any as ICompEvent[];

    const compEventsSchedGrid: ICompEventSchedGrid[] =
      compEventScheduleService.mapCompEventsToSchedule(R.clone(data));
    expect(compEventsSchedGrid[0].gender).toBe("M");

    const result = compEventScheduleService.sortSchedule(
      "gender",
      R.clone(compEventsSchedGrid)
    );

    expect(result.length).toBe(3);
    // console.log("", result[0]);
    expect(result[0].gender).toBe("F");
    expect(result[0].id).toBe(22449);
    expect(result[1].id).toBe(12449);
  });

  test("getEditEnabledSchedule", () => {
    const data: ICompEventSchedGrid[] = R.clone(testSched1);
    expect(data.length).toBe(4);
    expect(data[0].isEditEnabled).toBe(true);
    const result = compEventScheduleService.getEditEnabledSchedule(
      data[1],
      data
    );
    expect(result[0].isEditEnabled).toBe(false);
    expect(result[1].isEditEnabled).toBe(true);
  });

  test("setSelected", () => {
    const data: ICompEventSchedGrid[] = R.clone(testSched1);
    expect(data.length).toBe(4);
    expect(data[0].isEditEnabled).toBe(true);

    let evt36775 = commonService.getObjectByIdFromArray(
      36775,
      data
    ) as ICompEventSchedGrid;
    expect(evt36775.id).toBe(36775);
    let setAsSelected: boolean = !evt36775.isSelected;
    let result = compEventScheduleService.setSelected(
      evt36775,
      setAsSelected,
      data,
      true
    );

    expect(evt36775.id).toBe(36775);
    expect(evt36775.isSelected).toBe(true);
    expect(evt36775.isEditEnabled).toBe(true);
    // const test3 = getByIdOther(result) as ICompEventSchedGrid;
    const evt36773 = commonService.getObjectByIdFromArray(
      36773,
      result
    ) as ICompEventSchedGrid;
    expect(evt36773.id).toBe(36773);
    expect(evt36773.isSelected).toBe(false);
    expect(evt36773.isEditEnabled).toBe(false);

    let evt36776 = commonService.getObjectByIdFromArray(
      36776,
      result
    ) as ICompEventSchedGrid;
    expect(evt36776.isSelected).toBe(false);
    expect(evt36776.isEditEnabled).toBe(true);

    setAsSelected = true;
    result = compEventScheduleService.setSelected(
      evt36776,
      setAsSelected,
      result,
      true
    );
    evt36776 = commonService.getObjectByIdFromArray(
      36776,
      result
    ) as ICompEventSchedGrid;
    expect(evt36776.isSelected).toBe(true);
    expect(evt36776.isEditEnabled).toBe(true);

    //  This should still be selected
    expect(evt36775.isSelected).toBe(true);
    expect(evt36775.isEditEnabled).toBe(true);
  });

  test("getAddToEventNumbers", () => {
    const data: ICompEvent[] = [
      {
        event: {
          id: 1,
          name: "PV",
        },
        maxGroup: "1",
      } as ICompEvent,
      {
        event: {
          id: 1,
          name: "PV",
        },
        maxGroup: "1",
      } as ICompEvent,
      {
        event: {
          id: 1,
          name: "PV",
        },
        maxGroup: "11",
      } as ICompEvent,
      {
        event: {
          id: 2,
          name: "SP",
        },
        maxGroup: "22",
      } as ICompEvent,
      {
        event: {
          id: 3,
          name: "Discus",
        },
        maxGroup: "33",
      } as ICompEvent,
    ];

    let selectedEvents = [
      {
        event: {
          id: 3,
          name: "Discus",
        },
        eventNo: -1,
      } as ICompEvent,
    ];
    let result = compEventScheduleService.getAddToEventNumbers(
      selectedEvents,
      data
    );
    // console.log("result", result);
    expect(result.length).toBe(1);
    expect(result[0]).toBe("033");

    selectedEvents = [
      {
        event: {
          id: 1,
          name: "PV",
        },
        maxGroup: "99",
      } as ICompEvent,
    ];
    result = compEventScheduleService.getAddToEventNumbers(
      selectedEvents,
      data
    );
    // console.log("result", result);
    expect(result.length).toBe(2);
    expect(result.indexOf("001") > -1).toBe(true);
    expect(result.indexOf("011") > -1).toBe(true);

    selectedEvents = [
      {
        event: {
          id: 1,
          name: "PV",
        },
        maxGroup: "11",
      } as ICompEvent,
    ];
    result = compEventScheduleService.getAddToEventNumbers(
      selectedEvents,
      data
    );
    // console.log("result", result);
    expect(result.length).toBe(1);
    expect(result.indexOf("001") > -1).toBe(true);
    expect(result.indexOf("011") === -1).toBe(true);
  });

  test("mapEventGroupsAndTypeGroups", () => {
    let result;
    const data = [
      {
        eventGroupSummary: {
          type: "T",
          typeNo: 1,
          id: 4800,
          eventNo: 1,
          name: "200m",
        },
        event: {
          id: 5,
          name: "200m",
        },
        ageGroup: {
          id: 3,
        },
      },
      {
        eventGroupSummary: {
          type: "T",
          typeNo: 1,
          id: 4800,
          eventNo: 1,
          name: "200m",
        },
        event: {
          id: 5,
          name: "200m",
        },
        ageGroup: {
          id: 4,
        },
      },
      {
        eventGroupSummary: {
          type: "T",
          typeNo: 1,
          id: 4801,
          eventNo: 1,
          name: "Clashing Event no",
        },
        event: {
          id: 5,
          name: "400m",
        },
        ageGroup: {
          id: 4,
        },
      },
      {
        eventGroupSummary: {
          type: "T",
          typeNo: 2,
          id: 4801,
          eventNo: 2,
          name: "400m U11",
        },
        event: {
          id: 5,
          name: "400m",
        },
        ageGroup: {
          id: 4,
        },
      },
    ];

    result = compEventScheduleService.mapEventGroupsAndTypeGroups(
      data as unknown as ICompEvent[]
    );
    expect(Object.keys(result.eventNo[1]).length).toBe(2);

    const eventGroupSummary: IEventGroupSummary = {
      ...compEventService.factoryEventGroupSummary(),
      type: "T",
      typeNo: 1,
      id: 4800,
      eventNo: 1,
      name: "200m",
    };
    result = compEventScheduleService.eventGroupChangeWarning(
      eventGroupSummary,
      data as unknown as ICompEvent[]
    );
    expect(result["eventNoDups"].messages.length).toBe(1);
    expect(result["typeNoKeyDups"].messages.length).toBe(1);

    result = compEventScheduleService.eventGroupChangeWarning(
      {
        ...compEventService.factoryEventGroupSummary(),
        type: "T",
        typeNo: 9,
        id: 4800,
        eventNo: 9,
        name: "200m",
      },
      data as unknown as ICompEvent[]
    );
    expect(result["eventNoDups"]).toBe(undefined);
    expect(result["typeNoKeyDups"]).toBe(undefined);
  });

  test("doesEventContainThisAgeGroupSearchTerm", () => {
    const data: ICompEventSchedGrid = {
      id: 32849,
      startDateTime: "2022-07-31T00:00:00",
      sortDateTime: "2022-07-31T00:00:00",
      gender: "F",
      eventName: "Discus",
      eventNo: 7,
      maxGroup: "966",
      eventGroup: "Discus With Upscale",
      isEditEnabled: true,
      isSelected: false,
      showMore: false,
      compEvent: {
        maxGroup: 4966,
        split: 0,
        options: {
          min: 0,
          max: 0,
          helpText: "",
          registeredAthletes: true,
          unregistered: false,
          registered: true,
          isTeamEvent: false,
          wind: "",
          excludeFromCntRule: false,
          unique: [],
          eventTeam: {
            min: 0,
            max: 0,
            mustBeIndivEntered: false,
            minTargetAgeGroupCount: 0,
            maxOtherAgeGroupCount: 0,
            teamPositionLabel: "",
            maxEventTeams: 0,
            currCount: 0,
            singleClub: false,
            teamNameFormat:
              "{{entity}} {{eventname}} {{gender}} {{agegroup}} {{unique}}",
            teamSubstituteLabel: "athlete",
            showForm: false,
            formType: "DEFAULT",
            price: "",
          },
          rowOptions: {
            autoExpandHelpText: false,
            showPB: true,
            showPrice: false,
            showEntryCount: true,
          },
          maxInHeat: 0,
          heatInfo: {
            useLanes: "A",
            heatDurationMins: 0,
          },
          xiText: "",
          xeText: "",
          xbText: "",
          xrText: "",
          warningMessage: "",
          trialInfo: "",
          reportInfo: "",
          ageGroups: [
            {
              id: 2,
              minAge: 11,
              minAtDay: 31,
              minAtMonth: 8,
              keyName: "Under 13",
              options: [
                {
                  aocode: "EA",
                  default: true,
                  base: 1,
                },
              ],
              name: "Under 13",
              maxAge: 12,
              maxAtDay: 31,
              maxAtMonth: 8,
              maxAtYear: 0,
              minAtYear: 0,
              shortName: "Under 13",
            },
          ],
          singleAge: false,
          security: {
            clubs: [],
            counties: [],
            regions: [],
          },
          athleteSecurity: {
            clubs: [],
          },
          checkIn: {
            from: -1,
            to: -1,
            seedOnEntries: false,
            checkInMins: 60,
          },
          unregisteredAthletes: true,
          mandatoryPB: false,
          eventGroupInfo: {
            trialInfo: "",
            reportInfo: "",
          },
          seed: {
            firstLane: 1,
            laneCount: 0,
            gender: false,
            age: false,
            type: "O",
            waiting: false,
            qualifyToEg: {
              id: 0,
              compId: 0,
              name: "",
            },
          },
          secondarySpend: {
            isParent: false,
            parentCeid: 0,
          },
        },
        eoptions: {
          min: 1,
          max: 100,
        },
        eventGroupSummary: {
          type: "F",
          typeNo: 7,
          id: 4966,
          eventNo: 7,
          bibSortNo: "",
          name: "Discus With Upscale",
        },
        eventGroup: "Discus With Upscale",
        eventNo: 7,
        id: 32849,
        compId: 313,
        isOpen: 1,
        maxAthletes: 0,
        startDateTime: "2022-07-31T00:00:00",
        sortDateTime: "2022-07-31T00:00:00",
        event: {
          id: 18,
          name: "Discus",
          eventnameextra: null,
          gender: "F",
          tf: "F",
        },
        ageGroup: {
          id: 9,
          minAge: 13,
          minAtDay: 31,
          minAtMonth: 8,
          keyName: "Under 15",
          options: [
            {
              aocode: "EA",
              default: true,
              base: 1,
            },
          ],
          name: "Under 15",
          maxAge: 14,
          maxAtDay: 31,
          maxAtMonth: 8,
          maxAtYear: 0,
          minAtYear: 0,
          shortName: "Under 15",
        },
        price: {
          id: 435,
          fee: 0.6,
          saleFee: 0.6,
          price: 5,
          salePrice: 5,
          saleEndDate: null,
          options: {
            displayFee: false,
            feeIncluded: true,
            freeEntry: false,
          },
          name: "",
          description: "std",
        },
        multiEventInfo: {
          id: 0,
        },
        uom: {
          id: 5,
          type: "D",
          options: [
            {
              pattern: 0.99,
              text: "metres",
              short: "mt",
            },
          ],
        },
        entryCount: {
          total: 0,
          waiting: 0,
        },
      },
    } as any as ICompEventSchedGrid;

    // result = compEventScheduleService.doesEventContainThisAgeGroupSearchTerm(
    //   data,
    //   "under 15"
    // );
    expect(
      compEventScheduleService.doesEventContainThisAgeGroupSearchTerm(
        data,
        "under 15"
      )
    ).toBe(true);

    expect(
      compEventScheduleService.doesUpscalingMatchAgeGroupSearchTerm(
        data,
        "under 13"
      )
    ).toBe(true);

    expect(
      compEventScheduleService.doesEventContainThisAgeGroupSearchTerm(
        data,
        "under 12"
      )
    ).toBe(false);

    expect(
      compEventScheduleService.doesUpscalingMatchAgeGroupSearchTerm(
        data,
        "under 15"
      )
    ).toBe(false);

    expect(
      compEventScheduleService.doesEventContainThisAgeGroupSearchTerm(
        data,
        "under 13"
      )
    ).toBe(true);
  });

  test("getEntryCountDisplayText", () => {
    expect(
      compEventScheduleService.getEntryCountDisplayText(
        { total: 3, waiting: 0 },
        10
      )
    ).toBe("3 / 10");

    //  Even though passed waiting...the total is still 3 which is not greater than 10
    // so should still show 3 / 10.  THere must something  wrong with server data.
    expect(
      compEventScheduleService.getEntryCountDisplayText(
        { total: 3, waiting: 4 },
        10
      )
    ).toBe("3 / 10");

    //  Data is bad on the server, the total is greater than waiting.
    expect(
      compEventScheduleService.getEntryCountDisplayText(
        { total: 13, waiting: 0 },
        10
      )
    ).toBe("! 13 / 10");

    expect(
      compEventScheduleService.getEntryCountDisplayText(
        { total: 13, waiting: 3 },
        10
      )
    ).toBe("10 +3");

    expect(
      compEventScheduleService.getEntryCountDisplayText(
        { total: 0, waiting: 0 },
        10
      )
    ).toBe("0 / 10");

    expect(
      compEventScheduleService.getEntryCountDisplayText(
        { total: 0, waiting: 0 },
        0
      )
    ).toBe("");

    expect(
      compEventScheduleService.getEntryCountDisplayText(
        { total: 3, waiting: 0 },
        0
      )
    ).toBe("3");
  });
});
