<template>
  <div class="e4s-form-wrapper-xxx">
    <CollapseSection
      :is-expanded="false"
      v-on:isSectionBodyVisible="isSectionBodyVisible = $event"
    >
      <div slot="section-header">
        <span v-html="getSectionTitleHtml"></span>
      </div>

      <div slot="section-content">
        <div v-if="isSectionBodyVisible">
          <div class="row">
            <div class="col s12 m12 l12">
              <div class="e4s-flex-column e4s-gap--standard">
                <div class="e4s-flex-row e4s-justify-flex-space-between">
                  <div class="e4s-flex-row e4s-gap--standard">
                    <ButtonGenericV2
                      button-type="secondary"
                      class="e4s-button--auto"
                      style="min-width: 110px"
                      :disabled="getDisableEditButtons"
                      text="Edit Event"
                      @click="editEvent"
                    />
                    <ButtonGenericV2
                      v-if="getIsChildEvent"
                      button-type="secondary"
                      class="e4s-button--auto"
                      style="min-width: 110px"
                      :disabled="getDisableEditButtons"
                      text="Edit Parent"
                      @click="editParentEvent"
                    />
                    <ButtonGenericV2
                      button-type="destructive"
                      class="e4s-button--auto"
                      style="min-width: 110px"
                      :disabled="getDisableEditButtons"
                      text="Delete Event"
                      @click="deleteEvent"
                    />
                  </div>

                  <div class="e4s-flex-column">
                    <div
                      class="
                        e4s-flex-row
                        e4s-gap--standard
                        e4s-justify-flex-row-vert-center
                        e4s-flex-row--end
                      "
                    >
                      <span>Action for selected:</span>

                      <div class="e4s-flex-row" style="gap: 30px">
                        <label
                          v-for="action in getBulkActions"
                          :key="action.value"
                        >
                          <input
                            type="radio"
                            class="
                              browser-default
                              e4s-input-field e4s-input-field--primary
                            "
                            :value="action"
                            v-model="bulkAction"
                          />
                          <span v-text="action.label"></span>
                        </label>
                      </div>

                      <ButtonGenericV2
                        button-type="secondary"
                        text="Go"
                        @click="runAction"
                        :disabled="getRunDisabled"
                      />
                    </div>

                    <!--                    <div v-if="!canShowCheckBoxes">-->
                    <!--                      Specific editing of an individual line item can currently-->
                    <!--                      only be done if "Group By" sorting is set to "Event-->
                    <!--                      Group".-->
                    <!--                    </div>-->
                  </div>
                </div>

                <div v-if="!getAreAllEventGroupsInSectionSimilar">
                  Events in this section are a mixture of different event
                  groups: [
                  <span v-text="getAllEventGroupNames.join(', ')"></span>], some
                  functions are disabled.
                </div>

                <div v-if="getHasMultiEvents" style="margin-bottom: 8px">
                  <div class="row">
                    <div class="col s12 m12 l12">
                      Child Multi Events:
                      <span v-text="getMultiEventNamesDisplay"></span>.
                    </div>
                  </div>
                </div>

                <CompeventGroup
                  v-if="showCompEventGroupEditor"
                  :builder-competition="builderCompetition"
                  :value="eventGroupSummary"
                  :all-comp-events="allCompEvents"
                  @cancel="cancelGroupName"
                  @success="submittedGroupName"
                />
              </div>
            </div>
          </div>
        </div>

        <div style="height: 8px"></div>

        <div
          class="e4s-card-schedule"
          :class="getRowClass(compEventGrid, index)"
          v-for="(compEventGrid, index) in compEvents"
          :key="compEventGrid.compEvent.id"
        >
          <CompEventSectionRow
            :comp-event-grid="compEventGrid"
            :currency="configApp.currency"
            :display-check-box="
              eventGroupBeingEdited.length > 0
                ? compEventGrid.eventGroup === eventGroupBeingEdited
                : true
            "
            :is-loading="isLoading"
            :is-admin="isAdmin"
            v-on:onSelect="onSelect"
            v-on:onEditMe="onEditMe"
            v-on:onShowMore="onShowMore"
          />
        </div>

        <div>
          <div class="row">
            <div class="col s12 m12 l12">
              <div class="public-comp-mobile-separate"></div>
            </div>
          </div>

          <div class="row">
            <div class="col s6 m6 l6">
              <p>
                <label>
                  <input
                    class="e4s-checkbox"
                    type="checkbox"
                    v-model="showMore"
                    v-on:change="showMoreAllSection"
                  />
                  <span>Show more all</span>
                </label>
              </p>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col s12 m12 l12">
            <div class="e4s-flex-column">
              <div class="e4s-flex-row e4s-flex-row--end">
                <div
                  v-if="compEvents.length > 10"
                  class="
                    e4s-flex-row
                    e4s-gap--standard
                    e4s-justify-flex-row-vert-center
                  "
                >
                  <span>Action for selected:</span>

                  <div
                    class="e4s-flex-row e4s-flex-nowrap e4s-input--container"
                  >
                    <label v-for="action in getBulkActions" :key="action.value">
                      <input
                        type="radio"
                        class="
                          browser-default
                          e4s-input-field e4s-input-field--primary
                        "
                        :value="action"
                        v-model="bulkAction"
                      />
                      <span v-text="action.label"></span>
                    </label>
                  </div>

                  <ButtonGenericV2
                    button-type="secondary"
                    @click="runAction"
                    text="Go"
                    :disabled="getRunDisabled"
                  />
                </div>
              </div>
            </div>

            <CompeventGroup
              v-if="showCompEventGroupEditor"
              :builder-competition="builderCompetition"
              :value="eventGroupSummary"
              :all-comp-events="allCompEvents"
              @cancel="cancelGroupName"
              @success="submittedGroupName"
            />
          </div>
        </div>
      </div>
    </CollapseSection>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  ICompEventSchedGrid,
  ISectionAction,
} from "./compeventschedule-models";
import {
  BULK_ACTIONS,
  BulkActionType,
  IBulkAction,
  ISortBy,
  SORT_BY,
} from "../bulkevent/bulkevent-models";
import { CommonService } from "../../common/common-service";
import { CompEventScheduleService } from "./compeventschedule-service";
import CompEventSectionRow from "./compevent-section-row.vue";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp, IUserInfo } from "../../config/config-app-models";
import { format, parse } from "date-fns";
import { IEntryCount } from "../../builder/buildercompevent/builder-comp-event-models";
import { VUE_MQ_SIZES } from "../..";
import FieldWithButtons from "../../common/ui/field/field-with-buttons/field-with-buttons.vue";
import { ICompEvent, IEventGroupSummary } from "../compevent-models";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import { CompEventService } from "../compevent-service";
import { BUILDER_STORE_CONST } from "../../builder/builder-store-constants";
import CollapseSection from "../../common/ui/collapse/collapse-section.vue";
import { IBuilderCompetition } from "../../builder/builder-models";
import { IMultiEventEventDef } from "../../athleteCompSched/athletecompsched-models";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { getE4sStandardHumanDateTimeOutPut } from "../../common/common-service-utils";

@Component({
  name: "comp-event-schedule-section",
  components: {
    ButtonGenericV2,
    CollapseSection,
    FieldWithButtons,
    CompEventSectionRow,
    CompeventGroup: () => {
      return import(
        /* webpackPrefetch: true */
        /* webpackChunkName: "comp-event-group" */
        "../compevent-group/compevent-group.vue"
      );
    },
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class CompEventSchedSection extends Vue {
  public readonly userInfo: IUserInfo;
  public readonly configApp: IConfigApp;

  @Prop({ required: true })
  public readonly builderCompetition: IBuilderCompetition;
  @Prop({ default: "" }) public readonly sectionName: string;
  @Prop({
    default: () => {
      return {
        id: 0,
        value: "",
      };
    },
  })
  public readonly sortBy: ISortBy;
  @Prop({ default: () => [] })
  public readonly compEventsProp: ICompEventSchedGrid[];
  @Prop({ default: () => [] }) public readonly allCompEvents: ICompEvent[];
  @Prop({ default: false }) public readonly isLoading: boolean;
  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly eventNumbers: string[];
  @Prop({
    default: () => {
      return {
        sectionName: "",
      } as ISectionAction;
    },
  })
  public readonly currentSectionAction: ISectionAction;
  @Prop({ default: false }) public readonly canShowActionButton: boolean;
  @Prop({ default: false }) public readonly canShowCheckBoxes: boolean;
  @Prop({ default: "" }) public readonly eventGroupBeingEdited: string;
  @Prop({ default: false }) public readonly isMultiDateComp: boolean;
  @Prop({ default: false }) public readonly showAllGroupEditors: boolean;

  public compEventService = new CompEventService();
  public compEvents: ICompEventSchedGrid[] = [];
  public eventNumberSelected: string = "";
  public sortByOptions = SORT_BY;
  public bulkActions = BULK_ACTIONS;
  public bulkAction: IBulkAction = {
    label: "Edit",
    value: "EDIT",
    requiresSelected: true,
    dropDown: true,
    eventsMustBeSame: true,
  };
  public showMore: boolean = false;

  public commonService: CommonService = new CommonService();
  public compEventScheduleService: CompEventScheduleService =
    new CompEventScheduleService();
  public isSelectAll: boolean = false;

  public VUE_MQ_SIZES = VUE_MQ_SIZES;
  public $mq: any;

  public isEditingGroupName = false;
  public eventGroupSummary: IEventGroupSummary =
    this.compEventService.factoryEventGroupSummary();
  public isSectionBodyVisible = false;

  public showSectionRows = false;

  public showCompEventGroupEditor = false;

  public isMixedEvents = false;
  public mixedEvents: Record<string, unknown> = {};

  // public created() {
  //     this.compEvents = R.clone(this.compEventsProp);
  // }

  @Watch("compEventsProp", { immediate: true })
  public onCompEventsPropChanged(newValue: ICompEventSchedGrid[]) {
    let compEventsSchedGrid: ICompEventSchedGrid[] = R.clone(newValue);
    if (this.showMore) {
      compEventsSchedGrid = compEventsSchedGrid.map((cmpEventsSchedGrid) => {
        cmpEventsSchedGrid.showMore = true;
        return cmpEventsSchedGrid;
      });
    }
    this.compEvents = compEventsSchedGrid;
    this.buildEventGroupSummary(newValue);
    // this.eventGroupSummary = newValue[0].compEvent.eventGroupSummary;
    // console.log("CompEventSchedSection compEventsProp " + this.sectionName + " - " + new Date());

    const eventsNames = this.compEvents.reduce<Record<string, unknown>>(
      (accum, compEvent) => {
        accum[compEvent.eventGroup] = "";
        return accum;
      },
      {}
    );
    this.mixedEvents = eventsNames;
    this.isMixedEvents = Object.keys(eventsNames).length > 1;
  }

  @Watch("showAllGroupEditors")
  public onShowAllGroupEditorsChanged(newValue: boolean) {
    this.showCompEventGroupEditor = newValue;
  }

  public buildEventGroupSummary(compEventSchedGrids: ICompEventSchedGrid[]) {
    const compEventSchedGrid: ICompEventSchedGrid = compEventSchedGrids[0];

    const eventGroupSummary = {
      ...this.compEventService.factoryEventGroupSummary(),
      ...compEventSchedGrid.compEvent.eventGroupSummary,
    };
    eventGroupSummary.isOpen = compEventSchedGrid.compEvent.isOpen;
    eventGroupSummary.eventDateTime =
      compEventSchedGrid.compEvent.startDateTime;
    eventGroupSummary.limits = {
      maxAthletes: compEventSchedGrid.compEvent.maxAthletes,
      maxInHeat: compEventSchedGrid.compEvent.options.maxInHeat,
    };
    eventGroupSummary.options.seed = compEventSchedGrid.compEvent.options.seed;

    this.eventGroupSummary = eventGroupSummary;
  }

  @Watch("sortBy")
  public onSortByChanged() {
    this.isEditingGroupName = false;
  }

  public get getSectionTitleHtml() {
    let sectionNameSuffix = "";
    // let entryCountTotal = 0;
    let entryCountTotalMax = 0;
    let seedInfo: string = "";
    let scheduleOnly = "";
    let eventNo = "";
    let typeNo = "";
    let isOpen = true;
    let classifications = "";
    let bibSortNo = "";
    let isTeamEvent = false;
    let startTime = "";
    let isEventTrackish = false;
    let entryCountForCompEvents: IEntryCount = {
      total: 0,
      waiting: 0,
    };
    let entryCountDisplayText = "";
    let hasAvailableFrom = false;
    let hasAvailableTo = false;
    let hasAvailableTimes: string = "";

    let eventDescription = "";
    let hasAtLeastOneUniqueEventGroup = this.compEventsProp.reduce<boolean>(
      (accum, compEventSchedGrid) => {
        if (
          compEventSchedGrid.compEvent.options.uniqueEventGroups &&
          compEventSchedGrid.compEvent.options.uniqueEventGroups.length > 0
        ) {
          accum = true;
        }
        return accum;
      },
      false
    );

    if (this.compEventsProp.length > 0) {
      const compEventSchedGrid: ICompEventSchedGrid = this.compEventsProp[0];
      const compEvent = compEventSchedGrid.compEvent;

      eventDescription = compEventSchedGrid.compEvent.eventGroupSummary.name;
      if (compEventSchedGrid.compEvent.options.includeEntriesFromEgId.id > 0) {
        eventDescription +=
          " (inc: " +
          compEventSchedGrid.compEvent.options.includeEntriesFromEgId.name +
          ")";
      }

      typeNo =
        compEventSchedGrid.compEvent.eventGroupSummary.type +
        compEventSchedGrid.compEvent.eventGroupSummary.typeNo.toString();

      // console.log(    "?????" +  compEventSchedGrid.compEvent.eventGroupSummary.eventNo! ==
      //   compEventSchedGrid.compEvent.eventGroupSummary.typeNo)

      if (
        compEventSchedGrid.compEvent.eventGroupSummary.eventNo !==
        compEventSchedGrid.compEvent.eventGroupSummary.typeNo
      ) {
        eventNo =
          "E" +
          compEventSchedGrid.compEvent.eventGroupSummary.eventNo.toString();
      }

      bibSortNo = compEventSchedGrid.compEvent.eventGroupSummary.bibSortNo
        ? "B" +
          compEventSchedGrid.compEvent.eventGroupSummary.bibSortNo.toString()
        : "";

      isOpen = compEventSchedGrid.compEvent.isOpen === 1;

      const imgPath = require("../../images/ipc_logo.gif");

      classifications =
        compEventSchedGrid.compEvent.eoptions &&
        compEventSchedGrid.compEvent.eoptions.class &&
        compEventSchedGrid.compEvent.eoptions.class.length > 0
          ? " (" +
            "<img style='margin-left: 3px;' class='e4s-icon-img' src='" +
            imgPath +
            "'/>" +
            compEventSchedGrid.compEvent.eoptions.class +
            ") "
          : "";

      //<img class="qr--e4s-logo" :src="require('../images/po10.ico')"/>

      // entryCountTotal = this.compEventsProp.reduce((accum, compSchedEvent) => {
      //   const builderCompEvent =
      //     compSchedEvent.compEvent as unknown as IBuilderCompEvent;
      //   const total =
      //     builderCompEvent.entryCount && builderCompEvent.entryCount.total
      //       ? builderCompEvent.entryCount.total
      //       : 0;
      //   accum += total;
      //   return accum;
      // }, 0);

      entryCountForCompEvents =
        this.compEventScheduleService.getTotalsForCompEvents(
          this.compEventsProp
        );

      entryCountTotalMax = compEventSchedGrid.compEvent.maxAthletes;

      entryCountDisplayText =
        this.compEventScheduleService.getEntryCountDisplayText(
          entryCountForCompEvents,
          entryCountTotalMax
        );

      // if (this.sortBy.value === "startDateTime") {
      const dateTime = parse(compEventSchedGrid.startDateTime);
      const dateText = this.isMultiDateComp
        ? format(dateTime, "ddd Do MMM")
        : "";
      const timeText = format(dateTime, "HH:mm");
      startTime =
        dateText +
        " " +
        (timeText === "00:00" ? "TBC" : format(dateTime, "hh:mma"));

      if (compEventSchedGrid.compEvent.maxAthletes === -1) {
        scheduleOnly = "Schedule only";
      }

      isEventTrackish =
        this.compEventScheduleService.isEventTrackish(compEventSchedGrid);

      if (compEventSchedGrid.compEvent.options.seed && isEventTrackish) {
        const seed = compEvent.options.seed;
        const seedBy: string[] = [];
        if (seed.age) {
          seedBy.push("Age");
        }
        if (seed.gender) {
          seedBy.push("Gender");
        }

        seedInfo =
          "Seed: " +
          (seed.type === "O" ? "Open Format" : "Heat Format") +
          (seedBy.length === 0 ? "" : " by " + seedBy.join(", ")) +
          (seed.qualifyToEg.id > 0
            ? ", Q for:  [" +
              seed.qualifyToEg.eventNo +
              "] " +
              seed.qualifyToEg.name +
              " [" +
              seed.qualifyToEg.id +
              "]"
            : "");
      }
      isTeamEvent = compEvent.options.isTeamEvent;

      // if (compEvent.options.availableFrom) {
      hasAvailableFrom = !!(
        compEvent.options.availableFrom &&
        compEvent.options.availableFrom.length > 0
      );
      hasAvailableTo = !!(
        compEvent.options.availableTo &&
        compEvent.options.availableTo.length > 0
      );

      hasAvailableTimes =
        hasAvailableFrom || hasAvailableTo
          ? '<i title="From: ' +
            (hasAvailableFrom
              ? getE4sStandardHumanDateTimeOutPut(
                  compEvent.options.availableFrom
                )
              : "") +
            " To: " +
            (hasAvailableTo
              ? getE4sStandardHumanDateTimeOutPut(compEvent.options.availableTo)
              : "") +
            '" class="small material-icons e4s-icon e4s-force-inline-block">date_range</i>'
          : "";
      // }
    }

    if (this.isMixedEvents) {
      return "[" + Object.keys(this.mixedEvents).join(", ") + "] " + startTime;
    }

    // hasAvailableFrom = this.compEventsProp.reduce<boolean>(
    //   (accum, compEventSchedGrid) => {
    //     if (compEventSchedGrid.compEvent.options.availableFrom.length > 0) {
    //       accum = true;
    //     }
    //     return accum;
    //   },
    //   false
    // );

    // hasAvailableTo = this.compEventsProp.reduce<boolean>(
    //   (accum, compEventSchedGrid) => {
    //     if (compEventSchedGrid.compEvent.options.availableTo.length > 0) {
    //       accum = true;
    //     }
    //     return accum;
    //   },
    //   false
    // );

    return (
      "[" +
      [typeNo, eventNo, bibSortNo]
        .filter((val) => {
          return val.length > 0;
        })
        .join(", ") +
      "] " +
      (isTeamEvent
        ? "<i class='small material-icons e4s-icon'>group</i>"
        : "") +
      eventDescription +
      startTime +
      (hasAtLeastOneUniqueEventGroup
        ? "<i class='small material-icons e4s-icon red-text' title='Some entries in this event will block entry to other events'>change_history</i>"
        : "") +
      classifications +
      (sectionNameSuffix.length > 0 ? ": " + sectionNameSuffix : "") +
      (entryCountForCompEvents.total > 0 || entryCountTotalMax > 0
        ? " (<i class='material-icons normal'>directions_run</i>" +
          entryCountDisplayText +
          ")"
        : "") +
      (seedInfo.length > 0 ? " - " + seedInfo + "" : "") +
      (scheduleOnly.length > 0
        ? " - <span style='color: yellow'>" + scheduleOnly + "</span>"
        : "") +
      (isOpen ? "" : " <span style='color: yellow'>Entries Closed</span>") +
      ", " +
      // startTime +
      hasAvailableTimes
    );
  }

  public get getShowMoveNumbers() {
    return (
      [this.bulkActions.ADD.value, this.bulkActions.INSERT.value].indexOf(
        this.bulkAction.value
      ) > -1
    );
  }

  public get getBulkActions(): IBulkAction[] {
    const bulkActions = this.bulkActions;
    if (R.isNil(this.sortBy)) {
      return [];
    }
    const actionsReturn: IBulkAction[] = [];
    actionsReturn.push(bulkActions.EDIT);
    actionsReturn.push(bulkActions.DELETE);
    return R.clone(actionsReturn);
  }

  public get getSelectedCount() {
    return this.compEventScheduleService.getSelected(this.compEvents).length;
  }

  public get getRunDisabled() {
    if (this.isLoading) {
      return true;
    }
    if (this.bulkAction.value.length === 0) {
      return true;
    }
    return this.bulkAction.requiresSelected
      ? this.getSelectedCount === 0
      : false;
  }

  public get getShowActionBar() {
    return (
      this.currentSectionAction.sectionName.length === 0 ||
      this.currentSectionAction.sectionName === this.sectionName
    );
  }

  public onSelect(id: number) {
    // console.log("CompEventSchedSection onSelect " + new Date());
    this.$emit("onSelect", id);
    this.onChangeBulkAction();
  }

  public onEditMe(compSchedGrid: ICompEventSchedGrid) {
    // console.log("CompEventSchedSection onEditMe " + new Date());
    this.$emit("onEditMe", R.clone(compSchedGrid));
    this.onChangeBulkAction();
  }

  public onShowMore(id: number, showMore: boolean) {
    this.$emit("onShowMore", {
      id,
      showMore,
    });
  }

  public showMoreAllSection() {
    this.$emit("showMoreAllSection", {
      sectionName: this.sectionName,
      showMore: this.showMore,
    });
  }

  public onChangeBulkAction() {
    this.$emit("onSelectAction", {
      sectionName: this.sectionName,
      action: R.clone(this.bulkAction),
    });
  }

  public editEvent() {
    this.selectAll(true, "EDIT_SECTION");
    this.$emit("runAction", {
      sectionName: this.sectionName,
      action: R.clone(this.bulkActions.EDIT_SECTION),
      eventNo: this.eventNumberSelected,
    });
  }

  public editParentEvent() {
    this.selectAll(true, "EDIT_SECTION");
    this.$emit("runAction", {
      sectionName: this.sectionName,
      action: R.clone(this.bulkActions.EDIT_PARENT_SECTION),
      eventNo: this.eventNumberSelected,
    });
  }

  public deleteEvent() {
    this.$emit("runAction", {
      sectionName: this.sectionName,
      action: R.clone(this.bulkActions.DELETE_SECTION),
      eventNo: this.eventNumberSelected,
    });
  }

  public runAction() {
    // if (this.bulkAction.value === "EDIT_SECTION") {
    //   this.selectAll(true, "EDIT_SECTION");
    // }
    this.$emit("runAction", {
      sectionName: this.sectionName,
      action: R.clone(this.bulkAction),
      eventNo: this.eventNumberSelected,
    });
  }

  public selectAll(isSelectAll: boolean, bulkActionType?: BulkActionType) {
    this.isSelectAll = isSelectAll;
    this.$emit("runAction", {
      sectionName: this.sectionName,
      action: {
        value: bulkActionType
          ? bulkActionType
          : isSelectAll
          ? "SELECT_ALL"
          : "DE_SELECT_ALL",
      },
      eventNos: this.compEventsProp.map((evt) => evt.id),
    });
  }

  public getRowClass(compSchedGrid: ICompEventSchedGrid, index: number) {
    const classes = [];
    if (index % 2 === 0) {
      classes.push("e4s-card-schedule__row-odd");
    }
    if (compSchedGrid.isSelected) {
      classes.push("e4s-card-schedule__row-selected");
    }
    return classes.join(" ");
  }

  // public get getCompEventIds() {
  //     return this.compEventsProp.map((evt) => evt.id)
  // }

  public get areAllEventsSimilar() {
    const eventsSelected = this.compEvents.filter((evt) => {
      return evt.isSelected;
    });

    return this.compEventScheduleService.areAllEventsSimilar(eventsSelected);
  }

  public isActionDisabled(bulkAction: IBulkAction) {
    return !this.areAllEventsSimilar && bulkAction.eventsMustBeSame;
  }

  public get getAllEventGroupNames() {
    return this.compEventScheduleService.getAllEventGroupNames(
      this.compEventsProp
    );
  }

  public get getAreAllEventGroupsInSectionSimilar() {
    return this.compEventScheduleService.areAllEventsSimilar(
      this.compEventsProp
    );
  }

  public get getDisableEditButtons() {
    if (this.compEventsProp.length === 0) {
      return true;
    }
    if (this.isLoading) {
      return true;
    }
    if (!this.getAreAllEventGroupsInSectionSimilar) {
      return true;
    }
    if (this.getAreAllEventGroupsInSectionSimilar) {
      const compEventGroupThisSection =
        this.compEventsProp[0].compEvent.eventGroupSummary.name;
      return this.eventGroupBeingEdited.length === 0
        ? false
        : this.eventGroupBeingEdited !== compEventGroupThisSection;
    }
    return false;
  }

  public editGroupName() {
    if (this.compEventsProp.length === 0) {
      messageDispatchHelper(
        "No comp events found!",
        USER_MESSAGE_LEVEL.ERROR.toString()
      );
      return;
    }
    this.eventGroupSummary = this.compEventsProp[0].compEvent.eventGroupSummary;
    this.isEditingGroupName = true;
  }

  public submittedGroupName() {
    this.isEditingGroupName = false;

    //  Reloads schedule grid
    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_COMPEVENTS_LOAD
    );
  }

  public cancelGroupName() {
    this.isEditingGroupName = false;
  }

  public get getIsEditingGroupName() {
    return this.isEditingGroupName || this.sortBy.value !== "eventGroup";
  }

  public get getMultiEvents(): IMultiEventEventDef[] {
    if (this.compEvents.length === 0) {
      return [];
    }
    return this.compEvents[0].compEvent.options.multiEventOptions.childEvents;
  }

  public get getMultiEventNames(): string[] {
    return this.getMultiEvents.map((evt) => evt.name);
  }

  public get getMultiEventNamesDisplay(): string {
    return this.getMultiEventNames.join(", ");
  }

  public get getHasMultiEvents() {
    return this.getMultiEvents.length > 0;
  }

  public get getIsChildEvent(): boolean {
    if (this.compEvents.length === 0) {
      return false;
    }
    return this.compEventScheduleService.isChildEvent(this.compEvents[0]);
  }
}
</script>
