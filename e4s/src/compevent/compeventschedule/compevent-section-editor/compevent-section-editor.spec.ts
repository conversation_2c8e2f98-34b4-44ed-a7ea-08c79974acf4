import * as CompeventSectionEditorService from "./compevent-section-editor-service";
import { IEventGroupSummary } from "../../compevent-models";

describe("CompEventSectionEditorService", () => {
  test("calculateEventNumbers", () => {
    const data: IEventGroupSummary[] = [
      {
        id: 5217,
        isOpen: 1,
        eventNo: 2,
        name: "200m",
        notes: "",
        type: "T",
        typeNo: 2,
        bibSortNo: "",
        eventDateTime: "2022-07-31T22:00:00",
        options: {
          seed: {
            age: false,
            firstLane: 0,
            gender: false,
            laneCount: 0,
            qualifyToEg: { id: 0, eventNo: 0, name: "", isMultiEvent: false },
            type: "O",
            waiting: false,
          },
        },
        limits: { maxAthletes: 0, maxInHeat: 0 },
      },
      {
        id: 5219,
        isOpen: 1,
        eventNo: 1,
        name: "100m Final",
        type: "T",
        notes: "",
        typeNo: 1,
        bibSortNo: "",
        eventDateTime: "2022-07-31T00:00:00",
        options: {
          seed: {
            age: false,
            firstLane: 0,
            gender: false,
            laneCount: 0,
            qualifyToEg: { id: 0, eventNo: 0, name: "", isMultiEvent: false },
            type: "O",
            waiting: false,
          },
        },
        limits: { maxAthletes: -1, maxInHeat: 0 },
      },
      {
        id: 5215,
        isOpen: 1,
        eventNo: 5,
        name: "100m",
        notes: "",
        type: "T",
        typeNo: 5,
        bibSortNo: "",
        eventDateTime: "2022-07-31T09:02:00",
        options: {
          seed: {
            age: false,
            firstLane: 0,
            gender: false,
            laneCount: 0,
            qualifyToEg: { id: 0, eventNo: 0, name: "", isMultiEvent: false },
            type: "O",
            waiting: false,
          },
        },
        limits: { maxAthletes: 0, maxInHeat: 0 },
      },
      {
        id: 5218,
        isOpen: 1,
        eventNo: 3,
        name: "800m",
        notes: "",
        type: "T",
        typeNo: 3,
        bibSortNo: "",
        eventDateTime: "2022-07-31T13:00:00+01:00",
        options: {
          seed: {
            age: false,
            firstLane: 0,
            gender: false,
            laneCount: 0,
            qualifyToEg: { id: 0, eventNo: 0, name: "", isMultiEvent: false },
            type: "O",
            waiting: false,
          },
        },
        limits: { maxAthletes: 0, maxInHeat: 0 },
      },
      {
        id: 5216,
        isOpen: 1,
        eventNo: 4,
        name: "1500m",
        notes: "",
        type: "T",
        typeNo: 4,
        bibSortNo: "",
        eventDateTime: "2022-07-31T16:00:00+01:00",
        options: {
          seed: {
            age: false,
            firstLane: 0,
            gender: false,
            laneCount: 0,
            qualifyToEg: { id: 0, eventNo: 0, name: "", isMultiEvent: false },
            type: "O",
            waiting: false,
          },
        },
        limits: { maxAthletes: 0, maxInHeat: 0 },
      },
    ];
    const result = CompeventSectionEditorService.calculateEventNumbers(data);
    expect(result.length).toBe(5);

    //  Latest in day real time
    expect(result[3].name).toBe("200m");
    expect(result[3].eventNo).toBe(4);

    // Event with time not set
    expect(result[4].name).toBe("100m Final");
    expect(result[4].eventNo).toBe(5);
  });
});
