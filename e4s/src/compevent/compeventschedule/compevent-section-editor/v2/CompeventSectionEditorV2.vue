<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div
      class="
        e4s-flex-row
        e4s-gap--standard
        e4s-justify-flex-row-vert-center e4s-justify-flex-space-between
      "
    >
      <div
        class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
      >
        <FieldHelp
          :get-from-server="true"
          help-key="compevent-section-editor"
        />

        <ButtonGenericV2
          text="Save"
          :disabled="!state.isDirty"
          @click="state.ui.showConfirmSubmit = true"
        />

        <ButtonGenericV2
          class="e4s-button--auto"
          text="Set Event Numbers"
          button-type="secondary"
          :disabled="isFilterOn || state.ui.showConfirmCalc"
          @click="state.ui.showConfirmCalc = true"
        />

        <ButtonGenericV2
          class="e4s-button--auto"
          text="Undo Changes"
          button-type="secondary"
          :disabled="!state.isDirty"
          @click="state.ui.showConfirmReset = true"
        />

        <ButtonGenericV2
          class="e4s-button--auto"
          text="Remove Event Times"
          button-type="secondary"
          @click="state.ui.showConfirmClear = true"
        />
      </div>

      <div
        class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
      >
        <span style="margin-left: 20px">Tab Order</span>
        <label>
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            value="HORZ"
            v-model="state.tabDirection"
          />
          <span>Horizontal</span>
        </label>

        <label>
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            value="VERT"
            v-model="state.tabDirection"
          />
          <span>Vertical</span>
        </label>
      </div>
    </div>

    <hr class="dat-e4s-hr dat-e4s-hr-only" />

    <!--    <div>Click column header to sort.</div>-->

    <div style="font-weight: 600" v-if="isFilterOn">
      Filters applied: not all data is showing. Use caution when setting event
      numbers.
    </div>

    <div
      class="
        e4s-flex-row
        compevent-section-editor-v2--row
        e4s-gap--tiny
        e4s-flex-end
      "
    >
      <div
        class="
          compevent-section-editor-v2--cell
          compevent-section-editor-v2--cell-header
          compevent-section-editor-v2--eventno
        "
      >
        <a
          href="#"
          v-on:click.prevent="doSort('EVENT_NO')"
          :class="
            state.sortKey === 'EVENT_NO'
              ? 'compevent-section-editor--sort-active'
              : ''
          "
        >
          Event
          <i class="normal material-icons" v-if="state.sortKey === 'EVENT_NO'">
            arrow_drop_up
          </i>
        </a>
      </div>

      <div
        class="
          compevent-section-editor-v2--cell
          compevent-section-editor-v2--type
          compevent-section-editor-v2--cell-header
        "
      >
        <a
          href="#"
          v-on:click.prevent="doSort('TYPE_NO')"
          :class="
            state.sortKey === 'TYPE_NO'
              ? 'compevent-section-editor--sort-active'
              : ''
          "
        >
          Type
          <i class="normal material-icons" v-if="state.sortKey === 'TYPE_NO'">
            arrow_drop_up
          </i>
        </a>
      </div>

      <div
        class="
          compevent-section-editor-v2--cell
          compevent-section-editor-v2--cell-header
          compevent-section-editor-v2--bib-sort
        "
      >
        <a
          href="#"
          v-on:click.prevent="doSort('BIB_SORT')"
          :class="
            state.sortKey === 'BIB_SORT'
              ? 'compevent-section-editor--sort-active'
              : ''
          "
        >
          Bib Sort
          <i class="normal material-icons" v-if="state.sortKey === 'BIB_SORT'">
            arrow_drop_up
          </i>
        </a>
      </div>

      <div
        class="
          compevent-section-editor-v2--cell
          compevent-section-editor-v2--name
          compevent-section-editor-v2--cell-header
        "
      >
        <a
          href="#"
          v-on:click.prevent="doSort('NAME')"
          :class="
            state.sortKey === 'NAME'
              ? 'compevent-section-editor--sort-active'
              : ''
          "
        >
          Name
          <i class="normal material-icons" v-if="state.sortKey === 'NAME'">
            arrow_drop_up
          </i>
        </a>
      </div>
      <div
        class="
          compevent-section-editor-v2--cell
          compevent-section-editor-v2--time-group
          compevent-section-editor-v2--cell-header
        "
      >
        <a
          href="#"
          v-on:click.prevent="doSort('TIME')"
          style="margin-right: 20px"
          :class="
            state.sortKey === 'TIME'
              ? 'compevent-section-editor--sort-active'
              : ''
          "
        >
          Time
          <i class="normal material-icons" v-if="state.sortKey === 'TIME'">
            arrow_drop_up
          </i>
        </a>
      </div>
      <div
        class="
          compevent-section-editor-v2--cell
          compevent-section-editor-v2--max
          compevent-section-editor-v2--cell-header
        "
      >
        Max
      </div>
      <div
        class="
          compevent-section-editor-v2--cell
          compevent-section-editor-v2--checkbox
          compevent-section-editor-v2--cell-header
        "
      >
        Schedule Only
      </div>
      <div
        class="
          compevent-section-editor-v2--cell
          compevent-section-editor-v2--checkbox
          compevent-section-editor-v2--cell-header
        "
      >
        Open
      </div>
      <!--      <div-->
      <!--        class="-->
      <!--          compevent-section-editor-v2&#45;&#45;cell-->
      <!--          compevent-section-editor-v2&#45;&#45;notes-->
      <!--          compevent-section-editor-v2&#45;&#45;cell-header-->
      <!--        "-->
      <!--      >-->
      <!--        Notes-->
      <!--      </div>-->
    </div>

    <hr
      class="dat-e4s-hr dat-e4s-hr-only"
      style="border-color: var(--slate-200)"
    />

    <div class="e4s-flex-column">
      <CompeventSectionEditorRowV2
        v-for="(eventGroupSummary, index) in state.eventGroupSummaries"
        v-if="
          state.eventGroupSummariesMapForFilter[eventGroupSummary.id.toString()]
        "
        :value="eventGroupSummary"
        class="compevent-section-editor-v2--row"
        :class="
          eventGroupSummary.id === state.lastEditedId
            ? 'compevent-section-editor--lsat-edited'
            : ''
        "
        style="border-bottom: 1px solid var(--slate-200)"
        :key="eventGroupSummary.id"
        :index="index"
        :tab-direction="state.tabDirection"
        :is-filter-on="isFilterOn"
        v-on:input="processUpdate"
      />
    </div>

    <LoadingSpinnerV2 v-if="state.isLoading" />

    <E4sModal
      v-if="state.ui.showConfirmCalc"
      header-message="Calculate Event numbers"
      body-message="Are you sure you want to continue with Calculating event numbers?"
      v-on:closePrimary="doCalc(true)"
      v-on:closeSecondary="doCalc(false)"
    >
      <div slot="body">
        <p>
          This will split events by track then field, then allocate event
          numbers based on the start time.
        </p>

        <div v-if="groupsNeedingTimeSetting.length > 0">
          Please note, there are
          <span v-text="groupsNeedingTimeSetting.length"></span> events with no
          time set. These will allocated an event number after events with a
          time.
        </div>
      </div>
    </E4sModal>

    <E4sModal
      v-if="state.ui.showConfirmReset"
      header-message="Reset"
      body-message="Are you sure you want to continue with RESET?"
      v-on:closePrimary="doReset(true)"
      v-on:closeSecondary="doReset(false)"
    />

    <E4sModal
      v-if="state.ui.showConfirmSubmit"
      header-message="Submit"
      body-message="Are you sure you want to continue with SUBMIT?"
      v-on:closePrimary="doSubmit(true)"
      v-on:closeSecondary="doSubmit(false)"
    />

    <E4sModal
      v-if="state.ui.showConfirmClear"
      header-message="Clear"
      body-message="Are you sure you want to continue with CLEAR-ing event times?"
      v-on:closePrimary="doClear(true)"
      v-on:closeSecondary="doClear(false)"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  SetupContext,
  PropType,
  watch,
  reactive,
  computed,
} from "@vue/composition-api";
import {
  ICompEventSchedGrid,
  IGroupEditorOutput,
} from "../../compeventschedule-models";
import { CompEventService } from "../../../compevent-service";
import {
  CompeventSectionEditorSortProp,
  ICompeventSectionEditorState,
} from "../compevent-section-editor-models";
import * as CompeventSectionEditorService from "../compevent-section-editor-service";
import { ICompEvent, IEventGroupSummary } from "../../../compevent-models";
import * as CommonServiceUtils from "../../../../common/common-service-utils";
import { CompEventScheduleService } from "../../compeventschedule-service";
import { CompEventData } from "../../../compevent-data";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import LoadingSpinnerModal from "../../../../common/ui/modal/loading-spinner-modal.vue";
import StandardForm from "../../../../common/ui/standard-form/standard-form.vue";
import FieldHelp from "../../../../common/ui/field/field-help/field-help.vue";
import E4sModal from "../../../../common/ui/e4s-modal.vue";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import { simpleClone } from "../../../../common/common-service-utils";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import CompeventSectionEditorRowV2 from "./CompeventSectionEditorRowV2.vue";

export default defineComponent({
  name: "CompeventSectionEditorV2",
  components: {
    ButtonGenericV2,
    LoadingSpinnerV2,
    E4sModal,
    FieldHelp,
    StandardForm,
    LoadingSpinnerModal,
    CompeventSectionEditorRowV2,
  },
  props: {
    compEvents: {
      type: Array as PropType<ICompEvent[]>,
      default: () => {
        return [];
      },
    },
    isFilterOn: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    compEventsSchedGridObject: {
      type: Object as PropType<Record<string, ICompEventSchedGrid>>,
      default: () => {
        return {};
      },
    },
  },
  setup(
    props: {
      compEvents: ICompEvent[];
      isFilterOn: boolean;
      compEventsSchedGridObject: Record<string, ICompEventSchedGrid[]>;
    },
    context: SetupContext
  ) {
    const state = reactive<ICompeventSectionEditorState>({
      isDirty: false,
      isLoading: false,
      eventGroupSummaries: [],
      eventGroupSummariesMapForFilter: {},
      tabDirection: "HORZ",
      sortKey: "TIME_BY_TYPE",
      autoSort: false,
      lastEditedId: 0,
      ui: {
        showConfirmCalc: false,
        showConfirmReset: false,
        showConfirmSubmit: false,
        showConfirmClear: false,
      },
    });

    const compEventService = new CompEventService();
    const compEventScheduleService: CompEventScheduleService =
      new CompEventScheduleService();

    init(props.compEvents);

    watch(
      () => props.compEvents,
      (newValue: ICompEvent[]) => {
        init(newValue);
      }
    );

    watch(
      () => props.compEventsSchedGridObject,
      (newValue: Record<string, ICompEventSchedGrid[]>) => {
        setUpFilterObject(newValue);
      }
    );

    function init(compEvents: ICompEvent[]) {
      const compEventSchedGrids: ICompEventSchedGrid[] =
        compEventScheduleService.mapCompEventsToSchedule(compEvents);

      state.eventGroupSummaries =
        compEventService.convertCompEventsIntoEventGroupSummaries(
          compEventSchedGrids
        );

      // state.sortKey = "TIME_BY_TYPE";
      state.lastEditedId = 0;

      sortData();
      setUpFilterObject(props.compEventsSchedGridObject);
    }

    function setUpFilterObject(
      compEventSchedGridObj: Record<string, ICompEventSchedGrid[]>
    ) {
      const compEventSchedGrids: ICompEventSchedGrid[] = Object.values(
        compEventSchedGridObj
      ).reduce<ICompEventSchedGrid[]>((accum, compEventSchedGrids) => {
        // accum.concat(compEventSchedGrids);
        accum = accum.concat(compEventSchedGrids);
        return accum;
      }, []);

      state.eventGroupSummariesMapForFilter =
        compEventService.convertCompEventsIntoEventGroupSummaryMap(
          compEventSchedGrids
        );
    }

    function sortData() {
      if (state.sortKey === "TIME_BY_TYPE") {
        state.eventGroupSummaries =
          CompeventSectionEditorService.sortByTrackFieldTimeArray(
            state.eventGroupSummaries
          );
        return;
      }
      state.eventGroupSummaries = CompeventSectionEditorService.sortDataByProp(
        state.sortKey,
        state.eventGroupSummaries
      );
    }

    function doSort(sortKey: CompeventSectionEditorSortProp) {
      state.sortKey = sortKey;
      sortData();
    }

    function setIsDirty(isDirty: boolean) {
      state.isDirty = isDirty;

      const groupEditorOutput: IGroupEditorOutput = {
        eventGroupSummaries: state.eventGroupSummaries,
        performCheck: true,
      };

      context.emit("isDirty", groupEditorOutput);
    }

    function processUpdate(eventGroupSummary: IEventGroupSummary) {
      setIsDirty(true);
      state.eventGroupSummaries = CommonServiceUtils.simpleClone(
        state.eventGroupSummaries
      ).map((evt) => {
        return evt.id === eventGroupSummary.id ? eventGroupSummary : evt;
      });
      state.lastEditedId = eventGroupSummary.id;
      if (state.autoSort) {
        sortData();
      }
    }

    function doReset(doIt: boolean) {
      state.ui.showConfirmReset = false;
      if (doIt) {
        reset();
      }
    }

    function reset() {
      init(props.compEvents);
    }

    function doCalc(doIt: boolean) {
      state.ui.showConfirmCalc = false;
      if (doIt) {
        calc();
      }
    }

    function calc() {
      state.eventGroupSummaries =
        CompeventSectionEditorService.calculateEventNumbers(
          state.eventGroupSummaries
        );
      setIsDirty(true);
    }

    function doClear(doIt: boolean) {
      state.ui.showConfirmClear = false;
      if (doIt) {
        clearEventTimes();
      }
    }

    function clearEventTimes() {
      const eventGroupSummaries = CompeventSectionEditorService.clearEventTimes(
        state.eventGroupSummaries
      );
      state.eventGroupSummaries = simpleClone(eventGroupSummaries);
      setIsDirty(true);
    }

    function doSubmit(doIt: boolean) {
      state.ui.showConfirmSubmit = false;
      if (doIt) {
        submit();
      }
    }

    function submit() {
      state.isLoading = true;

      const prom = new CompEventData().updateEventGroupSummaries(
        state.eventGroupSummaries
      );

      handleResponseMessages(prom);

      prom
        .then((response) => {
          if (response.errNo === 0) {
            messageDispatchHelper("Saved", USER_MESSAGE_LEVEL.INFO.toString());
            context.emit("reload");
            context.emit("isDirty", false);
          }
        })
        .finally(() => {
          state.isLoading = false;
        });
    }

    const groupsNeedingTimeSetting = computed<IEventGroupSummary[]>(() => {
      return CompeventSectionEditorService.getEventsNeedingTimeSetting(
        state.eventGroupSummaries
      );
    });

    return {
      state,
      doSort,
      processUpdate,
      doCalc,
      doReset,
      doSubmit,
      doClear,
      groupsNeedingTimeSetting,
    };
  },
});
</script>

<style>
.compevent-section-editor-v2--row {
  flex-wrap: wrap;
}

.compevent-section-editor-v2--cell {
}

.compevent-section-editor-v2--cell-header {
  text-align: left;
}

.compevent-section-editor-v2--eventno {
  width: 65px;
}

.compevent-section-editor-v2--type {
  width: 85px;
}

.compevent-section-editor-v2--bib-sort {
  width: 65px;
}

.compevent-section-editor-v2--type-desc {
  /*margin-right: 5px;*/
  width: 20px;
  font-weight: 600;
  text-align: center;
}

.compevent-section-editor-v2--type-desc-track {
  color: blue;
}

.compevent-section-editor-v2--type-desc-field {
  color: green;
}

.compevent-section-editor-v2--name {
  width: 250px;
}

.compevent-section-editor-v2--time-group {
  width: 350px;
}

.compevent-section-editor-v2--max {
  width: 60px;
}

.compevent-section-editor-v2--checkbox {
  width: 60px;
}

.compevent-section-editor-v2--isopen {
}

.compevent-section-editor-v2--isscheduleonly {
}

.compevent-section-editor-v2--notes {
  width: 200px;
}

.compevent-section-editor-v2--lsat-edited {
  background-color: yellow;
}
</style>
