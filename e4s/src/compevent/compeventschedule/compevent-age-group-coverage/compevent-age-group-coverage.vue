<template>
  <div id="qaz-coverage-parent">
    <div
      v-if="showSection === 'HELPER'"
      class="e4s-flex-column"
      id="qaz-coverage-firstchild"
    >
      <!--      <div class="e4s-flex-column">-->
      <!--        <div>{{ compId }}</div>-->
      <!--        <div>{{ showCreateButton }}</div>-->
      <!--      </div>-->

      <div
        v-if="showHelperRow"
        class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
      >
        <span>Years</span>
        <FieldHelp
          title="Age Group Coverage: Years"
          message="Years: Use this to change the year spread displayed"
        />

        <FieldNumberV2 v-model="yearsToDisplay.min" />
        <span>to</span>
        <FieldNumberV2 v-model="yearsToDisplay.max" />

        <ButtonGenericV2 text="Change" button-type="secondary" />
        <ButtonGenericV2
          class="e4s-button--admin"
          v-if="isAdmin"
          button-type="secondary"
          @click="showRaw = !showRaw"
          :text="(showRaw ? 'Hide' : 'Show') + ' Raw'"
        />

        <ButtonGenericV2
          @click="showSection = 'CREATE'"
          text="Create"
          v-if="showCreateButton && compId > 0"
        />

        <div
          class="
            e4s-flex-row
            e4s-gap--standard
            e4s-flex-row--end
            e4s-justify-flex-row-vert-center
          "
        >
          <span v-text="calcAgeInYears"></span>
          <FieldHelp
            title="Date of Birth"
            message="Use this to see the age group coverage for a specific date of birth"
          />
          <!--        <DateEntry v-on:onSelected="dateOfBirthInternal = $event" />-->
          <DateInputDropDownV2
            :default-year="2001"
            @input="dateOfBirthInternal = $event"
            :allow-null-reset="true"
          />
        </div>
      </div>

      <table>
        <CompeventAgeGroupCoverageRow
          :debug="false"
          :years-to-display="yearsToDisplay"
          :ao-code="aoCode"
          :is-header="true"
          :date-of-birth="dateOfBirthInternal"
          :comp-date="compDate"
        />
        <CompeventAgeGroupCoverageRow
          v-for="(valuesForRow, name, index) in rowMap"
          :is-admin="isAdmin"
          :key="name"
          :age-group-comp-coverage-models="valuesForRow"
          :years-to-display="yearsToDisplay"
          :date-of-birth="dateOfBirthInternal"
          :ao-code="aoCode"
          v-on:ageGroupClicked="ageGroupClicked"
        />
      </table>

      <div v-if="showRaw">
        <CompeventAgeGroupCoverageText
          :age-group-comp-coverage-models="ageGroupCompCoverageModels"
        />
        {{ ageGroupCompCoverageModels }}
      </div>
    </div>

    <div v-if="showSection === 'CREATE'">
      <AgeGroupForm
        :comp-id="compId"
        class="e4s-form-wrapper"
        @onCancel="showSection = 'HELPER'"
        v-on:onSubmit="onAgeGroupCreated"
      >
      </AgeGroupForm>
    </div>
    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { CompeventAgeGroupCoverageService } from "./compevent-age-group-coverage-service";
import CompeventAgeGroupCoverageRow from "./compevent-age-group-coverage-row.vue";
import {
  IAgeGroup,
  IAgeGroupCompCoverageModel,
} from "../../../agegroup/agegroup-models";
import { IsoDate } from "../../../common/common-models";
import { format, parse } from "date-fns";
import DateEntry from "../../../common/ui/datetime/date-entry.vue";
import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
import { CommonService } from "../../../common/common-service";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import { AO_CODE } from "../../../common/ui/athletic-org/athletic-org-models";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FieldNumberV2 from "../../../common/ui/layoutV2/fields/field-number-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import DateInputDropDownV2 from "../../../common/ui/layoutV2/fields/date-input-drop-down-v2.vue";
import AgeGroupForm from "../../../agegroup/agegroup-form.vue";
import { BUILDER_STORE_CONST } from "../../../builder/builder-store-constants";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";

const compeventAgeGroupCoverageService = new CompeventAgeGroupCoverageService();

@Component({
  name: "compevent-age-group-coverage",
  components: {
    LoadingSpinnerV2,
    AgeGroupForm,
    DateInputDropDownV2,
    PrimaryLink,
    ButtonGenericV2,
    FieldNumberV2,
    FieldTextV2,
    FieldHelp,
    DateEntry,
    CompeventAgeGroupCoverageText: () => {
      return import(
        /* webpackPrefetch: true */
        /* webpackChunkName: "compevent-age-group-coverage-text" */
        "./compevent-age-group-coverage-text.vue"
      );
    },
    CompeventAgeGroupCoverageRow,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class CompeventAgeGroupCoverage extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageGroupCompCoverageModels: IAgeGroupCompCoverageModel[];

  @Prop({
    required: true,
  })
  public readonly aoCode: AO_CODE;

  @Prop({
    default: true,
  })
  public readonly showHelperRow: boolean;

  @Prop({
    default: false,
  })
  public readonly showCreateButton: boolean;

  @Prop({
    default: "",
  })
  public compDate: IsoDate;

  @Prop({
    default: 0,
  })
  public compId: number;

  public rowMap: Record<string, IAgeGroupCompCoverageModel[]> = {};

  public showSection: "HELPER" | "CREATE" = "HELPER";

  public isLoading = false;

  public yearsToDisplay = {
    min: 0,
    max: 100,
  };

  public dateOfBirthInternal: IsoDate = "";
  public commonService = new CommonService();
  public showRaw = false;

  public created() {
    if (this.ageGroupCompCoverageModels.length > 0) {
      this.rowMap = compeventAgeGroupCoverageService.buildSummary(
        this.ageGroupCompCoverageModels
      );
    }
  }

  @Watch("ageGroupCompCoverageModels")
  public onAgeGroupCompCoverageModelsChanged(
    newValue: IAgeGroupCompCoverageModel[],
    oldValue: IAgeGroupCompCoverageModel[]
  ) {
    if (!R.equals(newValue, oldValue)) {
      const rowMap = compeventAgeGroupCoverageService.buildSummary(newValue);
      this.rowMap = R.clone(rowMap);

      this.yearsToDisplay =
        compeventAgeGroupCoverageService.getMaxMinAge(newValue);
    }
  }

  @Watch("rowMap")
  public onRowMapChanged(
    newValue: Record<string, IAgeGroupCompCoverageModel[]>,
    oldValue: Record<string, IAgeGroupCompCoverageModel[]>
  ) {
    const hasOverLap = Object.keys(newValue).length > 1;
    console.log("hasOverLap", hasOverLap);
    this.$emit("hasOverLap", hasOverLap);
  }

  public get calcAgeInYears(): string {
    if (this.compDate.length === 10 && this.dateOfBirthInternal.length === 10) {
      const age = this.commonService.ageBetweenDates(
        this.dateOfBirthInternal,
        this.compDate
      );
      return (
        "Age on " +
        format(parse(this.compDate), "Do MMM YYYY") +
        ": " +
        age.message
      );
    }
    return "";
  }

  public get getCompDateDisplay() {
    if (this.compDate.length === 0) {
      return "";
    }
    return format(parse(this.compDate), "Do MMM YYYY");
  }

  public onAgeGroupCreated(ageGroup: IAgeGroup) {
    console.log("CompeventAgeGroupCoverage.onAgeGroupAllSelected", ageGroup);
    this.isLoading = true;
    this.$store
      .dispatch(
        BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
          "/" +
          BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_AGE_GROUPS_DEFAULT,
        {
          aocode: this.configApp.defaultao.code,
          compId: this.compId,
        }
      )
      .then(() => {
        this.showSection = "HELPER";
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public ageGroupClicked(
    ageGroupCompCoverageModel: IAgeGroupCompCoverageModel
  ) {
    this.$emit("ageGroupClicked", R.clone(ageGroupCompCoverageModel));
  }
}
</script>
