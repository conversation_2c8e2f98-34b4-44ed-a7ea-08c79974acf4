import * as R from "ramda";

describe("CompEventService", () => {

    test("R.merge", () => {
        const objCompEvt = {height: 192, age: 50, showPb: true};
        const objEvt = {name: "bob", age: 34, showPb: false};

        const result = R.merge(objCompEvt, objEvt);
        expect(result.height).toBe(192);
        expect(result.age).toBe(34);
        expect(result.showPb).toBe(false);
    });

    test("mergeDeepRight", () => {
        const objCompEvt = {
            height: 192,
            age: 50,
            showPb: true,
            options: {
                qaz: 1,
                foo: "23",
                address: {
                    line1: "blah"
                }
            }
        };
        const objEvt = {
            name: "bob",
            age: 34,
            showPb: false,
            height: 111
        };

        const result = R.merge(objCompEvt, objEvt);
        expect(result.height).toBe(111);
        expect(result.age).toBe(34);
        expect(result.showPb).toBe(false);
        expect(result.options.foo).toBe("23");
        expect(result.options.address.line1).toBe("blah");
    });


});
