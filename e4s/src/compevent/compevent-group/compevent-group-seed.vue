<template>
  <div>
    <div class="e4s-section-padding-separator"></div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div class="e4s-section-divider-line--simple-grey"></div>
        <div class="e4s-section-header-title">
          <i class="material-icons mat-icon-img">call_split</i
          >&nbsp;Seeding&nbsp;
          <FieldHelp
            :get-from-server="true"
            help-key="event-group-seeding"
          />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col s6 m3 l3">
        <label class="e4s-field-label" for="heat-order-slow">
          Seeding Format
        </label>
        <div>
          <label>
            <input
              type="radio"
              id="heat-order-slow"
              class="browser-default"
              value="O"
              v-model="valueInternal.type"
              v-on:change="changed"
            />
            <span>Open</span>
          </label>

          <label>
            <input
              type="radio"
              id="heat-order-fast"
              class="browser-default"
              value="H"
              v-model="valueInternal.type"
              v-on:change="changed"
            />
            <span>Heats</span>
          </label>
        </div>
      </div>

      <div class="col s6 m3 l3" v-if="valueInternal.type === 'H'">
        <label class="e4s-field-label" :for="PREFIX + 'qualifyToEg'">
          <span
            >Seed which event
            <span
              v-text="
                ' ' +
                valueInternal.qualifyToEg.id +
                ' ' +
                valueInternal.qualifyToEg.name
              "
            >
            </span>
          </span>
        </label>
        <EventGroupSelect
          :id="PREFIX + 'qualifyToEg'"
          v-model="valueInternal.qualifyToEg"
          :comp-events-schedule="allCompEvents"
          v-on:input="changed"
        />
      </div>

      <div class="col s6 m3 l3">
        <label>
          <input
            id="seeding-age"
            class="e4s-checkbox"
            type="checkbox"
            v-model="valueInternal.age"
          />
          <span>Seed by Age</span>
        </label>
      </div>

      <div class="col s6 m3 l3">
        <label>
          <input
            id="seeding-gender"
            class="e4s-checkbox"
            type="checkbox"
            v-model="valueInternal.gender"
          />
          <span>Seed by Gender</span>
        </label>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import {
  defineComponent,
  PropType,
  Ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { ref } from "@vue/composition-api";
import { IEventSeed } from "../../athleteCompSched/athletecompsched-models";
import { IBuilderCompetition } from "../../builder/builder-models";
import FieldHelp from "../../common/ui/field/field-help/field-help.vue";
import EventGroupSelect from "../../builder/form/event-group/event-group-select.vue";
import { ICompEvent } from "../compevent-models";
import { CompEventService } from "../compevent-service";
import { useSimpleObjectInputController } from "../../common/ui/form/form-controller/useSimpleObjectChangeController";

const compEventService = new CompEventService();

export default defineComponent({
  name: "compevent-group-seed",
  components: { EventGroupSelect, FieldHelp },
  props: {
    value: {
      type: Object as PropType<IEventSeed>,
      default: () => {
        return compEventService.factoryEventSeed();
      },
    },
    builderCompetition: {
      type: Object as PropType<IBuilderCompetition>,
      required: true,
    },
    allCompEvents: {
      type: Array as PropType<ICompEvent[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(
    props: {
      value: IEventSeed;
      builderCompetition: IBuilderCompetition;
      allCompEvents: ICompEvent[];
    },
    context: SetupContext
  ) {
    const valueInternal = ref(R.clone(props.value)) as Ref<IEventSeed>;
    const PREFIX = Math.random().toString(36).substring(2);

    const simpleFormController = useSimpleObjectInputController<IEventSeed>(
      valueInternal.value,
      context
    );

    watch(
      () => props.value,
      (currentValue, oldValue) => {
        const areEqual = R.equals(currentValue, valueInternal.value);
        if (!areEqual) {
          console.log("compevent-group-seed2  PROP CHANGED!!!");
          valueInternal.value = R.clone(currentValue);
        }
      }
    );

    function changed() {
      context.emit("input", R.clone(valueInternal.value));
    }

    return {
      valueInternal,
      PREFIX,
      simpleFormController,
      changed
    };
  },
});
</script>
