export async function copyFile(
  sourceDirHandle: any,
  destDirHandle: any,
  oldName: string,
  newName: string
): Promise<any> {
  // Get a handle to the source file
  const sourceFileHandle = await sourceDirHandle.getFileHandle(oldName);
  // Get a writable handle to the destination file
  const destFileHandle = await destDirHandle.getFileHandle(newName, {
    create: true,
  });

  const sourceFile = await sourceFileHandle.getFile();
  console.log("sourceFile", sourceFile);
  // const sourceStreamFile = await sourceFile.createReadable();
  // console.log("sourceStreamFile", sourceStreamFile);

  // const contents = await sourceFile.text();

  // Read the contents of the File object into an ArrayBuffer
  const sourceBuffer = await sourceFile.arrayBuffer();

  // Create a writable stream to the destination file
  const destStream = await destFileHandle.createWritable();
  console.log("destStream", destStream);

  await destStream.write(sourceBuffer);

  // Create a readable stream from the source file
  // const sourceStream = await sourceFileHandle.createReadable();

  // Pipe the source stream to the destination stream
  // await sourceStream.pipeTo(destStream);
  // Close both streams
  // await sourceStream.close();
  await destStream.close();

  return destFileHandle;
}

export async function getFilesNamesInDirectory(
  directoryHandle: any
): Promise<string[]> {
  const currentFiles: string[] = [];
  console.log("getFilesNamesInDirectory...start...");
  for await (const entry of directoryHandle.values()) {
    console.log("getFilesNamesInDirectory file : " + entry.name);
    if (entry.kind !== "file") {
      continue;
    }
    currentFiles.push(entry.name);
  }
  return currentFiles;
}
