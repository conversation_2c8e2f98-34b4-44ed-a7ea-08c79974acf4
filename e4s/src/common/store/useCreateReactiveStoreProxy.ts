export function useCreateReactiveStoreProxy<Data>(data: Data) {
  const observers: ((changes: Data) => unknown)[] = [];

  function addObserver(observer: (changes: Data) => unknown) {
    observers.push(observer);
  }

  function notifyObservers(changes: Data) {
    observers.forEach((observer) => observer(changes));
  }

  const handler = {
    //  @ts-ignore
    set(target, prop, value) {
      const oldValue = target[prop];
      const success = Reflect.set(target, prop, value);

      if (success && oldValue !== value) {
        //  @ts-ignore
        notifyObservers({ prop, oldValue, newValue: value });
      }

      return success;
    },
    //  @ts-ignore
    get(target, prop, receiver) {
      if (prop.startsWith("_")) {
        throw new Error(`Access denied: ${prop} is a private property`);
      }
      return Reflect.get(target, prop, receiver);
    },
  };

  const proxy = new Proxy(data, handler);
  return { data: proxy, addObserver };
}
