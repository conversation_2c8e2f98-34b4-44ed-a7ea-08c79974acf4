<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import { IBase, IServerResponse } from "../../common-models";
    import { ResourceData } from "../../resource/resource-service";
    import { messageDispatchHelper } from "../../../user-message/user-message-store";
    import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
    import LoadingSpinner from "../loading-spinner.vue";

    @Component({
        name: "common-route-container",
        components: {
            "loading-spinner": LoadingSpinner
        }
    })
    export default class CommonRouteContainer extends Vue {

        public routeId: number = 0;
        public isLoading: boolean = false;
        public resourceData: ResourceData<IBase>;
        public model: IBase = {id: 0};
        public serverResponse: IServerResponse<IBase>;
        public getDataPromise: Promise<IServerResponse<IBase>>;

        public mounted() {
            const id: number = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);
            this.routeId = id;

            if ( id > 0) {
                this.searchById();
            }
        }

        public searchById() {
            this.isLoading = true;
            let prom;
            if (this.getDataPromise) {
                //  @ts-ignore
                prom = this.getDataPromise.call(this.resourceData, this.routeId);
            } else {
                prom = this.resourceData.read(this.routeId);
            }
            // this.resourceData.read(this.routeId)
            prom
                .then((response: IServerResponse<IBase>) => {
                    if (response.errNo > 0) {
                        messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return;
                    }
                    this.model = response.data;
                    this.serverResponse = response;

                    return;
                })
                .catch((error: any) => {
                    messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return {};
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }

    }
</script>
