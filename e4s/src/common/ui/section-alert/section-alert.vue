<template>
  <table>
    <tr
      :class="isGreen ? 'e4s-indicator--row-green' : 'e4s-indicator--row-red'"
    >
      <td
        class="e4s-indicator--td"
        :class="isGreen ? 'e4s-indicator--green' : 'e4s-indicator--red'"
      ></td>
      <td class="section-alert--content">
        <slot name="header-content">
          <div
            v-if="headerTitle.length > 0"
            class="section-alert--header-title"
          >
            <span v-text="headerTitle"></span>
          </div>
        </slot>
        <slot name="content"></slot>
      </td>
      <td
        class="e4s-indicator--td"
        :class="isGreen ? 'e4s-indicator--green' : 'e4s-indicator--red'"
      ></td>
    </tr>
  </table>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";

@Component({
  name: "section-alert",
  components: {},
})
export default class SectionAlert extends Vue {
  @Prop({ default: "" })
  public readonly headerTitle!: string;

  @Prop({ default: false })
  public readonly isGreen!: boolean;
}
</script>

<style scoped>
.section-alert--header-title {
  font-size: 20px;
}

.section-alert--content {
  padding: 1rem;
}
</style>
