import * as CommonServiceUtils from "../../common-service-utils";
import * as PageCyclingService from "./page-cycling-service";
import { reactive } from "@vue/composition-api";

export interface IUsePageCyclingConfigBase {
  pageSize: number;
  pageCycleMs: number;
}

export interface IUsePageCyclingConfig<ObjectType>
  extends IUsePageCyclingConfigBase {
  onStartedPageCycle?: (pageNumber: number) => void;
  onFinishPageCycle?: (pageNumber: number) => unknown;
  onPageChange?: (pageNumber: number) => void;
}

export interface IUsePageCyclingState<ObjectType> {
  config: {
    pageSize: number;
    pageCycleMs: number;
  };
  pageDisplaying: number;
  pagesTotal: number;
  sourceObjects: ObjectType[];
  pageChunks: ObjectType[][];
  objectsToDisplay: ObjectType[];
}

export function usePageCycling<ObjectType>(
  initialConfig: IUsePageCyclingConfig<ObjectType>
) {
  const state = reactive(PageCyclingService.factoryUsePageCyclingState());
  let timerPageCycle: number = 0;

  const verifiedConfig = getVerifiedConfig(initialConfig);

  state.config.pageSize = verifiedConfig.pageSize;
  state.config.pageCycleMs = verifiedConfig.pageCycleMs;

  function setData(
    objects: ObjectType[],
    config: IUsePageCyclingConfig<ObjectType>
  ) {
    state.sourceObjects = CommonServiceUtils.simpleClone(objects);

    const vConfig = getVerifiedConfig(config);

    state.config.pageSize = vConfig.pageSize;
    state.config.pageCycleMs = vConfig.pageCycleMs;
    init();
  }

  function getVerifiedConfig(
    config: IUsePageCyclingConfigBase
  ): IUsePageCyclingConfigBase {
    const configInternal = CommonServiceUtils.simpleClone(config);
    if (configInternal.pageSize <= 0) {
      configInternal.pageSize = 1;
    }
    if (configInternal.pageCycleMs <= 0) {
      configInternal.pageCycleMs = 5000;
    }
    return configInternal;
  }

  function init() {
    stopPageCycle();
    state.pageDisplaying = 1;
    state.pagesTotal = CommonServiceUtils.howManyPages(
      state.sourceObjects,
      state.config.pageSize
    );

    state.pageChunks = CommonServiceUtils.chunkArray(
      state.sourceObjects,
      state.config.pageSize
    );
    if (state.sourceObjects.length > 0) {
      state.objectsToDisplay = state.pageChunks[0];
    }
    startPageCycle();
  }

  function startPageCycle() {
    timerPageCycle = window.setTimeout(() => {
      console.log(
        "usePageCycling.startPageCycle...ms: " + state.config.pageCycleMs
      );

      if (state.pageDisplaying === 0) {
        if (initialConfig.onStartedPageCycle) {
          initialConfig.onStartedPageCycle(state.pageDisplaying);
        }
      }

      state.pageDisplaying++;
      if (state.pageDisplaying > state.pagesTotal) {
        if (initialConfig.onFinishPageCycle) {
          initialConfig.onFinishPageCycle(state.pageDisplaying);
        }
        state.pageDisplaying = 1;
      }

      if (state.pageDisplaying > 1) {
        if (initialConfig.onPageChange) {
          initialConfig.onPageChange(state.pageDisplaying);
        }
      }

      state.objectsToDisplay = state.pageChunks[state.pageDisplaying - 1];

      console.log(
        "usePageCycling.startPageCycle...pageDisplaying: " +
          state.pageDisplaying
      );
      startPageCycle();
    }, state.config.pageCycleMs);
  }

  function stopPageCycle() {
    clearTimeout(timerPageCycle);
  }

  return {
    setData,
    state,
    stopPageCycle,
    startPageCycle,
  };
}
