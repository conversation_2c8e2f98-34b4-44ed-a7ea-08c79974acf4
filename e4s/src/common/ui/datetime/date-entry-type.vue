<template>
  <FormGenericInputTextV2
    class="date-entry-type"
    v-model="dateValue"
    :showLabel="false"
    :placeHolder="userInputFormat"
    :is-required-field="isRequiredField"
    v-on:input="inputChanged"
  />
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { CommonService } from "../../common-service";
import { parse, format } from "date-fns";
import FormGenericInputTextV2 from "../layoutV2/form/form-generic--input-text-v2.vue";

@Component({
  name: "date-entry-type",
  components: { FormGenericInputTextV2 },
})
export default class DateEntryType extends Vue {
  @Prop() public isoDate: string;
  @Prop({ default: "DD/MM/YYYY" }) public userInputFormat: string;
  @Prop({ default: false }) public readonly isRequiredField: boolean;

  public dateValue: string = "";
  public isValidDate: boolean = true;
  public commonService: CommonService = new CommonService();

  public created() {
    this.transformsIn();
  }

  @Watch("isoDate")
  public inputIsoDateChanged(isoDateNew: string, isoDateOld: string) {
    if (isoDateNew !== isoDateOld) {
      this.transformsIn();
    }
  }

  public transformsIn() {
    let dateFns;
    const dateTimeToProcess = this.isoDate;
    if (dateTimeToProcess.length > 0) {
      dateFns = parse(dateTimeToProcess);
      this.dateValue = format(dateFns, this.userInputFormat);
    } else {
      this.dateValue = "";
    }
  }

  public transformsOut() {
    // if (this.saleDate.length > 0 ) {
    //     this.price.saleEndDate = this.commonService.convertUserDateTimeInputToIso(this.saleDate, "00:00");
    // }
  }

  public inputChanged() {
    let outPut = "";
    if (this.dateValue.length > 0) {
      outPut = this.commonService.convertUserDateTimeInputToIso(
        this.dateValue,
        "00:00"
      );
      this.isValidDate = true;
      if (outPut.toUpperCase() === "INVALID DATE") {
        this.isValidDate = false;
        return;
      }
    }
    this.$emit("onInputChanged", outPut);
  }
}
</script>

<style>
.date-entry-type--input {
  width: 8rem !important;
}

.date-entry-type {
  width: 120px;
}
</style>
