<template>
  <div>
    <slot name="debug"></slot>

    <slot name="date">
      <div class="col s12 m6 l6">
        <slot name="label-date">
          <label class="active"> Date </label>
        </slot>

        <date-entry-mat
          :iso-date="isoDateTimeInternal"
          v-on:onSelected="onDateSelected"
        >
        </date-entry-mat>
      </div>
    </slot>

    <slot name="time">
      <div class="col s12 m6 l6">
        <slot name="label-time">
          <label class="active"> Time </label>
        </slot>

        <time-entry
          :iso-date-time="isoDateTimeInternal"
          :minute-interval="minuteInterval"
          v-on:onSelected="onTimeSelected"
        >
        </time-entry>
      </div>

      <!--            <input-->
      <!--                    v-model="timePart"-->
      <!--                    @change="onChangeTime"-->
      <!--                    :placeholder="getTimeDisplayFormat">-->
      <!--            <time-entry-mat :iso-date="isoDateTimeInternal"></time-entry-mat>-->
    </slot>
  </div>
</template>

<script lang="ts">
import { format, parse } from "date-fns";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import DateEntryMat from "./date-entry-mat.vue";
import { CommonService } from "../../common-service";
import TimeEntry from "./time-entry.vue";

@Component({
  name: "date-time-entry",
  components: {
    "date-entry-mat": DateEntryMat,
    "time-entry": TimeEntry,
  },
})
export default class DateTimeEntry extends Vue {
  @Prop({ default: "" }) public value: string;
  @Prop({ default: "" }) public isoDateTime: string;
  @Prop({ default: 1 }) public readonly minuteInterval: number;

  public isoDateTimeInternal: string = "";
  public datePart: string = "";
  public timePart: string = "";

  public commonService: CommonService = new CommonService();

  // public created() {
  //   if (this.isoDateTime.length > 0) {
  //     this.isoDateTimeInternal = this.isoDateTime;
  //     this.init();
  //   }
  // }

  @Watch("isoDateTime", {immediate: true})
  public inputIsoDateChanged(isoDateNew: string) {
    this.isoDateTimeInternal = isoDateNew;
    this.init();
  }

  @Watch("value", { immediate: true })
  public inputvalueChanged(isoDateNew: string) {
    this.isoDateTimeInternal = isoDateNew;
    this.init();
  }

  public init() {
    if (this.isoDateTimeInternal.length > 0) {
      this.datePart = format(parse(this.isoDateTimeInternal), "YYYY-MM-DD");
      this.timePart = format(parse(this.isoDateTimeInternal), "HH:mm");
    }
  }

  public onDateSelected(isoDate: string) {
    this.datePart = format(parse(isoDate), "YYYY-MM-DD");
    this.outputResult();
  }

  public onTimeSelected(isoTime: string) {
    this.timePart = isoTime;
    this.outputResult();
  }

  public onChangeTime() {
    this.outputResult();
  }

  public get getTimeDisplayFormat() {
    return "HH:MM";
  }

  public outputResult() {
    const result = this.commonService.convertToIsoDateTimeWithOffset(
      this.datePart,
      this.timePart
    );
    if (result.toUpperCase() !== "INVALID DATE") {
      this.isoDateTimeInternal = result;
      this.$emit("onSelected", result);
      this.$emit("input", result);
    }
  }
}
</script>
