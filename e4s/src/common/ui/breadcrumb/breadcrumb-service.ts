import { BreadCrumbControllerState } from "./useBreadCrumb";

export function factoryBreadCrumbControllerState(): BreadCrumbControllerState {
  return {
    breadCrumbsMap: {
      HOME: {
        name: "HOM<PERSON>",
        title: "Home",
        isClickable: true,
        isVisible: true,
        isLast: false,
      },
      COMP_HOME: {
        name: "COMP_HOME",
        title: "Competition",
        isClickable: true,
        isVisible: true,
        isLast: false,
      },
      COMP_ATHLETES: {
        name: "COMP_ATHLETES",
        title: "Athletes",
        isClickable: true,
        isVisible: false,
        isLast: false,
      },
      COMP_ATHLETE: {
        name: "COMP_ATHLETE",
        title: "Athlete",
        isClickable: true,
        isVisible: false,
        isLast: false,
      },
      COMP_ATHLETE_SCHEDULE: {
        name: "COMP_ATHLETE_SCHEDULE",
        title: "Schedule",
        isClickable: true,
        isVisible: false,
        isLast: false,
      },
      COMP_TEAMS: {
        name: "COMP_TEAMS",
        title: "My Teams",
        isClickable: true,
        isVisible: false,
        isLast: false,
      },
    },
  };
}
