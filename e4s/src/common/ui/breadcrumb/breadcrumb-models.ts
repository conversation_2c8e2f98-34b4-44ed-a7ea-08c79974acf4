export type breadCrumbName =
  | "HOME"
  | "COMP_HOME"
  | "COMP_ATHLETES"
  | "COMP_ATHLETE"
  | "COMP_ATHLETE_SCHEDULE"
  | "COMP_TEAMS";

export interface BreadCrumbBase {
  name: breadCrumbName;
  title?: string;
}

export interface BreadCrumb extends BreadCrumbBase {
  isClickable: boolean; //  E.g. is it the last breadcrumb?
  isVisible: boolean;
  isLast: boolean;
  icon?: string;
  params?: any;
}
