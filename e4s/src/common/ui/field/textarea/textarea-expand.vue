<template>
  <div
    contenteditable="true"
    class="e4s-textarea-expand--default"
    v-model="input"
    v-on:keyup="onChange"
  ></div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
// import {debounce} from "../../../debounce"

@Component({
  name: "textarea-expand",
})
export default class TextareaExpand extends Vue {
  @Prop({
    default: "",
  })
  public readonly input: string;

  public userEnteredText = "";

  public debounceOnChange: any;

  // public created(): void {
  //     this.debounceOnChange = debounce(() => {
  //         this.onChange(null);
  //     }, 500);
  // }

  public onChange(evt: any): void {
    this.userEnteredText = evt.target.innerText;

    this.$emit("input", this.userEnteredText);
  }
}
</script>

<style>
.e4s-textarea-expand--default {
  height: 75px;
  border: 1px solid gray;
  padding: 5px;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
