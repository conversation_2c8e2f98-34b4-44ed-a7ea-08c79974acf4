<template>
  <div>
    <div class="row" v-show="showSection === sections.LIST">
      <div class="col s1 m12 l12">
        <div class="e4s-flex-column e4s-gap--standard">
          <div class="e4s-vertical-spacer"></div>

          <div class="e4s-flex-row">
            <span class="e4s-header--400">Help Configuration</span>

            <ButtonGenericV2
              text="Create"
              @click="create"
              class="e4s-flex-row--end"
            />
          </div>

          <InputWithButton>
            <FieldTextV2
              slot="field"
              class="e4s-square--right e4s-flex-grow"
              v-model="startsWith"
              place-holder="Enter search..."
              v-on:keyUpEnter="search"
            />
            <button-generic-v2
              class="e4s-button--auto"
              with-input="right"
              v-on:click="search"
              slot="after"
            />
          </InputWithButton>
        </div>
      </div>
    </div>
    <div class="row" v-show="showSection === sections.LIST">
      <div class="col s1 m12 l12">
        <div class="e4s-flex-column">
          <div class="e4s-vertical-spacer"></div>

          <HelpList
            :help-data-items="helpDataItems"
            v-on:edit="edit"
            v-on:deleteHelp="getDeleteHelpConf"
          >
          </HelpList>
        </div>
      </div>
    </div>

    <HelpForm
      :help-data="helpData"
      :is-loading="isLoading"
      v-show="showSection === sections.FORM"
      v-on:cancel="cancel"
      v-on:submit="submit"
    >
    </HelpForm>

    <E4sModal
      header-message="Delete"
      v-if="showDeleteConf"
      :is-loading="isLoading"
      :body-message="'Are you sure you want to delete: ' + helpData.key + '?'"
      v-on:closeSecondary="showDeleteConf = false"
      v-on:closePrimary="deleteHelp"
    >
    </E4sModal>

    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { FieldHelpData } from "./field-help-data";
import { FieldHelpService } from "./field-help-service";
import { IHelpData } from "./field-help-store";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import HelpList from "./help-list.vue";
import HelpForm from "./help-form.vue";
import { IServerResponse } from "../../../common-models";
import { handleResponseMessages } from "../../../handle-http-reponse";
import E4sModal from "../../e4s-modal.vue";
import InputWithButton from "../../layoutV2/fields/InputWithButton.vue";
import ButtonGenericV2 from "../../layoutV2/buttons/button-generic-v2.vue";
import FieldTextV2 from "../../layoutV2/fields/field-text-v2.vue";
import LoadingSpinnerV2 from "../../loading-spinner-v2.vue";

@Component({
  name: "HelpFormList",
  components: {
    LoadingSpinnerV2,
    FieldTextV2,
    ButtonGenericV2,
    InputWithButton,
    E4sModal,
    HelpForm,
    HelpList,
  },
})
export default class HelpFormList extends Vue {
  public fieldHelpData: FieldHelpData = new FieldHelpData();
  public fieldHelpService: FieldHelpService = new FieldHelpService();

  public isLoading: boolean = false;
  public showDeleteConf: boolean = false;
  public helpData: IHelpData = this.fieldHelpService.factoryHelpData();

  public helpDataItems: IHelpData[] = [];

  public startsWith: string = "";

  public sections = {
    FORM: "FORM",
    LIST: "LIST",
    LOADING: "LOADING",
  };
  public showSection: string = this.sections.LIST;

  public created() {
    this.search();
  }

  public search() {
    this.showSection = this.sections.LOADING;

    const prom = this.fieldHelpData.list({
      startswith: this.startsWith,
      pagenumber: 1,
      pagesize: 100,
      sortkey: "",
    });
    handleResponseMessages(prom);
    prom
      .then((response) => {
        if (response.errNo > 0) {
          return;
        }
        this.helpDataItems = response.data;
      })
      .catch(() => {
        return;
      })
      .finally(() => {
        this.isLoading = false;
        this.showSection = this.sections.LIST;
      });
  }

  public create() {
    this.helpData = this.fieldHelpService.factoryHelpData();
    this.showSection = this.sections.FORM;
  }

  public edit(helpData: IHelpData) {
    this.helpData = helpData;
    this.showSection = this.sections.FORM;
  }

  public cancel() {
    this.helpData = this.fieldHelpService.factoryHelpData();
    this.showSection = this.sections.LIST;
  }

  public submit(helpData: IHelpData) {
    this.isLoading = true;
    let prom: Promise<IServerResponse<IHelpData>>;
    if (helpData.id === 0) {
      prom = this.fieldHelpData.create(helpData);
    } else {
      prom = this.fieldHelpData.update(helpData);
    }
    handleResponseMessages(prom);
    prom
      .then((response) => {
        if (response.errNo > 0) {
          return;
        }
        messageDispatchHelper("Saved.", USER_MESSAGE_LEVEL.INFO.toString());
        this.helpData = this.fieldHelpService.factoryHelpData();
        this.search();
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public getDeleteHelpConf(helpData: IHelpData) {
    this.showDeleteConf = true;
    this.helpData = R.clone(helpData);
  }

  public deleteHelp() {
    const prom = this.fieldHelpData.delete(this.helpData.id);
    handleResponseMessages(prom);
    prom
      .then((response) => {
        if (response.errNo > 0) {
          return;
        }
        messageDispatchHelper("Deleted.", USER_MESSAGE_LEVEL.INFO.toString());
        this.helpData = this.fieldHelpService.factoryHelpData();
        this.search();
      })
      .finally(() => {
        this.isLoading = false;
        this.showDeleteConf = false;
      });
  }
}
</script>
