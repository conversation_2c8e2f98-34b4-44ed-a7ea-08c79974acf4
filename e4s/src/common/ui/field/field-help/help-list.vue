<template>
  <div>
    <div class="e4s-flex-column">
      <!--      <div v-for="(helpData, index) in helpDataItems"></div>-->

      <div class="flex-table">
        <div class="flex-row">
          <div class="col narrow">#Id</div>
          <div class="col wide">Key</div>
          <div class="col wide">Title</div>
          <div class="col narrow"></div>
        </div>
        <div class="flex-row" v-for="(helpData, index) in helpDataItems">
          <div class="col narrow">
            <span v-text="helpData.id"></span>
          </div>
          <div class="col wide">
            (<span v-text="helpData.type"></span>) &nbsp;
            <span v-text="helpData.key"></span>
            <i
              v-if="helpData.preload"
              style="vertical-align: middle; font-size: 15px"
              class="material-icons green-text"
              >arrow_upward</i
            >
          </div>
          <div class="col wide">
            <span v-text="helpData.title"></span>
          </div>
          <div class="col narrow">
            <i
              class="material-icons red-text"
              style="cursor: pointer"
              v-on:click.stop="deleteHelp(index)"
              >delete_forever</i
            >
            <i
              class="material-icons green-text"
              style="cursor: pointer"
              v-on:click.stop="edit(index)"
              >mode_edit</i
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { IHelpData } from "./field-help-store";
import { Prop } from "vue-property-decorator";

@Component({
  name: "HelpList",
})
export default class HelpList extends Vue {
  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly helpDataItems: IHelpData[];

  public isLoading: boolean = false;

  public deleteHelp(index: number) {
    this.$emit("deleteHelp", R.clone(this.helpDataItems[index]));
  }

  public edit(index: number) {
    this.$emit("edit", R.clone(this.helpDataItems[index]));
  }
}
</script>

<style scoped>
/* General table container styling */
.flex-table {
  display: flex;
  flex-direction: column; /* Stack rows vertically */
  width: 100%;
  border: 1px solid #ccc;
  font-family: Arial, sans-serif;
}

/* Each row in the "table" */
.flex-row {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #ddd;
}

/* Columns */
.col {
  padding: 8px;
  box-sizing: border-box;
  text-align: center;
  border-right: 1px solid #ddd;
}

/* Remove right border for the last column */
.col:last-child {
  border-right: none;
}

/* Narrow columns (1 and 4) */
.narrow {
  flex: 0 0 80px; /* Fixed width of 80px */
}

/* Wider columns (2 and 3) */
.wide {
  flex: 1; /* Equal flexible space for columns 2 and 3 */
  background-color: #ffffff;
}

@media (max-width: 600px) {
  .flex-row {
    flex-direction: column;
  }

  .col {
    flex: none;
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #ddd;
  }

  .col:last-child {
    border-bottom: none;
  }
}
</style>
