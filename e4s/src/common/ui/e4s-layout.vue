<template>
    <div>
        <slot name="e4s-header">
            <LaunchHeader></LaunchHeader>
        </slot>
        <slot name="e4s-content">

        </slot>
    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import LaunchHeader from "../../launch/launch-header.vue";

    @Component({
        name: "admin-section",
        components: {
            LaunchHeader
        }
    })
    export default class E4SLayout extends Vue {

    }
</script>
