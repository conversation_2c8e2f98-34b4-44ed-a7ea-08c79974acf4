<template>
    <select class="browser-default"
            v-model="selected"
            v-on:change="onSelected"
            :disabled="isLoading">

        <option v-if="pleaseSelect" :value="{}" disabled selected hidden>Please Choose</option>
        <option v-for="simpleObject in simpleObjectsInternal"
                :value="simpleObject">
            <span v-text="simpleObject[propName]"></span>
        </option>

    </select>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {IBase} from "../common-models";

    @Component({
        name: "simple-object-drop-down"
    })
    export default class SimpleObjectDropDown extends Vue {
        @Prop({
            default: () => {
                return [];
            }
        }) public readonly simpleObjects: IBase[];
        @Prop({
            default: () => {
                return {
                    id: 0
                };
            }
        }) public readonly currentValue: IBase;
        @Prop({default: "name"}) public propName: string;
        @Prop({default: false}) public isLoading: boolean;
        @Prop({default: false}) public pleaseSelect: boolean;

        public simpleObjectsInternal: IBase[] = [];
        public selected: IBase = {id: 0};

        public created() {
            this.simpleObjectsInternal = R.clone(this.simpleObjects);
            this.setSelected();
        }

        public getPleaseSelect(): IBase {
            return {
                id: 0,
                [this.propName]: "Please Select..."
            };
        }

        @Watch("currentValue")
        public onCurrentValueChanged(newValue: IBase, oldValue: IBase) {
            if (newValue.id > 0 && (newValue.id !== oldValue.id)) {
                this.setSelected();
            }
        }

        @Watch("simpleObjects")
        public onSimpleObjectsChanged(newValue: IBase[], oldValue: IBase[]) {
            // this.simpleObjectsInternal = this.pleaseSelect ? [this.getPleaseSelect(), ...newValue] : R.clone(newValue);
            this.setSelected();
        }

        public setSelected() {

            if (this.simpleObjects.length === 0 && this.currentValue.id === 0) {
                return;
            }

            let simpleObjectsInternal = this.simpleObjects;
            simpleObjectsInternal = simpleObjectsInternal.length === 0 && (this.currentValue.id > 0 ) ? [this.currentValue] : this.simpleObjects;

            this.simpleObjectsInternal = this.pleaseSelect ? [this.getPleaseSelect(), ...simpleObjectsInternal] : R.clone(simpleObjectsInternal);

            if (this.simpleObjectsInternal.length === 0 && this.currentValue) {
                this.simpleObjectsInternal = [R.clone(this.currentValue)];
                this.selected = this.simpleObjectsInternal[0];
            }
            this.simpleObjectsInternal.forEach( (simpleObject, index) => {
                if (simpleObject.id === this.currentValue.id ) {
                    // @ts-ignore
                    // console.log("!..!..!..!..!..!..! " + simpleObject.name + "=" + this.simpleObjectsInternal[index].name + " " + (simpleObject === this.simpleObjectsInternal[index]));
                    this.selected = this.simpleObjectsInternal[index];
                }
            });
        }

        public onSelected() {
            this.$emit("onSelected", this.selected);
        }
    }
</script>


























































