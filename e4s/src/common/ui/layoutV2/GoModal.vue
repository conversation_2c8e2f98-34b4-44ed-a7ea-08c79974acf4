<template>
  <div :class="showFullScreenState ? 'go-modal--full-screen' : ''">
    <div v-if="showFullScreenState" class="e4s-flex-row e4s-justify-flex-end">
      <ButtonGenericV2 text="Back" v-on:click="goBack" />
    </div>
    <div v-on:click="processShouldGoFullScreen">
      <slot name="default"></slot>
    </div>
  </div>
</template>

<script lang="ts">
import {
  ref,
  defineComponent,
  SetupContext,
  watch,
} from "@vue/composition-api";
import ButtonGenericV2 from "./buttons/button-generic-v2.vue";

export default defineComponent({
  name: "go-modal",
  components: { ButtonGenericV2 },
  props: {
    showFullScreenState: {
      type: Boolean,
      default: false,
    },
  },
  setup(props: { showFullScreenState: boolean }, context: SetupContext) {
    const showFullScreen = ref(false);


    watch(
      () => props.showFullScreenState,
      (newValue: boolean) => {
        if (showFullScreen.value !== newValue) {
          showFullScreen.value = newValue;
        }
      },
      {
        immediate: true
      }
    );

    function goBack() {
      showFullScreen.value = false;
      context.emit("onGoBack", false)
    }

    function processShouldGoFullScreen() {
      showFullScreen.value = true;
      onFullScreenChanged();
    }

    function onFullScreenChanged() {
      const prop = props.showFullScreenState;
      const current = showFullScreen.value;
      console.log(prop + " =  " + current);
      // context.emit("onFullScreenChanged", showFullScreen.value);
    }

    return {
      showFullScreen,
      processShouldGoFullScreen,
      goBack,
    };
  },
});
</script>

<style>
.go-modal--full-screen {
  position: absolute;
  top: 0;
  left: 0;
  background: white;
  padding: 8px;
  width: 100%;
  height: 100%;
}
</style>
