<template>
  <FieldTextV2 :value="dateHumanReadable" :is-disabled="true"></FieldTextV2>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import FieldTextV2 from "./field-text-v2.vue";
import { IsoDate } from "../../../common-models";
import {
  getE4sStandardHumanDateOutPut,
  isNil,
} from "../../../common-service-utils";

export default defineComponent({
  name: "DateHuman",
  components: { FieldTextV2 },
  props: {
    dateIso: {
      type: String as PropType<IsoDate>,
      required: true,
    },
  },
  setup(props: { dateIso: IsoDate }, context: SetupContext) {
    const dateHumanReadable = ref("");

    watch(
      () => props.dateIso,
      (newValue: IsoDate) => {
        if (!isNil(newValue)) {
          dateHumanReadable.value = getE4sStandardHumanDateOutPut(newValue);
        }
      },
      {
        immediate: true,
      }
    );

    return { dateHumanReadable };
  },
});
</script>
