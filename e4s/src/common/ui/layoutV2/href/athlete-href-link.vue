<template>
  <a :href="link"><span v-text="linkText"></span></a>
</template>

<script lang="ts">
import { computed, defineComponent, SetupContext } from "@vue/composition-api";

import {
  LAUNCH_ROUTES_PATHS_V2,
  LAUNCH_ROUTES_PATHS_V2_BASE,
} from "../../../../launch/v2/launch-routes-v2";

export default defineComponent({
  name: "athlete-href-link",
  components: {},
  props: {
    athleteId: {
      type: Number,
      default: 0,
    },
    linkText: {
      type: String,
      default: "Edit",
    },
  },
  setup(props: { athleteId: number; linkText: string }, context: SetupContext) {
    const link = computed(() => {
      return (
        LAUNCH_ROUTES_PATHS_V2_BASE +
        "/" +
        LAUNCH_ROUTES_PATHS_V2.ATHLETE_V2 +
        "/" +
        props.athleteId
      );
    });

    return { link };
  },
});
</script>
