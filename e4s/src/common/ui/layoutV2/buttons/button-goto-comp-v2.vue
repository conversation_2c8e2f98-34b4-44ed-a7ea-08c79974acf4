<template>
  <ButtonGenericV2
    class="e4s-button--150"
    :class="getCss"
    :text="getEnterButtonText"
    v-on:click="goToComp"
    :disabled="isEnterButtonDisabled"
  />
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";
import { RawLocation } from "vue-router";
import { useRouter } from "../../../../router/migrateRouterVue3";
import ButtonGenericV2 from "./button-generic-v2.vue";
import { useAuthStore } from "../../../../auth/useAuthStore";
import {
  LAUNCH_ROUTES_PATHS_V2,
  LAUNCH_ROUTES_PATHS_V2_DIR,
} from "../../../../launch/v2/launch-routes-v2";
import {
  canUserEnterCompetition,
  enterButtonText,
} from "../../../../competition/v2/competiton-service-v2";
import { useConfigController } from "../../../../config/useConfigStore";
import { UiVersion } from "../../../../config/config-app-models";
// import { useUserRouteV1V2Helper } from "../../../../launch/v2/userRouteV1V2Helper";

export default defineComponent({
  name: "button-goto-comp-v2",
  components: { ButtonGenericV2 },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      default: () => {
        return [];
      },
    },
    buttonText: {
      type: String,
      default: () => {
        return "Enter Competition!";
      },
    },
    configVersion: {
      type: String as PropType<UiVersion>,
      default: "v2",
    },
  },
  setup(
    props: {
      competitionSummaryPublic: ICompetitionSummaryPublic;
      buttonText: string;
      configVersion: UiVersion;
    },
    context: SetupContext
  ) {
    let authStore = useAuthStore();
    const router = useRouter();
    const configController = useConfigController();

    // const routeV1V2Helper = useUserRouteV1V2Helper();

    const isV2Route = computed(() => {
      return router.currentRoute.path.startsWith("/v2");
    });

    const canUserEnter = computed<boolean>(() => {
      return canUserEnterCompetition(
        props.competitionSummaryPublic,
        configController.getStore.value.configApp.userInfo
      );
    });

    const isCompAdmin = computed<boolean>(() => {
      if (configController.isAdmin.value) {
        return true;
      }
      const hasBuilderPermissionForComp =
        configController.hasBuilderPermissionForComp(
          props.competitionSummaryPublic.compOrgId,
          props.competitionSummaryPublic.compId
        );
      console.log(
        "hasBuilderPermissionForComp: " + hasBuilderPermissionForComp
      );
      if (hasBuilderPermissionForComp) {
        return true;
      }
      return false;
    });

    const isEnterButtonDisabled = computed<boolean>(() => {
      if (isCompAdmin.value) {
        return false;
      }
      return !canUserEnter.value;
    });

    const getEnterButtonText = computed<string>(() => {
      if (isCompAdmin.value && !canUserEnter.value) {
        return "Org Enter";
      }
      return enterButtonText(props.competitionSummaryPublic);
    });

    const getCss = computed<string[]>(() => {
      const css: string[] = [];

      //  If you are admin, but users can't enter, then make it red
      if (configController.isAdmin.value && !canUserEnter.value) {
        css.push("e4s-button--destructive");
      }

      if (getEnterButtonText.value.length > 10) {
        css.push(" e4s-button--auto");
      }

      return css;
    });

    function goToComp() {
      const useV2Route =
        props.competitionSummaryPublic.options.useV2Routes || isV2Route.value;
      if (!useV2Route) {
        console.log("ButtonGotoCompV2.goToComp useV1Routes");
        context.emit("goToCompV1");
        return;
      }

      // if (props.configVersion === "v1") {
      //   console.log("ButtonGotoCompV2.goToCompV1");
      //   context.emit("goToCompV1");
      //   return;
      // }

      const basePath = "/" + LAUNCH_ROUTES_PATHS_V2_DIR + "/";

      if (
        props.competitionSummaryPublic.options.ui.entryDefaultPanel ===
        "SHOP_ONLY"
      ) {
        router.push({
          path:
            basePath +
            LAUNCH_ROUTES_PATHS_V2.SHOP_V2 +
            "/" +
            props.competitionSummaryPublic.compId,
        });
        return;
      }

      const compUrl =
        basePath +
        LAUNCH_ROUTES_PATHS_V2.ENTRY_V2 +
        "/" +
        props.competitionSummaryPublic.compId;

      console.log(
        "ButtonGotoCompV2.goToComp: " + compUrl,
        props.competitionSummaryPublic
      );

      const loginUrlV1 = "/login";
      const loginUrlV2 = "/v2/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2;

      const loginUrl = useV2Route ? loginUrlV2 : loginUrlV1;
      console.log("PublicCompCardV2.goToComp: " + loginUrl);

      let location: RawLocation;
      let isLoggedIn = authStore.isLoggedIn;

      // basePath + "/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2
      if (!isLoggedIn) {
        location = {
          path: loginUrl,
          query: {
            redirectFrom: compUrl,
          },
        };
      } else {
        location = {
          path: compUrl,
        };
      }

      router.push(location);
    }
    return {
      getEnterButtonText,
      canUserEnter,
      configController,
      getCss,
      isEnterButtonDisabled,

      goToComp,
    };
  },
});
</script>
