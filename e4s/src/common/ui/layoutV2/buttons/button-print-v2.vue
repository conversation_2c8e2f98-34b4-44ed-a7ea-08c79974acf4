<template>
  <ButtonGenericV2
    :button-type="buttonType"
    :text="text"
    v-on:click="buttonClicked"
  />
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { ButtonGenericType } from "./button-generic-models";
import ButtonGenericV2 from "./button-generic-v2.vue";

export default defineComponent({
  name: "button-print-v2",
  components: { ButtonGenericV2 },
  props: {
    text: {
      type: String,
      default: () => {
        return "Print";
      },
    },
    buttonType: {
      type: String as PropType<ButtonGenericType>,
      default: () => {
        return "tertiary";
      },
      validator: function (value: ButtonGenericType) {
        return ["primary", "secondary", "tertiary"].indexOf(value) !== -1;
      },
    },
  },
  setup(
    props: {
      text: string;
      buttonType: "primary" | "secondary" | "tertiary";
    },
    context: SetupContext
  ) {
    function buttonClicked() {
      window.print();
      context.emit("click");
    }

    return { buttonClicked };
  },
});
</script>
