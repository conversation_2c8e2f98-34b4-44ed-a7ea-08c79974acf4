<template>
  <!--  class="e4s-flex-column e4s-input&#45;&#45;container"-->
  <div :class="getRequiredCss">
    <span class="dat-e4s-input--info-label" v-if="showLabel">
      <slot name="label">
        <label
          :class="cssForLabel"
          :style="'visibility:' + (hideLabel ? 'hidden' : '')"
        >
          <span v-text="formLabel"></span>
          <slot name="after-label"></slot>
        </label>
        <slot name="right-label"></slot>
      </slot>
      <div
        v-if="showHelp"
        @click="helpClicked"
        class="
          e4s-flex-column e4s-flex-center e4s-justify-flex-center
          dat-e4s-input--info-icon-container
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
        >
          <path
            d="M12,6A6,6,0,1,1,6,0a6,6,0,0,1,6,6ZM4.928,4.525h-.99A1.84,1.84,0,0,1,6,2.625c1.048,0,2,.548,2,1.68a1.831,1.831,0,0,1-.933,1.543c-.553.419-.758.576-.758,1.115v.266H5.338l-.005-.347a1.708,1.708,0,0,1,.876-1.49c.443-.333.724-.552.724-1.028a.871.871,0,0,0-.986-.876A.957.957,0,0,0,4.928,4.525Zm.938,4.832a.7.7,0,1,1,0-*******,0,1,1,0,1.4Z"
          />
        </svg>
      </div>
    </span>
    <!--    v-on="$listeners"-->
    <slot name="field"> Place whatever you want here. </slot>
    <p class="e4s-subheader--general" v-text="helpText"></p>
    <slot name="below-field"></slot>
    <p
      v-if="errorMessage.length > 0"
      class="e4s-body--200 e4s-body--error"
      v-text="errorMessage"
    ></p>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, SetupContext } from "@vue/composition-api";

export default defineComponent({
  name: "form-generic-input-template-v2",
  components: {},
  inheritAttrs: false,
  props: {
    formLabel: {
      type: String,
      default: () => {
        return "";
      },
    },
    formLabelClass: {
      type: String,
      default: () => {
        return "e4s-input--label e4s-flex-row e4s-gap--standard";
      },
    },
    errorMessage: {
      type: String,
      default: () => {
        return "";
      },
    },
    showHelp: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isRequiredField: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isRequiredFieldValid: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    showLabel: {
      type: Boolean,
      default: () => {
        //   If false hides the whole label section, which doesn't
        //  really make sense...you'd just dump an input or whatever
        //  on ui...see "hideLabel"
        return true;
      },
    },
    hideLabel: {
      type: Boolean,
      default: () => {
        //  This sets visibility to hidden, so the label + field
        //  still takes up same space...which is most likely
        //  what we are after.
        return false;
      },
    },
    helpText: {
      type: String,
      default: () => {
        return "";
      },
    },
    layoutRowWhenLabelLessThan: {
      type: Number,
      default: () => {
        //  If label short then layout as row.
        //  Default 0 means never and display as column.
        //  3 is a good starting point.
        //  Use with caution, if labels slots used, may need to adjust and css
        //  will need to be adjusted.
        return 0;
      },
    },
  },
  setup(
    props: {
      value: string;
      formLabel: string;
      formLabelClass: string;
      placeHolder: string;
      errorMessage: string;
      showHelp: boolean;
      showLabel: boolean;
      hideLabel: boolean;
      isRequiredField: boolean;
      isRequiredFieldValid: boolean;
      helpText: string;
      layoutRowWhenLabelLessThan: number;
    },
    context: SetupContext
  ) {
    function helpClicked() {
      context.emit("helpClicked");
    }

    const cssForLabel = computed(() => {
      return (
        props.formLabelClass +
        (props.isRequiredField && !props.isRequiredFieldValid
          ? " e4s-input--label-required"
          : "") +
        (props.isRequiredField && props.isRequiredFieldValid
          ? " e4s-input--label-required--is-valid"
          : "")
      );
    });

    const useLayoutRowWhenLabelShort = computed(() => {
      // if props.layoutRowWhenLabelLessThan is zero or less, then never layout as row.
      if (props.layoutRowWhenLabelLessThan <= 0) {
        return false;
      }

      return props.formLabel.length < props.layoutRowWhenLabelLessThan;
    });

    const getRequiredCss = computed(() => {
      const css = [];

      if (useLayoutRowWhenLabelShort.value) {
        css.push("e4s-flex-row");
      } else {
        css.push("e4s-flex-column");
      }

      css.push("e4s-input--container");

      if (props.isRequiredField && !props.isRequiredFieldValid) {
        css.push("xxxxxx");
      }

      return css;
    });

    return {
      cssForLabel,
      getRequiredCss,
      useLayoutRowWhenLabelShort,

      helpClicked,
    };
  },
});
</script>
<style>
.form-generic-input-template-v2--input-container {
  margin: 4px 0;
}
</style>
