<template>
  <div
    class="e4s-flex-column e4s-input--container"
    :class="
      isRequiredField
        ? 'e4s-field-label--required'
        : 'e4s-field-label--optional'
    "
  >
    <span class="dat-e4s-input--info-label" v-if="showLabel">
      <label
        class="e4s-input--label"
        :class="cssForLabel"
        v-text="formLabel"
      ></label>
      <div
        v-if="showHelp"
        class="
          e4s-flex-column e4s-flex-center e4s-justify-flex-center
          dat-e4s-input--info-icon-container
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
        >
          <path
            d="M12,6A6,6,0,1,1,6,0a6,6,0,0,1,6,6ZM4.928,4.525h-.99A1.84,1.84,0,0,1,6,2.625c1.048,0,2,.548,2,1.68a1.831,1.831,0,0,1-.933,1.543c-.553.419-.758.576-.758,1.115v.266H5.338l-.005-.347a1.708,1.708,0,0,1,.876-1.49c.443-.333.724-.552.724-1.028a.871.871,0,0,0-.986-.876A.957.957,0,0,0,4.928,4.525Zm.938,4.832a.7.7,0,1,1,0-1.4.7.7,0,1,1,0,1.4Z"
          />
        </svg>
      </div>
      <slot name="after-label"></slot>
      <span class="e4s-flex-row--end">
        <span
          :class="
            valueInternal.length >= maxLength
              ? 'e4s-info-text--error'
              : 'e4s-subheader--general'
          "
          >Max Length:
          <span v-text="valueInternal.length + ' of ' + maxLength"></span>
        </span>
      </span>
    </span>
    <InputRestrictLength
      :is-disabled="isDisabled"
      :is-required-field="isRequiredField"
      :use-text-area="useTextArea"
      :placeholder="placeHolder"
      :max-length="maxLength"
      input-class="e4s-full-width"
      v-model="valueInternal"
      v-on:input="onChange"
    />
    <p class="e4s-subheader--general" v-text="helpText"></p>
    <slot name="below-field"></slot>
    <p
      v-if="errorMessage && errorMessage.length > 0"
      class="e4s-body--200 e4s-body--error"
      v-text="errorMessage"
    ></p>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import InputRestrictLength from "../../field/input-restrict-length/input-restrict-length.vue";

export default defineComponent({
  name: "FormGenericInputRestrictLength",
  components: { InputRestrictLength },
  inheritAttrs: false,
  props: {
    formLabel: {
      type: String,
      default: () => {
        return "";
      },
    },
    formLabelClass: {
      type: String,
      default: () => {
        return "";
      },
    },
    placeHolder: {
      type: String,
      default: () => {
        return "";
      },
    },
    value: {
      type: String,
      default: () => {
        return "";
      },
    },
    fieldType: {
      type: String as PropType<"text" | "password" | "email">,
      default: () => {
        return "text";
      },
    },
    errorMessage: {
      type: String,
      default: () => {
        return "";
      },
    },
    showHelp: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    showLabel: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isRequiredField: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isRequiredFieldValid: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    helpText: {
      type: String,
      default: () => {
        return "";
      },
    },
    useTextArea: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    maxLength: {
      type: Number,
      default: () => {
        return 100;
      },
    },
  },
  setup(
    props: {
      value: string;
      formLabel: string;
      formLabelClass: string;
      placeHolder: string;
      errorMessage: string;
      showHelp: boolean;
      showLabel: boolean;
      isDisabled: boolean;
      isRequiredField: boolean;
      isRequiredFieldValid: boolean;
      helpText: string;
      useTextArea: boolean;
      maxLength: number;
    },
    context: SetupContext
  ) {
    const initialValue = props.value;
    const valueInternal = ref(props.value);

    // const attrs = useAttrs();

    watch(
      () => props.value,
      (newValue: string) => {
        if (newValue !== valueInternal.value) {
          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    function onChange(newValue: string) {
      valueInternal.value = newValue;
      submit();
    }

    function onKeyUp(newValue: string) {
      valueInternal.value = newValue;
      console.log(
        "form-generic-input-text-v2.onKeyUp() value: " + valueInternal.value
      );
      context.emit("onKeyUp", valueInternal.value);
    }

    function onKeyUpEnter(newValue: string) {
      valueInternal.value = newValue;
      console.log(
        "form-generic-input-text-v2.onKeyUpEnter() value: " +
          valueInternal.value
      );
      context.emit("keyUpEnter", valueInternal.value);
    }

    function submit() {
      console.log(
        "form-generic-input-text-v2.submit() value: " + valueInternal.value
      );
      context.emit("input", valueInternal.value);
      context.emit("change", valueInternal.value);
      context.emit("onIsDirty", valueInternal.value !== initialValue);
      // console.log(">>>>>>>>>>", attrs.value);
    }
    // function keypressEnter() {
    //   console.log(
    //     "form-generic-input-text-v2.keypressEnter() value: " +
    //       valueInternal.value
    //   );
    //   context.emit("keypressEnter", valueInternal.value);
    // }
    //
    // function everything(x: unknown) {
    //   console.log("everything");
    // }

    const cssForLabel = computed(() => {
      return (
        props.formLabelClass +
        (props.isRequiredField && !props.isRequiredFieldValid
          ? " e4s-input--label-required"
          : "") +
        (props.isRequiredField && props.isRequiredFieldValid
          ? " e4s-input--label-required--is-valid"
          : "")
      );
    });

    return {
      valueInternal,
      submit,
      onChange,
      onKeyUpEnter,
      onKeyUp,
      cssForLabel,
    };
  },
});
</script>
