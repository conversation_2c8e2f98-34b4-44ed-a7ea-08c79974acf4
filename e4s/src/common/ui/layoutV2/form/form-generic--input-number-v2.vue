<template>
  <div class="e4s-flex-column e4s-input--container">
    <span class="dat-e4s-input--info-label" v-if="showLabel">
      <label
        class="e4s-input--label"
        :class="formLabelClass"
        v-text="formLabel"
      >
      </label>
      <div
        v-if="showHelp"
        class="
          e4s-flex-column e4s-flex-center e4s-justify-flex-center
          dat-e4s-input--info-icon-container
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
        >
          <path
            d="M12,6A6,6,0,1,1,6,0a6,6,0,0,1,6,6ZM4.928,4.525h-.99A1.84,1.84,0,0,1,6,2.625c1.048,0,2,.548,2,1.68a1.831,1.831,0,0,1-.933,1.543c-.553.419-.758.576-.758,1.115v.266H5.338l-.005-.347a1.708,1.708,0,0,1,.876-1.49c.443-.333.724-.552.724-1.028a.871.871,0,0,0-.986-.876A.957.957,0,0,0,4.928,4.525Zm.938,4.832a.7.7,0,1,1,0-1.4.7.7,0,1,1,0,1.4Z"
          />
        </svg>
      </div>
    </span>

    <FieldNumberV2
      :value="valueInternal"
      :place-holder="placeHolder"
      :error-message="errorMessage"
      :is-disabled="isDisabled"
      :field-size="fieldSize"
      v-on:input="onChange"
      v-on:keyUpEnter="onKeyUpEnter"
    />
    <p class="e4s-subheader--general" v-text="helpText"></p>
    <p
      v-if="errorMessage && errorMessage.length > 0"
      class="e4s-body--200 e4s-body--error"
      v-text="errorMessage"
    ></p>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import FieldNumberV2 from "../fields/field-number-v2.vue";
import { InputNumberLengthType } from "../fields/field-models";

/**
 * Use this as follows, obv' pass whatever native events needed:
 *       <FormGenericInputTextV2
 *        form-label="Priority Code"
 *        v-bind:value="priorityCode"
 *        place-holder="Case in-sensitive.  E.g. if code ABCDE, can enter abcde"
 *        :error-message="message"
 *        @change="priorityCode = $event.target.value"
 *        @keyup.enter="priorityCode = $event.target.value"
 *        />
 */
export default defineComponent({
  name: "form-generic-input-number-v2",
  components: { FieldNumberV2 },
  inheritAttrs: false,
  props: {
    formLabel: {
      type: String,
      default: () => {
        return "";
      },
    },
    formLabelClass: {
      type: String,
      default: () => {
        return "";
      },
    },
    placeHolder: {
      type: String,
      default: () => {
        return "";
      },
    },
    value: {
      type: Number,
      default: 0,
    },
    errorMessage: {
      type: String,
      default: () => {
        return "";
      },
    },
    showHelp: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    showLabel: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    helpText: {
      type: String,
      default: () => {
        return "";
      },
    },
    fieldSize: {
      type: String as PropType<InputNumberLengthType>,
      default: "medium",
      validator: function (value: InputNumberLengthType) {
        const types: InputNumberLengthType[] = ["small", "medium", "large"];

        return types.indexOf(value) !== -1;
      },
    },
  },
  setup(
    props: {
      value: string;
      formLabel: string;
      placeHolder: string;
      errorMessage: string;
      showHelp: boolean;
      showLabel: boolean;
      isDisabled: boolean;
      helpText: string;
      fieldSize: InputNumberLengthType;
    },
    context: SetupContext
  ) {
    const initialValue = props.value;
    const valueInternal = ref(props.value);

    // const attrs = useAttrs();

    watch(
      () => props.value,
      (newValue: string) => {
        if (newValue !== valueInternal.value) {
          valueInternal.value = newValue;
        }
      },
      {
        immediate: true,
      }
    );

    function onChange(newValue: string) {
      valueInternal.value = newValue;
      submit();
    }

    function onKeyUp(newValue: string) {
      valueInternal.value = newValue;
      console.log(
        "form-generic-input-text-v2.onKeyUp() value: " + valueInternal.value
      );
      context.emit("onKeyUp", valueInternal.value);
    }

    function onKeyUpEnter(newValue: string) {
      valueInternal.value = newValue;
      console.log(
        "form-generic-input-text-v2.onKeyUpEnter() value: " +
          valueInternal.value
      );
      context.emit("keyUpEnter", valueInternal.value);
    }

    function submit() {
      console.log(
        "form-generic-input-text-v2.submit() value: " + valueInternal.value
      );
      context.emit("input", valueInternal.value);
      context.emit("onIsDirty", valueInternal.value !== initialValue);
      context.emit("change", valueInternal.value !== initialValue);
      // console.log(">>>>>>>>>>", attrs.value);
    }
    // function keypressEnter() {
    //   console.log(
    //     "form-generic-input-text-v2.keypressEnter() value: " +
    //       valueInternal.value
    //   );
    //   context.emit("keypressEnter", valueInternal.value);
    // }
    //
    // function everything(x: unknown) {
    //   console.log("everything");
    // }

    return { valueInternal, submit, onChange, onKeyUpEnter, onKeyUp };
  },
});
</script>
