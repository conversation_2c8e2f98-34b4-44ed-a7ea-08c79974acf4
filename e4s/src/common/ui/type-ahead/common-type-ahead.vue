<template>
  <div class="e4s-flex-column e4s-full-width common-type-ahead">
    <InputWithButton>
      <div
        slot="field"
        class="e4s-flex-column"
        style="position: relative; flex-grow: 1"
      >
        <InputDebounce
          :id="inputId"
          class="e4s-flex-grow e4s-square--right"
          :default-value="valueForInput"
          :placeholder="placeHolder"
          :default-object-hack="objectHackChanged"
          :is-disabled="isDisabled"
          v-on:input="runSearch"
        />
        <div
          class="common-type-ahead--results-section"
          :class="
            isModal ? '' : 'common-type-ahead--results-section-fullscreen'
          "
          v-if="showResults"
          v-click-outside="clickOutsideReset"
        >
          <div
            v-for="result in results"
            :key="result.id"
            class="common-type-ahead--result"
          >
            <div v-on:click.prevent="selected(result.id)">
              <slot :result="result">
                <span v-text="getObjectDescriptionInternal(result)"></span>
              </slot>
            </div>
          </div>
        </div>
      </div>

      <div slot="after" class="e4s-flex-row">
        <ButtonGenericV2
          class="e4s-square--left common-type-ahead--button"
          :class="isLoading ? 'common-type-ahead--button-loading' : ''"
          button-type="tertiary"
          with-input="right"
          :disabled="isDisabled"
          v-on:click="reset"
        >
          <CancelSmallMinor slot="button-content" v-if="!isLoading" />
          <LoadingSpinnerCircle slot="button-content" v-if="isLoading" />
          <!--
            <LoadingSpinnerMinor
            slot="button-content"
            v-if="isLoading"
          />-->
        </ButtonGenericV2>
        <!--        <LoadingSpinnerMinor/>-->
        <LoadingSpinner
          v-if="useSpinner"
          :style="'visibility: ' + (isLoading ? '' : 'hidden')"
        ></LoadingSpinner>
      </div>
    </InputWithButton>

    <!--    <div-->
    <!--      class="common-type-ahead&#45;&#45;results-section"-->
    <!--      :class="isModal ? '' : 'common-type-ahead&#45;&#45;results-section-fullscreen'"-->
    <!--      v-if="showResults"-->
    <!--      v-click-outside="clickOutsideReset"-->
    <!--    >-->
    <!--      <div-->
    <!--        v-for="result in results"-->
    <!--        :key="result.id"-->
    <!--        class="common-type-ahead&#45;&#45;result"-->
    <!--      >-->
    <!--        <div v-on:click.prevent="selected(result.id)">-->
    <!--          <slot :result="result">-->
    <!--            <span v-text="getObjectDescriptionInternal(result)"></span>-->
    <!--          </slot>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import InputDebounce from "../field/input-debounce.vue";
import { Prop, Watch } from "vue-property-decorator";
import { handleResponseMessages } from "../../handle-http-reponse";
import { IBase, IServerResponseList } from "../../common-models";
import { CommonService } from "../../common-service";
import { simpleClone } from "../../common-service-utils";
import ButtonGenericV2 from "../layoutV2/buttons/button-generic-v2.vue";
import InputWithButton from "../layoutV2/fields/InputWithButton.vue";
import LoadingSpinnerMinor from "../svg/LoadingSpinnerMinor.vue";
import CancelSmallMinor from "../svg/CancelSmallMinor.vue";
import { debounce } from "../../debounce";
import LoadingSpinnerCircle from "../svg/LoadingSpinnerCircle.vue";

/**
 * Use like this...where providing own get-object-description() for display name...
 *
 <CommonTypeAhead
 slot="default"
 :default-object="athlete"
 :data-function="doSearch"
 :get-object-description="getLabelForAthleteAutoComplete"
 :show-cancel-button="false"
 :is-modal="false"
 v-on:selected="onAthleteSelected"
 v-on:reset="reset"
 />

 or...pass in a slot so you can stuck whatever html you want.

 <CommonTypeAhead
 slot="default"
 :default-object="valueInternal"
 :data-function="search"
 :get-object-description="getLabel"
 :show-cancel-button="false"
 :is-modal="isModal"
 v-on:selected="onChanged"
 v-on:reset="reset"
 >
 <div slot-scope="{ result }">
 {{
        result.clubname +
        (configController.isAdmin.value ? " (" + result.id + ")" : "")
      }}
 </div>
 </CommonTypeAhead>

 or do ther scope as HTML...
 <div slot-scope="{ result }">
  <div v-html="getOptionDisplayValue(result)"></div>
 </div>

 where...
 function getOptionDisplayValue(clubCrud: IClubCrud) {
  return clubCrud.clubname + " <b>Hi</b>" +
  (configController.isAdmin.value ? " (" + clubCrud.id + ")" : "")
}

 *
 *
 */

@Component({
  name: "common-type-ahead",
  components: {
    LoadingSpinnerCircle,
    CancelSmallMinor,
    LoadingSpinnerMinor,
    InputWithButton,
    ButtonGenericV2,
    InputDebounce,
  },
})
export default class CommonTypeAhead extends Vue {
  @Prop({
    default: "",
  })
  public readonly defaultValue!: string;

  @Prop({
    default: () => {
      return {
        id: 0,
        name: "",
      };
    },
  })
  public readonly defaultObject: IBase;

  @Prop({
    default: true,
  })
  public readonly showCancelButton!: boolean;

  @Prop({
    default: true,
  })
  public readonly dataFunction!: (
    searchTerm: string
  ) => Promise<IServerResponseList<IBase>>;

  @Prop({
    default: "",
  })
  public readonly getObjectDescription: (object: unknown) => string;

  // If modal, list can expand to content, if NOT modal, restricted in height.
  @Prop({
    default: false,
  })
  public readonly isModal!: boolean;

  @Prop({
    default: false,
  })
  public readonly debounceTime: 100;

  @Prop({ default: false })
  public readonly isDisabled: boolean;

  @Prop({ default: false })
  public readonly resetInputOnSelected: boolean;

  @Prop({ default: true })
  public readonly useClickOutsideReset: boolean;

  /**
   * If true, will show a spinner to the right of the cancel button. But
   * that screws up the layout a bit as stuff moves around.
   * Setting this to true will show the spinner in the button, no layout issues.
   */
  @Prop({ default: false })
  public readonly useSpinner: boolean;

  @Prop({ default: "" })
  public readonly placeHolder: string;

  public isLoading = false;
  public results: IBase[] = [];
  public resultSelected: IBase = {
    id: 0,
    name: "",
  };
  public hasBeenSelected = false;
  public showResults = false;
  public commonService = new CommonService();
  public valueForInput: string = "";
  public PREFIX = Math.random().toString(36).substring(2);
  public inputId = "input-debounce" + this.PREFIX;
  public debounceSearch: any;

  public objectHackChanged: IBase = {
    id: 0,
    name: "",
  };

  // public valueForInput: string = "";

  @Watch("defaultValue")
  public onDefaultValueChanged(newValue: string, oldValue: string) {
    if (newValue !== oldValue) {
      this.valueForInput = newValue;
    }
  }

  @Watch("defaultObject", { immediate: true })
  public onDefaultObjectChanged(newValue: IBase, oldValue: IBase) {
    this.resultSelected = simpleClone(newValue);
    this.setValueForInput();
  }

  public created() {
    this.valueForInput =
      this.defaultValue && typeof this.defaultValue === "string"
        ? this.defaultValue
        : "";
  }

  public mounted() {
    this.debounceSearch = debounce((searchTerm: string) => {
      this.doSearch(searchTerm);
    }, this.debounceTime);
  }

  public runSearch(searchTerm: string) {
    this.debounceSearch(searchTerm);
  }

  public doSearch(searchTerm: string) {
    this.$emit("searchTermChanged", searchTerm);
    if (searchTerm.length === 0) {
      this.showResults = false;
      this.results = [];
      return;
    }

    this.hasBeenSelected = false;
    this.isLoading = true;
    const prom = this.dataFunction(searchTerm);
    handleResponseMessages(prom);
    prom
      .then((resp: IServerResponseList<IBase>) => {
        if (resp.errNo === 0) {
          //  Back end is returning multiple results
          const results = this.commonService.uniqueArrayById<IBase>(resp.data);
          this.results = results;
          this.showResults = true;
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public selected(id: number) {
    const results = this.results.filter((result) => {
      return result.id === id;
    });
    if (results.length === 1) {
      this.resultSelected = results[0];
      this.setValueForInput();
      this.$emit("selected", R.clone(results[0]));
      this.showResults = false;
      this.hasBeenSelected = true;
      if (this.resetInputOnSelected) {
        this.reset(false);
        /*
        this.valueForInput = "";
        //  put a random 10 string character into the name
        this.objectHackChanged = {
          id: 0,
          name: Math.random().toString(36).substring(2, 15),
        };
        */
      }
    }
  }

  public getObjectDescriptionInternal(result: IBase) {
    if (this.getObjectDescription) {
      return this.getObjectDescription(result);
    }
    return "";
  }

  public setValueForInput() {
    if (this.hasBeenSelected && this.resetInputOnSelected) {
      this.valueForInput = "";
    }
    this.valueForInput = this.resultSelected.name
      ? this.resultSelected.name
      : this.getObjectDescriptionInternal(this.resultSelected);
  }

  public get getValueForInput() {
    if (this.hasBeenSelected && this.resetInputOnSelected) {
      return "";
    }
    return this.resultSelected.name
      ? this.resultSelected.name
      : this.getObjectDescriptionInternal(this.resultSelected);
  }

  public clickOutsideReset() {
    if (this.useClickOutsideReset) {
      console.log("clickOutsideReset");
      this.reset();
    }
  }

  public reset(doEmitReset: boolean = true) {
    //  we are not clearing the model, just clearing the text...this is slightly clunky...but works.
    // (document.getElementById(this.inputId) as HTMLInputElement)!.value = "";
    this.results = [];
    this.showResults = false;
    //  put a random 10 string character into the name
    this.objectHackChanged = {
      id: 0,
      name: Math.random().toString(36).substring(2, 15),
    };
    // this.valueForInput = "";
    if (doEmitReset) {
      this.$emit("reset");
    }
  }

  public cancel() {
    this.$emit("cancel");
  }
}
</script>

<style>
.common-type-ahead {
}
.common-type-ahead .common-type-ahead--button {
  width: 45px;
}

.common-type-ahead--button-loading {
  background: var(--orange-300);
}

.common-type-ahead--results-section {
  overflow: auto;
  border: 1px solid #d6d6d6;
  background: var(--orange-100);
  z-index: 9999;
}

.common-type-ahead--results-section-fullscreen {
  position: absolute;
  max-height: 300px;
  top: 100%;
  //margin-top: 50px;
}

.common-type-ahead--result {
  border-bottom: 1px solid #d6d6d6;
  padding: 5px;
}
.common-type-ahead--result:hover {
  background-color: var(--slate-200);
}
</style>
