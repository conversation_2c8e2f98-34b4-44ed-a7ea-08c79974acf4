<template>
    <athletic-org-drop-down :orgs="aos"
                            v-on:onSelected="onSelected">
    </athletic-org-drop-down>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import AthleticOrgDropDown from "./athletic-org-drop-down.vue";
    import {mapState} from "vuex";
    import {CONFIG_STORE_CONST, IConfigStoreState} from "../../../config/config-store";
    import {IAthleticsOrganisation} from "./athletic-org-models";
    import {AthleticOrgService} from "./athletic-org-service";
    import {IConfigApp} from "../../../config/config-app-models";

    @Component({
        name: "athletic-org-select",
        components: {
            "athletic-org-drop-down": AthleticOrgDropDown
        },
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: IConfigStoreState) => state.configApp
            })
        }
    })
    export default class AthleticOrgSelect extends Vue {
        public configApp: IConfigApp;

        public aos: IAthleticsOrganisation[] = [] as IAthleticsOrganisation[];

        public mounted() {
           this.init();
        }

        public init() {
            const athleticOrgService: AthleticOrgService = new AthleticOrgService();
            const configAos: IAthleticsOrganisation[] = R.clone(this.configApp.aos);
            this.aos = [athleticOrgService.factory(), ...configAos];
        }

        public onOrgsChange(newValue: IAthleticsOrganisation[]) {
           this.init();
        }

        public onSelected(athleticsOrganisation: IAthleticsOrganisation) {
            if (athleticsOrganisation.id > 0) {
                this.$emit("onSelected", athleticsOrganisation);
            }
        }
    }

</script>
