<template>
  <div class="no-print">
    <!--Drop Down Section-->

    <LaunchMenuCreate
      id="dropdown--comp-create"
      class="dropdown-content dropdown-content-e4s"
    />

    <!--Drop Down - ADMIN-->
    <LaunchMenuAdmin
      id="dropdown-admin"
      class="dropdown-content dropdown-content-e4s"
    ></LaunchMenuAdmin>
    <!--/Drop Down - ADMIN-->

    <!--Drop Down - MY ACCOUNT-->
    <LaunchMenuAccount
      id="dropdown-my-account"
      class="dropdown-content dropdown-content-e4s"
    ></LaunchMenuAccount>
    <!--/Drop Down - MY ACCOUNT-->

    <!--Drop Down - HELP-->
    <LaunchMenuHelp
      id="dropdown-help"
      class="dropdown-content dropdown-content-e4s"
    ></LaunchMenuHelp>
    <!--/Drop Down - HELP-->

    <!--/Drop Down Section-->

    <nav>
      <!--            <div class="nav-wrapper secondary-background white-text" :class="'e4s-secondary-color-bg-' + configApp.theme">-->
      <!--          <div class="nav-wrapper white-text" :class="'e4s-primary-color-bg-' + configApp.theme">-->
      <div
        class="nav-wrapper white-text"
        :class="'e4s-header-color-bg-' + configApp.theme"
      >
        <a href="#/" class="hide-on-small-only">
          <img class="e4s-aai-logo" :src="configApp.logo" />
        </a>

        <!--        <ConfigVersionSwitchV1-->
        <!--          current-ui-version="v1"-->
        <!--          v-if="userApplication.version.toggle "-->
        <!--        />-->

        <a href="#" data-target="nav-mobile" class="sidenav-trigger">
          <i class="material-icons e4s-nav-icons-squish">menu</i>
        </a>

        <ul class="right">
          <!--          <li v-if="userApplication.impersonating">-->
          <li v-if="false">
            <button
              class="btn waves-effect waves red"
              v-on:click.stop="unImpersonate"
            >
              <span>Un-Impersonate</span>
            </button>
          </li>

          <li v-if="isLoggedIn && $mq !== VUE_MQ_SIZES.MOBILE.name">
            <router-link :to="'/' + launchRoutesPaths.EMAIL_MESSAGES">
              <span id="1234">
                <i class="material-icons small left">email</i>
                <span
                  v-if="getMessageUnreadCount > 0"
                  class="new badge red launch-header--icon-counter"
                  :data-badge-caption="getMessageUnreadCount"
                >
                </span>
              </span>
            </router-link>
          </li>

          <li v-if="$mq !== VUE_MQ_SIZES.MOBILE.name">
            <router-link :to="'/' + launchRoutesPaths.SHOW_ENTRIES">
              <span>Home</span>
            </router-link>
          </li>

          <li
            v-if="$mq !== VUE_MQ_SIZES.MOBILE.name"
            :style="{
              display: $mq === VUE_MQ_SIZES.DESKTOP.name ? '' : 'none',
            }"
          >
            <a
              id="dropdown-trigger--comp-create"
              href="#"
              data-target="dropdown--comp-create"
            >
              Create
              <i class="material-icons right">arrow_drop_down</i>
            </a>
          </li>

          <li
            :style="{
              display: $mq === VUE_MQ_SIZES.DESKTOP.name ? '' : 'none',
            }"
            v-if="
              isLoggedIn &&
              hasAdminMenuAccess &&
              $mq !== VUE_MQ_SIZES.MOBILE.name
            "
          >
            <a
              id="dropdown-trigger-admin"
              href="#"
              data-target="dropdown-admin"
            >
              Admin
              <i class="material-icons right">arrow_drop_down</i>
            </a>
          </li>

          <li
            :style="{
              display: $mq === VUE_MQ_SIZES.DESKTOP.name ? '' : 'none',
            }"
            v-show="isLoggedIn"
          >
            <a
              id="dropdown-trigger-my-account"
              href="#"
              data-target="dropdown-my-account"
            >
              My Account
              <i class="material-icons right">arrow_drop_down</i>
            </a>
          </li>

          <li v-if="$mq !== VUE_MQ_SIZES.MOBILE.name">
            <a
              href="https://support.entry4sports.com/onlinehelp"
              target="_blank"
            >
              Help
            </a>
          </li>

          <!--All Screens-->

          <li v-if="!isLoggedIn">
            <a href="#/login"><span v-text="$t('menu.login')"></span></a>
          </li>

          <li
            v-if="isLoggedIn"
            :style="{
              display:
                $mq === VUE_MQ_SIZES.DESKTOP.name ||
                $mq === VUE_MQ_SIZES.TABLET.name
                  ? ''
                  : 'none',
            }"
          >
            <!--doLogout-->
            <a v-on:click="showLogoutConfirm = true">
              <span v-if="$mq === VUE_MQ_SIZES.MOBILE.name">Log Out</span>
              <span v-if="$mq !== VUE_MQ_SIZES.MOBILE.name">
                Log out: <span v-text="userApplication.display_name"></span>
              </span>
            </a>
          </li>

          <!--          v-if="isLoggedIn"-->
          <li>
            <a v-on:click="miniCartShow(true)">
              <span>
                <i class="material-icons small left">shopping_cart</i>
                <span
                  v-if="getCartItemCount > 0"
                  class="new badge red launch-header--icon-counter"
                  :data-badge-caption="getCartItemCount"
                >
                </span>
              </span>
            </a>
          </li>

          <!--/All Screens-->
        </ul>
      </div>
    </nav>

    <SubBanner
      v-if="hasCredit"
      :user-info="configApp.userInfo"
      :currency="configApp.currency"
    />

    <div v-if="getShowHealth">
      <div class="e4s-section-padding-separator"></div>
      <AdminConfigSection></AdminConfigSection>
    </div>

    <ul id="nav-mobile" class="sidenav e4s-sidenav-mobile sidenav-close">
      <slot name="mobile-menu"> </slot>

      <li>
        <router-link :to="'/' + launchRoutesPaths.SHOW_ENTRIES">
          <span v-text="$t('menu.showEntries')"></span>
        </router-link>
      </li>

      <li v-if="!isLoggedIn">
        <a href="#/login"><span v-text="$t('menu.login')"></span></a>
      </li>

      <li class="divider" v-if="isLoggedIn"></li>
      <LaunchMenuCreate v-if="isLoggedIn" />

      <li class="divider" v-if="isLoggedIn && hasAdminMenuAccess"></li>
      <LaunchMenuAdmin
        v-if="isLoggedIn && hasAdminMenuAccess"
      ></LaunchMenuAdmin>

      <li class="divider" v-if="isLoggedIn"></li>
      <LaunchMenuAccount v-if="isLoggedIn"></LaunchMenuAccount>

      <li class="divider"></li>
      <LaunchMenuHelp></LaunchMenuHelp>

      <li class="divider"></li>
      <li v-if="isLoggedIn">
        <!--                <a v-on:click="doLogout">-->
        <a v-on:click="showLogoutConfirm = true">
          Log out: <span v-text="userApplication.display_name"></span>
        </a>
      </li>

      <li v-if="isLoggedIn">
        <a v-on:click="miniCartShow(true)">
          <span>
            <i class="material-icons small left">shopping_cart</i>
            <span
              v-if="getCartItemCount > 0"
              class="new badge red e4s-cart-counter"
              :data-badge-caption="getCartItemCount"
            >
            </span>
          </span>
        </a>
      </li>
    </ul>

    <E4sModalSimple
      :css-class="
        $mq === VUE_MQ_SIZES.MOBILE.name
          ? 'e4s-modal-container--full-size'
          : 'cart-mini--top-right'
      "
      v-if="isMiniCartDisplayed"
    >
      <StandardForm title="Cart" slot="body">
        <div slot="form-content">
          <CartMini v-on:closeCart="miniCartShow(false)"></CartMini>
          <div slot="buttons"></div>
        </div>
      </StandardForm>
    </E4sModalSimple>

    <E4sModalSimple
      :css-class="
        $mq === VUE_MQ_SIZES.MOBILE.name
          ? 'e4s-modal-container--full-size'
          : 'cart-mini--top-right'
      "
      v-if="showLogoutConfirm === true"
    >
      <StandardForm title="LogOut" slot="body">
        <div slot="form-content" class="e4s-padding--standard">
          <div
            class="e4s-flex-column e4s-gap--large e4s-flex-end"
            style="margin-top: var(--e4s-gap--large)"
          >
            <span>Are you sure you would like to log out?</span>

            <div class="e4s-flex-row e4s-gap--standard">
              <ButtonGenericV2
                text="Cancel"
                button-type="tertiary"
                @click="showLogoutConfirm = false"
              />
              <ButtonGenericV2 text="OK" @click="doLogout" />
            </div>
          </div>
        </div>
      </StandardForm>
    </E4sModalSimple>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { mapGetters, mapState } from "vuex";
import { AUTH_STORE_CONST, IAuthStoreState } from "../auth/auth-store";
import Logout from "../auth/logout.vue";
import { CONFIG_STORE_CONST, IConfigStoreState } from "../config/config-store";
import { LAUNCH_ROUTES_PATHS } from "./launch-routes";
import { IConfigApp, IUserApplication } from "../config/config-app-models";
import { ConfigService } from "../config/config-service";
import {
  ATH_COMP_SCHED_STORE_CONST,
  IAthCompSchedStoreState,
} from "../athletecompsched/store/athletecompsched-store";
import {
  IAthleteCompSched,
  IAthleteCompSchedRuleEvent,
} from "../athletecompsched/athletecompsched-models";
import { Watch } from "vue-property-decorator";
import { VUE_MQ_SIZES } from "..";
import LaunchMenuAdmin from "./launch-menu-admin.vue";
import LaunchMenuAccount from "./launch-menu-account.vue";
import LaunchMenuHelp from "./launch-menu-help.vue";
import E4sModal from "../common/ui/e4s-modal.vue";
import CartMini from "../cart/cart-mini.vue";
import { CONFIG } from "../common/config";
import {
  ISecondaryStoreState,
  SECONDARY_STORE_CONST,
} from "../secondary/secondary-store";
import { IServerResponse } from "../common/common-models";
import { ICartWooCommerce } from "../secondary/secondary-models";
import StandardForm from "../common/ui/standard-form/standard-form.vue";
import CloseIcon from "../common/ui/close-icon.vue";
import E4sModalSimple from "../common/ui/modal/e4s-modal-simple.vue";
import SubBanner from "../entry/sub-banner/sub-banner.vue";
import { Socket } from "socket.io-client";
import { EmailMessageService } from "../email-message/email-message-service";
import ConfigVersionSwitch from "../config/ConfigVersionSwitch.vue";
import ConfigVersionSwitchV1 from "../config/ConfigVersionSwitchV1.vue";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";
import LaunchMenuCreate from "./launch-menu-create.vue";
import { useConfigController } from "../config/useConfigStore";

@Component({
  name: "launch-header",
  components: {
    LaunchMenuCreate,
    ButtonGenericV2,
    ConfigVersionSwitchV1,
    ConfigVersionSwitch,
    AdminConfigSection: () => {
      return import(
        /* webpackPrefetch: true */
        /* webpackChunkName: "admin-config-section" */
        "../location/admin-section/admin-config-section.vue"
      );
    },
    SubBanner,
    E4sModalSimple,
    CloseIcon,
    StandardForm,
    CartMini,
    E4sModal,
    LaunchMenuHelp,
    LaunchMenuAccount,
    LaunchMenuAdmin,
    logout: Logout,
  },
  computed: {
    ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, {
      isLoggedIn: (state: IAuthStoreState) => state.isLoggedIn,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
      userApplication: (state: IConfigStoreState) =>
        state.configApp.userInfo.user,
      il8nLanguageX: (state: IConfigStoreState) => state.il8nLanguage,
    }),
    ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME, {
      cartItems: (state: IAthCompSchedStoreState) => state.eventsSelected,
      cart: (state: IAthCompSchedStoreState) => state.cart,
    }),
    ...mapState(SECONDARY_STORE_CONST.SECONDARY_CONST_MODULE_NAME, {
      secondaryStoreState: (state: ISecondaryStoreState) => state,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,

      isAppAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_APP_ADMIN,

      hasChequesAccess:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_HAS_CHEQUES_ACCESS,
      hasCredit:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_HAS_CREDIT,
    }),
  },
})
export default class LaunchHeader extends Vue {
  public readonly isLoggedIn: boolean;
  public readonly configApp: IConfigApp;
  public readonly userApplication: IUserApplication;
  public readonly cartItems: IAthleteCompSchedRuleEvent[];
  public readonly isAdmin: boolean;
  public readonly isAppAdmin: boolean;
  public readonly hasChequesAccess: boolean;
  public readonly secondaryStoreState: ISecondaryStoreState;
  public readonly cart: IServerResponse<
    IAthleteCompSched[],
    { wcCart: ICartWooCommerce }
  >;

  public burgerInstance: any = null;

  public dropDownCompCreate: any = null;
  public dropDownAdmin: any = null;
  public dropDownHelp: any = null;
  public dropDownMyAccount: any = null;

  public isMiniCartDisplayed = false;
  public showLogoutConfirm = false;

  public launchRoutesPaths = LAUNCH_ROUTES_PATHS;
  public PREFIX = Math.random().toString(36).substring(2);
  // public language: string = "en";
  public selectedLanguage = {
    id: "en",
    label: "EN",
  };
  // public languageOptions = LANGUAGE_OPTIONS;
  public languageOptions = [
    {
      id: "en",
      label: "EN",
    },
    {
      id: "fr",
      label: "FR",
    },
  ];

  public isLoading: boolean = false;
  public $mq: any;
  public VUE_MQ_SIZES = VUE_MQ_SIZES;
  public CONFIG = CONFIG;
  public configService: ConfigService = new ConfigService();
  public emailMessageService: EmailMessageService = new EmailMessageService();
  public configController = useConfigController();

  public ioSocket: Socket;

  public mounted() {
    this.$store.commit(
      CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_MUTATIONS_SET_UI_VERSION,
      "v1"
    );

    //  Some rendering lag, so defer for a second.
    window.setTimeout(() => {
      this.initBurger();
      this.initAdmin();
      this.initCompCreate();
      this.initMyAccountDropDown();
      this.initHelpDropDown();
    }, 1000);

    // this.ioSocket = io("ws://109.228.60.74:3000");
    // this.ioSocket = getSocketInstance();
  }

  public testSocketSend() {
    //  this.ioSocket.emit("test", "hi from Nick at " + new Date().toISOString());
  }

  @Watch("isLoggedIn")
  public isLoggedInChanged(newValue: boolean, oldValue: boolean) {
    if (newValue !== oldValue && newValue === true) {
      this.$store.dispatch(
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME +
          "/" +
          ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_GET_CART
      );
    }
  }

  public doLogout() {
    this.showLogoutConfirm = false;
    this.$store.dispatch(
      AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME +
        "/" +
        AUTH_STORE_CONST.AUTH_ACTIONS_LOGOUT,
      { redirectToHome: true, logoutUrl: this.configApp.logout }
    );
  }

  public get getSupportLink() {
    return "https://support.entry4sports.com/";
  }

  public changeLanguage() {
    this.$store.dispatch(
      CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_ACTIONS_SWITCH_IL8N_LANGUAGE,
      this.selectedLanguage
    );
  }

  public unImpersonate() {
    this.isLoading = true;
    this.$store
      .dispatch(
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
          "/" +
          CONFIG_STORE_CONST.CONFIG_ACTIONS_IMPERSONATE,
        { id: 0 }
      )
      .then(() => {
        this.isLoading = false;
      });
  }

  public get hasAdminMenuAccess() {
    // Role: admin, Org: National
    // is in IRL, but not in the UK
    // is in the UK, but not in IRL

    // const hasNAtionalOrgAccess = this.configService.hasNationalOrgAccess(
    //   this.configApp.userInfo
    // );

    // ||
    //   (this.configController.isIRL.value &&
    //     this.configController.hasAdminForNational.value)

    return this.isAdmin || this.isAppAdmin;
  }

  public get hasBuilderPermissionForAnyOrg() {
    return this.configService.hasBuilderPermissionForAnyOrg(
      this.configApp.userInfo
    );
  }

  public initBurger() {
    const defaultOptions = {};
    this.burgerInstance = document.getElementById("nav-mobile");
    if (this.burgerInstance) {
      (window as any).M.Sidenav.init(this.burgerInstance, defaultOptions);
    }
  }

  public initAdmin() {
    this.dropDownAdmin = document.getElementById("dropdown-trigger-admin");
    //  @ts-ignore
    if (this.dropDownAdmin) {
      (window as any).M.Dropdown.init(this.dropDownAdmin, {
        constrainWidth: false,
        hover: false,
      });
    }
  }

  public initCompCreate() {
    this.dropDownCompCreate = document.getElementById(
      "dropdown-trigger--comp-create"
    );
    //  @ts-ignore
    if (this.dropDownCompCreate) {
      (window as any).M.Dropdown.init(this.dropDownCompCreate, {
        constrainWidth: false,
        hover: false,
      });
    }
  }

  public initMyAccountDropDown() {
    this.dropDownMyAccount = document.getElementById(
      "dropdown-trigger-my-account"
    );
    //  @ts-ignore
    if (this.dropDownMyAccount) {
      (window as any).M.Dropdown.init(this.dropDownMyAccount, {
        constrainWidth: false,
        hover: false,
      });
    }
  }

  public initHelpDropDown() {
    this.dropDownHelp = document.getElementById("dropdown-trigger-help");
    if (this.dropDownHelp) {
      (window as any).M.Dropdown.init(this.dropDownHelp, {
        constrainWidth: false,
        hover: false,
      });
    }
  }

  public destroyWidgets() {
    if (this.burgerInstance && this.burgerInstance.destroy) {
      this.burgerInstance.destroy();
    }

    if (this.dropDownAdmin && this.dropDownAdmin.destroy) {
      this.dropDownAdmin.destroy();
    }

    if (this.dropDownHelp && this.dropDownHelp.destroy) {
      this.dropDownHelp.destroy();
    }

    if (this.dropDownMyAccount && this.dropDownMyAccount.destroy) {
      this.dropDownMyAccount.destroy();
    }
  }

  public get getCartItemCount(): number {
    if (this.cart.meta && this.cart.meta.wcCart) {
      return this.cart.meta.wcCart.items.length;
    }
    return 0;
  }

  public get getMessageUnreadCount(): number {
    return this.emailMessageService.getUnreadMessages(this.configApp.messages)
      .length;
  }

  public openMessages() {}

  public miniCartShow(showIt: boolean) {
    console.log("LaunchHeader.miniCartShow() showIt: " + showIt);
    this.isMiniCartDisplayed = showIt;
  }

  public confirmCart() {
    // this.$store.dispatch(
    //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
    //     ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SEND_TO_BASKET
    // );
    window.location.href = CONFIG.WP_BASKET;
  }

  public get getShowHealth() {
    if (!this.isAdmin) {
      return false;
    }
    if (this.configApp.env !== "prod") {
      return false;
    }
    return this.configService.isThereHealthIssue(this.configApp);
  }

  public destroy() {
    console.log("LaunchHeader.destroy():");
    this.destroyWidgets();
  }
}
</script>

<style>
.launch-header--icon-counter {
  position: relative;
  left: -30px;
  top: 10px;
}
</style>
