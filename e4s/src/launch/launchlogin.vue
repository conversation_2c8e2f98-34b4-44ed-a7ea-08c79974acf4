<template>
  <div>
    <login></login>
  </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import {mapState} from "vuex";
import {AUTH_STORE_CONST, IAuthStoreState} from "../auth/auth-store";
import Login from "../auth/login.vue";

@Component({
    name: "launchlogin",
    components: {
        login: Login
    },
    computed: {
     ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, {
            isLoggedIn: (state: IAuthStoreState) => state.isLoggedIn,
            tokenReponse: (state: IAuthStoreState) => state.tokenReponse,
            tokenResponseFailure: (state: IAuthStoreState) => state.tokenResponseFailure
        })
    }
})
export default class LaunchLogin extends Vue {
}
</script>