<template>
  <div class="e4s-flex-column e4s-sticky-navigation" id="e4s-nav-bar">
    <div
      class="e4s-navigation-bar"
      :class="[
        'e4s-navigation-bar--' +
          configController.getCssEnvSuffix.value +
          'primary',
        'e4s-navigation-bar-top--' + configController.getCssEnvIdentifier.value,
      ]"
    >
      <div class="e4s-navigation-bar--content-wrapper" :class="getContentWidth">
        <!-- Logo -->
        <div class="e4s-navigation-bar-logo--container">
          <a href="#" v-on:click.prevent="goHome">
            <E4sLogoSvg height="45px" />
          </a>
          <ConfigVersionSwitchV1
            current-ui-version="v2"
            v-if="configVersionSwitch.canUserViewSwitcher.value"
          />
        </div>

        <!-- /Logo -->

        <!-- NAV LINKS -->
        <ul class="e4s-navigation-bar-menu" :class="getMenuMobileClass">
          <!--          <E4sLogoSvg/>-->

          <LaunchLinkV2
            :link-parent-v2="launchHeaderControllerV2.homeLink.value"
            :show-children-link-parent-v2="
              launchHeaderControllerV2.state.showChildrenLink
            "
          />

          <!--        <ul class="e4s-navigation-bar-menu" id="qwertyuiop">-->
          <LaunchLinkV2
            v-if="authStoreController.isLoggedIn.value"
            :link-parent-v2="
              launchHeaderControllerV2.getLinksById.value.MESSAGES_V2
            "
            :show-children-link-parent-v2="
              launchHeaderControllerV2.state.showChildrenLink
            "
          />
          <LaunchLinkV2
            v-if="authStoreController.isLoggedIn.value"
            :link-parent-v2="
              launchHeaderControllerV2.getLinksById.value.PARENT__MY_ACCOUNT
            "
            :show-children-link-parent-v2="
              launchHeaderControllerV2.state.showChildrenLink
            "
            v-on:showChildren="launchHeaderControllerV2.setShowChildrenLink"
            v-on:hideChildren="launchHeaderControllerV2.hideChildrenLink()"
          >
            <!--            -->
            <template slot="child-links">
              <LaunchSublinkSimpleV2
                :link-display-name="
                  launchHeaderControllerV2.configController.getStore.value
                    .configApp.userInfo.user.display_name
                "
                :is-link="false"
              />
              <LaunchSublinkV2
                :link-v2="launchHeaderControllerV2.state.subLinksV1.BUILDER"
              />
              <LaunchSublinkV2
                :link-v2="
                  launchHeaderControllerV2.getSubLinksById.value.ATHLETES_V2
                "
              />
              <LaunchSublinkV2
                v-if="configController.isAdmin.value"
                :link-v2="
                  launchHeaderControllerV2.getSubLinksById.value.USER_SEARCH_V2
                "
              />
              <LaunchSublinkV2
                v-if="configController.isAdmin.value"
                :link-v2="
                  launchHeaderControllerV2.getSubLinksById.value.CLUB_SEARCH_V2
                "
              />
              <LaunchSublinkV2
                :link-v2="
                  launchHeaderControllerV2.getSubLinksById.value.MY_ACCOUNT
                "
              />
              <LaunchSublinkSimpleV2
                link-display-name="Logout"
                v-on:linkClicked="launchHeaderControllerV2.doLogout()"
              />
            </template>
          </LaunchLinkV2>

          <!--          <LaunchLinkV2-->
          <!--            v-if="authStoreController.isLoggedIn.value"-->
          <!--            v-for="link in launchHeaderControllerV2.state.links"-->
          <!--            :link-parent-v2="link"-->
          <!--            :show-children-link-parent-v2="-->
          <!--              launchHeaderControllerV2.state.showChildrenLink-->
          <!--            "-->
          <!--            :key="link.linkId"-->
          <!--            v-on:showChildren="launchHeaderControllerV2.setShowChildrenLink"-->
          <!--          />-->
          <LaunchLinkV2
            id="okmijn"
            :link-parent-v2="{
              linkId: 'CART',
              linkDisplayName: 'Cart',
              path: CONFIG.WP_BASKET,
              showBadge: true,
              badgeText: launchHeaderControllerV2.cartItemCount.value,
              children: [],
            }"
            :show-children-link-parent-v2="
              launchHeaderControllerV2.state.showChildrenLink
            "
          />

          <!--          <LaunchLinkV2-->
          <!--            v-if="!authStoreController.isLoggedIn.value"-->
          <!--            :link-parent-v2="{-->
          <!--              linkId: 'LOGIN',-->
          <!--              linkDisplayName: 'Login',-->
          <!--              path: getLoginPath.value,-->
          <!--              showBadge: false,-->
          <!--              badgeText: '',-->
          <!--              children: [],-->
          <!--            }"-->
          <!--            :show-children-link-parent-v2="-->
          <!--              launchHeaderControllerV2.state.showChildrenLink-->
          <!--            "-->
          <!--          />-->
          <LaunchLinkV2
            v-if="!authStoreController.isLoggedIn.value"
            :link-parent-v2="launchHeaderControllerV2.loginLink.value"
            :show-children-link-parent-v2="
              launchHeaderControllerV2.state.showChildrenLink
            "
          />
        </ul>
        <!-- /NAV LINKS -->

        <!-- NAV HAMBURGER -->
        <div
          class="
            e4s-flex-row e4s-flex-center
            e4s-navigation-bar--mobile-wrapper
          "
        >
          <div
            class="e4s-navigation-bar-menu--item"
            v-if="authStoreController.isLoggedIn.value"
            id="qazwsx"
          >
            <a :href="CONFIG.WP_BASKET" class="e4s-subheader--200">
              <span class="e4s-subheader--200">Cart</span>
              <div class="e4s-badge e4s-badge--navigation">
                <span
                  class="e4s-subheader--300"
                  v-text="launchHeaderControllerV2.cartItemCount.value"
                ></span>
              </div>
            </a>
          </div>
          <div class="e4s-navigation-bar__hamburger-container">
            <button
              class="
                hamburger hamburger--slider
                e4s-navigation__hamburger
                e4s-hamburger__menu-closed
              "
              type="button"
              v-on:click="launchHeaderControllerV2.toggleNav()"
            >
              <span class="hamburger-box">
                <span class="hamburger-inner"></span>
              </span>
            </button>
          </div>
        </div>
        <!-- /NAV HAMBURGER -->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { useStore } from "../../app.store";
import { CONFIG_STORE_CONST } from "../../config/config-store";
import { IConfigApp, USER_ROLES } from "../../config/config-app-models";
import { EmailMessageService } from "../../email-message/email-message-service";
import {
  ATH_COMP_SCHED_STORE_CONST,
  IAthCompSchedStoreState,
} from "../../athleteCompSched/store/athleteCompSched-store";
import { CONFIG } from "../../common/config";
import LaunchLinkV2 from "./launchlink/launch-link-v2.vue";
import { useLaunchHeaderV2 } from "./useLaunchHeaderV2";
import {
  useConfigController,
  useConfigStore,
} from "../../config/useConfigStore";
import InfoMinorSvg from "../../common/ui/svg/InfoMinorSvg.vue";
import CartMajorSvg from "../../common/ui/svg/CartMajorSvg.vue";
import { useAuthStoreController } from "../../auth/useAuthStore";
import E4sLogoSvg from "../../common/ui/svg/E4sLogoSvg.vue";
import { useRoute, useRouter } from "../../router/migrateRouterVue3";
import { RawLocation } from "vue-router";
import {
  LAUNCH_ROUTES_PATHS_V2,
  LaunchRouteValueV2,
  widerRoutesClass,
} from "./launch-routes-v2";
import { router } from "../../index";
import LaunchSublinkV2 from "./launchlink/launch-sublink-v2.vue";
import LaunchSublinkSimpleV2 from "./launchlink/launch-sublink-simple.v2.vue";
import { ENTRY_STORE_CONST } from "../../entry/entry-store";
import ConfigVersionSwitch from "../../config/ConfigVersionSwitch.vue";
import ConfigVersionSwitchV1 from "../../config/ConfigVersionSwitchV1.vue";
import { useConfigVersionSwitch } from "../../config/useConfigVersionSwitch";

export default defineComponent({
  name: "launch-header-v2",
  components: {
    ConfigVersionSwitchV1,
    ConfigVersionSwitch,
    LaunchSublinkSimpleV2,
    LaunchSublinkV2,
    E4sLogoSvg,
    CartMajorSvg,
    InfoMinorSvg,
    LaunchLinkV2,
  },
  props: {
    entries: {
      type: Array as PropType<unknown[]>,
      default: () => {
        return [];
      },
    },
  },
  setup(props: { entries: unknown[] }, context: SetupContext) {
    const store = useStore();
    const configStore = useConfigStore();
    const configController = useConfigController();
    const authStoreController = useAuthStoreController();
    const launchHeaderControllerV2 = useLaunchHeaderV2();
    const routerInternal = useRouter();
    const route = useRoute();
    // const e4sRouteController = useE4sRouteController();

    const configVersionSwitch = useConfigVersionSwitch(
      "v2",
      configStore.configApp.userInfo.user.version
    );

    router.beforeEach((to: any, from: any, next: any) => {
      console.log("launch-header-v2 setup() router.beforeEach()...");
      launchHeaderControllerV2.hideMobileNav();
      next();
    });

    const configApp = computed<IConfigApp>(() => {
      return store.state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME].configApp;
    });

    const athCompSchedStoreState = computed<IAthCompSchedStoreState>(() => {
      return store.state[
        ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME
      ];
    });

    store.commit(
      CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_MUTATIONS_SET_UI_VERSION,
      "v2"
    );

    watch(
      () => configStore.configApp.role,
      (newValue: string) => {
        //  User is initially ANON, but if logs in etc., recalc
        launchHeaderControllerV2.createUserLinks();
      },
      {
        immediate: true,
      }
    );

    //  launchHeaderControllerV2.reloadCart();

    watch(
      () => authStoreController.isLoggedIn.value,
      (newValue: boolean, oldValue: boolean) => {
        //  User is initially ANON, but if logs in etc., recalc
        // if (newValue !== oldValue) {
        //   launchHeaderControllerV2.reloadCart();
        // }
      }
    );

    // const configApp: IConfigApp = store.state[CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME].configApp;
    // const athCompSchedStoreState: IAthCompSchedStoreState  = store.state[ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME];

    const emailMessageService: EmailMessageService = new EmailMessageService();

    const getLogo = computed(() => {
      return CONFIG.E4S_HOST + "/" + configApp.value.logo;
    });

    const getMessageUnreadCount = computed(() => {
      return emailMessageService.getUnreadMessages(configApp.value.messages)
        .length;
      // return configApp.value.messages.length;
    });

    const getCartItemCount = computed(() => {
      const cart = athCompSchedStoreState.value.cart;
      if (cart.meta && cart.meta.wcCart) {
        return cart.meta.wcCart.items.length;
      }
      return 0;
    });

    // function toggleNav(target: Event) {
    //   const tar: HTMLElement = target.currentTarget as HTMLElement;
    //
    //   LaunchHeaderV2js.toggleNav(tar);
    // }

    function toggleAdmin() {
      const userRole =
        configStore.configApp.role === "E4SUSER"
          ? USER_ROLES.USER
          : USER_ROLES.E4SUSER;
      store.commit(
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
          "/" +
          CONFIG_STORE_CONST.CONFIG_MUTATIONS_SET_ROLE,
        userRole
      );
    }

    // function showMobileNavigation() {
    //   launchHeaderControllerV2.toggleNav();
    // }

    function goHome() {
      routerInternal
        .push({
          name: LAUNCH_ROUTES_PATHS_V2.HOME_V2,
        } as RawLocation)
        .catch((error: any) => {
          console.log("launch-header goHome error");
          store.commit(
            ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
              "/" +
              ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_ROUTE_ERROR,
            error
          );
        });
    }

    const getContentWidth = computed(() => {
      const routeName: LaunchRouteValueV2 = route.name
        ? route.name
        : ("" as any as LaunchRouteValueV2);
      if (widerRoutesClass[routeName]) {
        return widerRoutesClass[routeName];
      }
      return "e4s-width-controller";
    });

    const getMenuMobileClass = computed(() => {
      const cssClasses: string[] = [];
      if (!launchHeaderControllerV2.state.showMobile) {
        return cssClasses;
      }

      cssClasses.push("e4s-navigation-bar-menu--mobile");
      const getCssEnvSuffix: string = configController.getCssEnvSuffix.value;
      if (getCssEnvSuffix.length > 0) {
        cssClasses.push(
          "e4s-navigation-bar-menu--" + getCssEnvSuffix + "mobile"
        );
      }

      return cssClasses;
    });

    return {
      store,
      configApp,
      getLogo,
      getMessageUnreadCount,
      getCartItemCount,
      toggleAdmin,
      launchHeaderControllerV2,
      configController,
      authStoreController,
      goHome,
      getContentWidth,
      CONFIG,
      configVersionSwitch,
      getMenuMobileClass,
    };
  },
});
</script>
