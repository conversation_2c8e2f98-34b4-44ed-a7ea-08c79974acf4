import { computed } from "@vue/composition-api";
import { useRouter } from "../../router/migrateRouterVue3";

export function useUserRouteV1V2Helper() {
  const routerInternal = useRouter();

  // are we in v1 or v2?
  // function getIsV1() {
  //   return routerInternal.currentRoute.path.startsWith("/#/");
  // }
  //
  // function getIsV2() {
  //   return routerInternal.currentRoute.path.startsWith("/v2/");
  // }

  // computed prop isV2Route
  const isV2Route = computed(() => {
    return routerInternal.currentRoute.path.startsWith("/v2/");
  });

  return { routerInternal, isV2Route };
}
