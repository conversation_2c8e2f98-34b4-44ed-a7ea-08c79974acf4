<template>
  <div class="e4s-flex-column">
    <div
      class="e4s-navigation-bar e4s-navigation-bar--primary"
      :class="'e4s-header-color-bg-' + configStore.configApp.theme"
    >
      <div class="e4s-navigation-bar--content-wrapper">
        <!-- Logo -->
        <div class="e4s-navigation-bar-logo--container">
          <E4sLogoSvg />
        </div>
        <!-- /Logo -->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";

import E4sLogoSvg from "../../common/ui/svg/E4sLogoSvg.vue";
import { useConfigStore } from "../../config/useConfigStore";

export default defineComponent({
  name: "launch-header-blank-v2",
  components: { E4sLogoSvg },
  props: {
    isFullScreen: {
      type: Boolean,
      default: false,
    },
  },
  setup(props: { isFullScreen: boolean }, context: SetupContext) {
    const configStore = useConfigStore();
    return { configStore };
  },
});
</script>

<style scoped></style>
