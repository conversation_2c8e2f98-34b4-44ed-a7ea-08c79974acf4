<template>
    <div>

        <li>
            <router-link to="/showentries" :class="cssClass">
                <span v-text="$t('menu.showEntries')"></span>
            </router-link>
        </li>

    </div>
</template>



<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";

    import {mapState} from "vuex";
    import {AUTH_STORE_CONST, IAuthStoreState} from "../auth/auth-store";
    import {CONFIG_STORE_CONST, IConfigStoreState} from "../config/config-store";
    import {IConfigApp} from "../config/config-app-models";
    import { Prop } from "vue-property-decorator";

    @Component({
        name: "user-nav",
        components: {
        },
        computed: {
            ...mapState(AUTH_STORE_CONST.AUTH_CONST_MODULE_NAME, {
                isLoggedIn: (state: IAuthStoreState) => state.isLoggedIn
            }),
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: IConfigStoreState) => state.configApp,
                userApplication: (state: IConfigStoreState) => state.configApp.userInfo.user
            })
        }
    })
    export default class UserNav extends Vue {
        public isLoggedIn: boolean;
        public configApp: IConfigApp;

        @Prop({default: ""}) public readonly cssClass: string;
    }
</script>
