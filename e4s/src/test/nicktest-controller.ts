import { reactive, UnwrapRef } from "@vue/composition-api";

export interface INickTestControllerState {
  name: string;
  count: number;
  userNames: string[];
  isoTimeStamp: string;
}

export function factoryNickTestController(
  stateInject: INickTestControllerState | UnwrapRef<INickTestControllerState>
) {
  const state: INickTestControllerState | UnwrapRef<INickTestControllerState> =
    stateInject
      ? stateInject
      : {
          name: "",
          count: 0,
          userNames: [],
          isoTimeStamp: "",
        };

  function addTestName() {
    const isoTimeStamp = new Date().toISOString();
    state.userNames.push(isoTimeStamp);
    state.isoTimeStamp = isoTimeStamp;
    state.count++;
  }

  return { state, addTestName };
}

export function useNickTestController() {
  const state = reactive({
    name: "",
    count: 0,
    userNames: [],
    isoTimeStamp: "",
  });
  const controller = factoryNickTestController(state);

  return { state, controller };
}
