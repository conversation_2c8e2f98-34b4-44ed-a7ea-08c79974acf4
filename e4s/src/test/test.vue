<template>
    <div>
        <div><b>This is test component {{message}}</b></div>
    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";

    @Component({
        name: "test",
        components: {
        }
    })
    export default class Test extends Vue {

        public message = "";

        public created() {
            this.message = "Hello at :" + new Date();
        }
    }
</script>
