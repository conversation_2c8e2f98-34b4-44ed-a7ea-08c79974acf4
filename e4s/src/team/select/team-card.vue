<template>
    <div class="e4s-card">
        <div class="row">
            <div class="col s10 m6 l6">
                <a><i class='material-icons red-text' @click="removeTeam">delete_forever</i></a>
                <span class="cart-card-event" v-text="team.teamName"></span>
            </div>

            <div class="col m4 l4 hide-on-med-and-down">
                <span v-text="team.areaName"></span>
            </div>

            <div class="col s2 m2 l2">
                <span class="right" v-text="getGender"></span>
            </div>
        </div>

        <div class="row hide-on-large-only">
            <div class="col s12">
                <span v-text="team.areaName"></span>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop} from "vue-property-decorator";
    import {GENDER} from "../../common/common-models";
    import {IAreaTeamSummary, ITeam} from "../team-models";

    @Component({
        name: "cart-card"
    })
    export default class TeamCard extends Vue {
        @Prop() public team: IAreaTeamSummary;

        public removeTeam() {
            this.$emit("removeTeam", R.clone(this.team));
        }

        public get getGender() {
            const team: ITeam = this.team;
            if (team.gender === GENDER.MALE) {
                return "Male";
            }
            if (team.gender === GENDER.FEMALE) {
                return "Female";
            }
            return "";
        }

    }
</script>