export type SECONDARY_OBJECT_TYPES = "SYSTEM" | "AO" | "ORG" | "COMP" | "EVENT" | "GROUP" | "CE";

export interface ISecondaryRefObj {
    id: number;
    objId: number;
    objType: SECONDARY_OBJECT_TYPES;
    compId: number;                 //  If "COMP" | "EVENT" | "EVENT_GROUP" | "CE";

    objName: string;                //  slightly redundant, but need for "displaying"
    objKey?: string;                //  Back end calc this.
}

export interface ISecondaryPrice {
    price: number;
    salePrice: number;
    saleEndDate: string;
}

export interface ISecondaryAttributeBase {
    name: string;
}

export interface ISecondaryAttributes extends ISecondaryAttributeBase {
    values: string;                     //  E.g. M | L | XL
}

export interface ISecondaryAttributeValue extends ISecondaryAttributeBase {
    value: string;                     //  E.g. L
}

export interface ITicket {
    ticketText: string;                //   HTML Use this one, any further info.
    dataReq: boolean;
}

export interface ISecondaryProductBase {
    id: number;
    parentId: number;
    name: string;
    description: string;
    image: string;
    stockQty: number;
    soldQty: number;
    price: ISecondaryPrice;
    download: boolean;                  //  true = it IS a ticket.
    ticket: ITicket;
}

export interface ISecondaryProduct extends ISecondaryProductBase {
    attributes: ISecondaryAttributes[];
    variations: ISecondaryVariant[]            //   N.B. READ ONLY, "CRUD" CUD goes through it's own end point.
}

export interface ISecondaryVariant extends ISecondaryProductBase {
    menuOrder: number;
    name: string;                               //  Ignore this in child variation, it will come up.
    attributeValues: ISecondaryAttributeValue[];   // [{name: "size", value: "L"}, {name: "colour", value: "Red"}]
}

export interface ISecondaryDef {
    id: number;
    prod: ISecondaryProduct;
    maxAllowed: number;                       //  How many can they buy 0 = no max.
    refObj: ISecondaryRefObj;
    perAcc: "A" | "U" | "";                    //   If "A" implied that athlete has entered.
}

export interface IWooCommerceLineItem {
    lineValue: number;
    productId: number;
    variationId: number;
    qty: number;
    title: string;
    variation: Record<string, string>;          //  string | "_athlete"
}

export interface ICartWooCommerce {
    currency: string;
    items: IWooCommerceLineItem[];
    url: string;
}
