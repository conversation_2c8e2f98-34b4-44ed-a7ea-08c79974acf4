<template>
    <div style="width: 100%">
        <div class="row">
            <div class="col s12 m12 l12">
                <div class="right">
                    <CloseIcon v-on:close="cancel" class="right"></CloseIcon>
                </div>
            </div>
        </div>

        <div class="stadium-img-style">
            <img :src="url">
        </div>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import CloseIcon from "../../common/ui/close-icon.vue";

@Component({
    name: "stadium-seating",
    components: {
        CloseIcon
    }
})
export default class StadiumSeating extends Vue {
    @Prop({default: ""})
    public readonly url: string;

    public cancel() {
        this.$emit("cancel");
    }
}
</script>

<style scoped>
.stadium-img-style {
    padding: 1rem;
    overflow: auto;
    height: 80vh;
    width: 96vw;
}
</style>
