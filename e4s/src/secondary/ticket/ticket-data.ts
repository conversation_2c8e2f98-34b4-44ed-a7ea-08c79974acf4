import https from "../../common/https"
import {
    ITicketForm,
    ITicketFormBase,
    ITicketFormBaseMeta,
    ITicketFormDataPayload,
    ITicketFormTC,
    ITicketGuidRequest,
    ITicketReport
} from "./ticket-models"
import {IServerGenericResponse, IServerResponse} from "../../common/common-models"

export interface ITicketSearchPayload {
    firstname: string;
    surname: string;
    urn: string;
    club: string;
}

export class TicketData {

    /**
     *  For punter
     * @param key   E.g. 1-32677d0876aee1515f8fec07bde00614
     */
    public readTicketData(compId: number, key: string): Promise<IServerResponse<ITicketForm, ITicketFormBaseMeta>> {
        return https.get("/v5/ticket/read/" + compId + "/" + key) as any as Promise<IServerResponse<ITicketForm, ITicketFormBaseMeta>>;
    }

    /**
     *  For GateKeeper
     * @param key   E.g. 1-32677d0876aee1515f8fec07bde00614
     */
    public getTicketData(compId: number, key: string): Promise<IServerResponse<ITicketFormTC, ITicketFormBaseMeta>> {
        return https.get("/v5/ticket/scan/" + compId + "/" + key) as any as Promise<IServerResponse<ITicketFormTC, ITicketFormBaseMeta>>;
    }

    public getTicketForAthlete(compId: number, athleteId: number): Promise<IServerResponse<ITicketForm>> {
        return https.get("/v5/ticket/get/" + compId + "/" + athleteId) as any as Promise<IServerResponse<ITicketForm>>;
    }

    public getTicketsForOrder(hash: string): Promise<IServerResponse<ITicketFormBase[], ITicketFormBaseMeta>> {
        return https.get("/v5/ticket/order/" + hash) as any as Promise<IServerResponse<ITicketFormBase[], ITicketFormBaseMeta>>;
    }

    public getTicketsByEmailSearch(compId: number, searchTerm: string): Promise<IServerResponse<ITicketFormBase[], ITicketFormBaseMeta>> {
        return https.get("/v5/ticket/list/" + compId + "?email=" + searchTerm) as any as Promise<IServerResponse<ITicketFormBase[], ITicketFormBaseMeta>>;
    }

    public getTicketsByAthleteSearch(compId: number, ticketSearchPayload: ITicketSearchPayload): Promise<IServerResponse<ITicketFormBase[], ITicketFormBaseMeta>> {
        return https.get("/v5/ticket/athlete/list/" + compId, {params: ticketSearchPayload}) as any as Promise<IServerResponse<ITicketFormBase[], ITicketFormBaseMeta>>;
    }

    public getTicketsSearch(compId: number, searchTerm: string): Promise<IServerResponse<ITicketFormBase[], ITicketFormBaseMeta>> {
        return https.get("/v5/ticket/listall/" + compId + "?search=" + searchTerm) as any as Promise<IServerResponse<ITicketFormBase[], ITicketFormBaseMeta>>;
    }

    /**
     * https://dev.entry4sports.com/wp-json/e4s/v5/ticket/update/1-32677d0876aee1515f8fec07bde00614
     * @param key       E.g. 1-32677d0876aee1515f8fec07bde00614
     * @param ticketFormDataPayload
     */
    public submitFormData(key: string, ticketFormDataPayload: ITicketFormDataPayload): Promise<IServerResponse<ITicketFormDataPayload>> {
        return https.post("/v5/ticket/update/" + key, ticketFormDataPayload);
    }

    /**
     * Basically, this can be used on an "order", e.g. an athlete entry, and get returned the "guid"
     * e.g. 25029-bef6d09c27d5d0c27aae77c2273eec4b, so you can open up their QR form.
     *
     * @param itemId
     */
    public getGuidForItemId(itemId: number): Promise<IServerResponse<ITicketGuidRequest>> {
        return https.get("/v5/ticket/athlete/" + itemId) as Promise<IServerResponse<ITicketGuidRequest>>;
    }

    public setAllTicketsOffSite(compId: number): Promise<IServerGenericResponse>{
        return https.get("/v5/ticket/clear/" + compId) as Promise<IServerGenericResponse>;
    }

    public getTicketReport(compId: number): Promise<IServerResponse<ITicketReport>>{
        return https.get("/v5/ticket/report/" + compId) as Promise<IServerResponse<ITicketReport>>;
    }

}
