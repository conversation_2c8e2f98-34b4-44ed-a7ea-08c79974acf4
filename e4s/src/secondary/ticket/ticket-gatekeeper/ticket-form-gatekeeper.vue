<template>
    <div>

        <FormHeader title="Ticket Gatekeeper">
            <CompetitionGoTo slot="e4s-form-header--right" :comp-id="compId" class="right"></CompetitionGoTo>
        </FormHeader>

        <!--SEARCH_BUTTONS-->
        <div  v-show="showSection === sections.SEARCH_BUTTONS">
            <div class="row">
                <div class="col s12 m12 l12">
                    <button class="e4s-button e4s-button--green e4s-button--fat e4s-button--medium"
                            :disabled="getIsLoading"
                            v-on:click.stop="startScan">
                        <span>Scan</span>
                    </button>
                    <button class="e4s-button e4s-button--green e4s-button--fat e4s-button--medium"
                            :disabled="getIsLoading"
                            v-on:click.stop="doSearch(true)">
                        <span>Search</span>
                    </button>
<!--                    <div class="e4s-force-inline-block">-->
<!--                        <p>-->
<!--                            <label>-->
<!--                                <input id="auto-scan-next"-->
<!--                                       class="e4s-checkbox"-->
<!--                                       type="checkbox"-->
<!--                                       v-model="autoScanNext" />-->
<!--                                <span class="e4s-bold">-->
<!--                                    Auto Scan-->
<!--                                    <FieldHelp-->
<!--                                        title="Automatically open camera for next scan"-->
<!--                                        message="After each ticket set to on/off site, will automatically restart camera."-->
<!--                                    ></FieldHelp>-->
<!--                                </span>-->
<!--                            </label>-->
<!--                        </p>-->
<!--                    </div>-->

<!--                    <CompetitionGoTo :comp-id="compId" class="right"></CompetitionGoTo>-->

                </div>
            </div>
        </div>
        <!--/SEARCH_BUTTONS-->

        <!--CAMERA-->
        <QrReader
            v-show="showSection === sections.CAMERA"
            :start-camera="startCamera"
            check-for-pattern-in-result="ticket"
            :stop-camera-processing="getIsTicketFormLoaded"
            v-on:qrResult="processScanResult"
            v-on:onCancel="cancelCamera"
        >
            <template slot="buttons">
                <button class="e4s-button e4s-button--green e4s-button--fat e4s-button--medium"
                        :disabled="getIsLoading"
                        v-on:click.stop="doSearch(true)">
                    <span>Search</span>
                </button>
            </template>
        </QrReader>
        <!--/CAMERA-->

        <!--SEARCH-->
<!--        :style="'display:' + (showSection === sections.SEARCH ? '' : 'none')"-->
        <TicketFormSearch
            v-show="showSection === sections.SEARCH"
            style="padding: 1rem"
            slot="form-content"
            :comp-id="compId"
            :ticket-form-base-trigger-search="ticketFormBaseTriggerSearch"
            v-on:cancel="doSearch(false)"
            v-on:scan="startScan"
            v-on:selectedCheckInTicket="selectedCheckInTicket"
        />
        <!--/SEARCH-->

        <!--FORM-->
        <div v-show="showSection === sections.FORM">

            <div class="e4s-section-padding-separator"></div>

            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="ticket--section-header">
                        <span v-text="getTicketHeader"></span>
                        <span :class="ticketFormInternal.ticket.onSite ? 'ticket--section-header-on-site' : 'ticket--section-header-off-site'"
                              v-text="ticketFormInternal.ticket.onSite ? 'On Site' : 'Off Site'"
                        ></span>

<!--                        <div v-show="getIsLoading" class="right">Loading...<LoadingSpinner></LoadingSpinner></div>-->
                        <button class="e4s-button e4s-button--red e4s-button--medium right"
                                v-on:click.stop="cancelEverything">
                            <span>Close</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">

                <div class="input-field col s6 m6 l6" v-if="ticketFormInternal.athlete.id > 0">
                    <label class="active" :for="PREFIX + 'athlete'">
                        Athlete
                    </label>
                    <div :id="PREFIX + 'athlete'">
                        <span v-text="ticketFormInternal.athlete.id"></span>:&nbsp;
                        <span v-text="ticketFormInternal.athlete.name"></span>:&nbsp;
                        <span v-text="ticketFormInternal.athlete.club"></span>
                        <div v-text="getEntriesDescription"></div>
                    </div>
                </div>

                <div class="input-field col s6 m6 l6" v-if="ticketFormInternal.athlete.id === 0">
                    <label class="active" :for="PREFIX + 'user'">
                        Ticket (order ID)
                    </label>
                    <div :id="PREFIX + 'user'">
                        <span v-text="ticketFormInternal.product.name"></span>
                        (<span v-text="ticketFormInternal.orderId"></span>)
                        <span v-text="getAthleteNameFromVariations"></span>
                    </div>
                </div>

                <div class="input-field col s6 m6 l6">
                    <label class="active" :for="PREFIX + 'scan-count'">
                        Count
                    </label>
                    <div :id="PREFIX + 'scan-count'">
                        Scanned: <span v-text="ticketFormInternal.ticket.scannedCount"></span>
                        |
                        Search: <span v-text="ticketFormInternal.ticket.searchCount"></span>
                    </div>
                </div>

                <div class="input-field col s6 m6 l6">
                    <label class="active" :for="PREFIX + 'comp'">
                        Competition
                    </label>
                    <div :id="PREFIX + 'comp'">
                        <div v-if="ticketFormInternal.competition" >
                            <span v-text="ticketFormInternal.competition.id"></span>: <span v-text="ticketFormInternal.competition.name"></span>
                        </div>
                        <span v-if="!ticketFormInternal.competition">No competition data.</span>
                    </div>
                </div>

                <div class="input-field col s6 m6 l6">
                    <label class="active" :for="PREFIX + 'user'">
                        User
                    </label>
                    <div :id="PREFIX + 'user'">
                        <span v-text="ticketFormInternal.user.email"></span>
                    </div>
                </div>

                <div class="input-field col s6 m6 l6" v-if="getHasSeat">
                    <label class="active" :for="PREFIX + 'seat'">
                        Seat
                    </label>
                    <div :id="PREFIX + 'seat'">
                        <span v-text="getSeatDescription"></span>
                    </div>
                </div>

            </div>

            <div class="e4s-section-padding-separator"></div>

            <div v-if="getIsDataRequired">

                <div class="row">
                    <div class="col s12 m12 l12">
                        <div class="ticket--section-header">Contact Data</div>
                    </div>
                </div>

                <div class="row">
                    <div class="input-field col s6 m6 l6">
                        <label class="active" :for="PREFIX + 'name'">
                            Name
                            <FieldValidationLabel :validation-controller="validationController" prop-path="data.name"/>
                        </label>
                        <input
                            :id="PREFIX + 'name'"
                            :name="PREFIX + 'name'"
                            :disabled="getIsLoading"
                            type="text"
                            v-model="ticketFormInternal.data.name"
                            placeholder=""/>
                    </div>

                    <div class="input-field col s6 m6 l6">
                        <label class="active" :for="PREFIX + 'tel'">
                            Tel
                            <FieldValidationLabel :validation-controller="validationController" prop-path="data.telNo"/>
                        </label>
                        <input
                            :id="PREFIX + 'tel'"
                            :name="PREFIX + 'tel'"
                            :disabled="getIsLoading"
                            type="text"
                            v-model="ticketFormInternal.data.telNo"
                            placeholder=""/>
                    </div>

                    <div class="input-field col s12 m6 l6">
                        <label class="active" :for="PREFIX + 'address'">
                            Address (optional)
                            <FieldValidationLabel :validation-controller="validationController" prop-path="data.address"/>
                        </label>
                        <input
                            :id="PREFIX + 'address'"
                            :name="PREFIX + 'address'"
                            :disabled="getIsLoading"
                            type="text"
                            v-model="ticketFormInternal.data.address"
                            placeholder=""/>
                    </div>

                    <div class="input-field col s12 m6 l6">
                        <label class="active" :for="PREFIX + 'email'">
                            Email (optional)
                            <FieldValidationLabel :validation-controller="validationController" prop-path="data.email"/>
                        </label>
                        <input
                            :id="PREFIX + 'email'"
                            :name="PREFIX + 'email'"
                            :disabled="getIsLoading"
                            type="text"
                            v-model="ticketFormInternal.data.email"
                            placeholder=""/>
                    </div>
                </div>


            </div>


            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="right">
                        <button class="e4s-button e4s-button--green e4s-button--fat e4s-button--medium"
                                v-show="getShowTicketForm"
                                :disabled="getIsLoading || ticketFormInternal.ticket.onSite"
                                v-on:click.stop="setOnSite(true)">
                            <span>On Site</span>
                        </button>

                        <button class="e4s-button e4s-button--green e4s-button--fat e4s-button--medium"
                                v-show="getShowTicketForm"
                                :disabled="getIsLoading || !ticketFormInternal.ticket.onSite"
                                v-on:click.stop="setOnSite(false)">
                            <span>Off Site</span>
                        </button>

                    </div>
                </div>
            </div>

            <div v-if="ticketFormInternal.text && (ticketFormInternal.text.terms.length > 0)">

                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                    <div class="input-field col s12 m12 l12">
                        <label class="active" :for="PREFIX + 'info'">
                            Terms and Conditions
                        </label>
                        <div :id="PREFIX + 'info'" v-html="ticketFormInternal.text.terms"></div>
                    </div>
                </div>
            </div>

            <div v-if="ticketFormInternal.text && (ticketFormInternal.text.ticketText.length > 0)">

                <div class="e4s-section-padding-separator"></div>

                <div class="row">
                    <div class="input-field col s12 m12 l12">
                        <label class="active" :for="PREFIX + 'info'">
                            Information
                        </label>
                        <div :id="PREFIX + 'info'" v-html="ticketFormInternal.text.ticketText"></div>
                    </div>
                </div>
            </div>

        </div>
        <!--/FORM-->

        <E4sModal
            v-if="showSiteFormDirtyConfirm"
            header-message="Confirm Close"
            body-message="You have not set the ticket on or off site, continue?"
            v-on:closeSecondary="showSiteFormDirtyConfirm = false"
            v-on:closePrimary="confirmCloseDirty()"
        >
        </E4sModal>

    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import FieldValidationLabel from "../../../validation/validation-field-lable.vue";
import { TicketService } from "../ticket-service";
import {ITicketForm, ITicketFormBase, ITicketFormBaseMeta, ITicketFormDataPayload} from "../ticket-models"
import { ValidationController } from "../../../validation/validation-controller";
import QrReader from "../../../qr-code/qr-reader/qr-reader.vue"
import { VUE_MQ_SIZES } from "../../..";
import TicketFormSearch from "./ticket-form-search.vue"
import {ITrigger} from "../../../common/common-models"
import {CHECKIN_STORE_CONST} from "../../../competition/checkin/checkin-store"
import {handleResponseMessages} from "../../../common/handle-http-reponse"
import {TicketData} from "../ticket-data"
import {SecondaryService} from "../../secondary-service"
import FieldHelp from "../../../common/ui/field/field-help/field-help.vue"
import E4sModal from "../../../common/ui/e4s-modal.vue"
import CompetitionGoTo from "../../../competition/ui/competition-go-to.vue"
import FormHeader from "../../../common/ui/form/header/form-header.vue"

const ticketService: TicketService = new TicketService();

@Component({
    name: "ticket-form-gatekeeper",
    components: {
        FormHeader, CompetitionGoTo, E4sModal, FieldHelp, TicketFormSearch, QrReader, FieldValidationLabel
    }
})
export default class TicketFormGatekeeper extends Vue {
    @Prop({default: 0})
    public readonly compId: number;

    @Prop({
        default: () => {
            return ticketService.factoryTicketForm();
        }
    })
    public readonly ticketForm: ITicketForm;

    @Prop({default: false})
    public readonly isLoading: boolean;

    @Prop({default: ""})
    public readonly qrHtml: string;

    public isLoadingInternal: boolean = false;

    public PREFIX = Math.random().toString(36).substring(2);
    public ticketData = new TicketData();
    public ticketService = ticketService;
    public secondaryService: SecondaryService = new SecondaryService();
    public ticketFormInternal: ITicketForm = this.ticketService.factoryTicketForm();
    public ticketFormBaseMeta: ITicketFormBaseMeta = this.ticketService.factoryTicketFormBaseMeta();
    public validationController: ValidationController = new ValidationController();

    // public requiredFieldsDisabled = false;

    public startCamera = false;
    // public showCamera = false;
    public autoScanNext = false;

    public sections = {
        SEARCH_BUTTONS: "SEARCH_BUTTONS",
        CAMERA: "CAMERA",
        SEARCH: "SEARCH",
        FORM: "FORM"
    };
    public showSection: string = this.sections.SEARCH;
    public lastSearchType = "";

    // public showSearch = false;
    public isScanning = false;
    public showAdmin = false;
    // public showAllOnSiteConfirm = false;
    public isSiteFormDirty = false;
    public showSiteFormDirtyConfirm = false;

    public qrTriggerStop: ITrigger<string> = {
        timeIso: ""
    }

    public qrTriggerOpenCamera: ITrigger<string> = {
        timeIso: ""
    }

    public searchTermTrigger = "";
    public ticketFormBaseTriggerSearch: ITicketFormBase = ticketService.factoryTicketFormBase();

    public $mq: any;
    public VUE_MQ_SIZES = VUE_MQ_SIZES;

    public created() {
        if (this.compId > 0) {
            this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
                CHECKIN_STORE_CONST.CHECKIN_ACTIONS_GET_SUMMARY, this.compId);
        }
        this.loadTicketForm(this.ticketForm);
    }

    public loadTicketForm(ticketForm: ITicketForm) {
        this.isSiteFormDirty = false;
        this.ticketFormInternal = R.clone(ticketForm);
        if (this.getIsDataRequired) {
            this.validateFormData()
        }
        // this.startCamera = false;
        // if (this.ticketFormInternal.id > 0) {
        //     this.showSection = this.sections.FORM;
        // }
    }

    public get getTicketHeader() {
        return this.ticketService.getTicketHeader(this.ticketFormInternal);
    }

    public get getIsDataRequired() {
        //  For now, always show it.
        return true;
        // return this.ticketFormInternal.ticket.dataReq;
    }

    public get getIsTicketFormLoaded() {
        return this.ticketFormInternal.id > 0;
    }

    public get getShowTicketForm() {
        return this.ticketFormInternal.id > 0;
    }

    public get getAthleteNameFromVariations() {
        return this.secondaryService.getAthleteVariationName(this.ticketFormInternal);
    }

    @Watch("ticketForm")
    public onTicketFormChanged(newValue: ITicketForm, oldValue: ITicketForm) {
        this.loadTicketForm(newValue);
        // this.requiredFieldsDisabled = true;
    }

    public selectedCheckInTicket(newValue: ITicketForm) {
        this.loadTicketForm(newValue);
        // this.doSearch(false);
        this.lastSearchType = this.showSection;
        this.showSection = this.sections.FORM;
    }

    public validateFormData() {
        this.validationController.reset();
        this.validationController.setErrors(this.ticketService.validate(this.ticketFormInternal));
    }

    public onScanning(isScanning: boolean) {
        this.isScanning = isScanning;
        if (isScanning) {
            this.ticketFormInternal = this.ticketService.factoryTicketForm();
        }
    }

    public processScanResult(result: string) {
        if (!result || result.length === 0) {
            // this.ticketFormInternal = this.ticketService.factoryTicketForm();
            // this.$emit("reset");
            // this.ticketFormInternal = this.ticketService.factoryTicketForm();
            return;
        }
        //  https://dev.entry4sports.com/#/ticket-form/1-32677d0876aee1515f8fec07bde00614
        if (result.indexOf("#") > -1) {
            const urlSplit: string[] = result.split("/");
            const key = urlSplit[urlSplit.length - 1];
            if (key.length > 0 && !this.isLoadingInternal && this.ticketFormInternal.id === 0) {
                this.loadData(key);
            }
        }
    }

    public loadData(key: string) {
        this.isLoadingInternal = true;
        const prom = this.ticketData.getTicketData(this.compId, key);
        handleResponseMessages(prom);
        prom.then( (response) => {
            if (response.errNo === 0) {
                // const ticketFormBaseMeta = response.meta ? response.meta : this.ticketService.factoryTicketFormBaseMeta();
                // this.ticketFormBaseMeta = ticketFormBaseMeta;
                // const ticket: ITicketForm = {
                //     ...response.data,
                //     competition: ticketFormBaseMeta.competition
                // }
                // this.loadTicketForm(ticket);
                // this.startCamera = false;
                // this.showSection = this.sections.FORM;

                //  we get the single record back, but the order might have more than 1, so
                //  lookup by special order id format, this will display all tickets on the "order".
                this.startCamera = false;
                // this.searchTermTrigger = "Order:" + response.data.orderId;
                this.ticketFormBaseTriggerSearch = response.data;
                this.showSection = this.sections.SEARCH;
            }
        })
            .finally(()=> {
                this.isLoadingInternal = false;
            })
    }

    public get getIsLoading() {
        return this.isLoading || this.isLoadingInternal;
    }

    public get getEntriesDescription(): string {
        return this.ticketService.getEntriesDescription(this.ticketFormInternal);
    }

    public submitData() {
        if (this.getIsDataRequired) {
            this.validateFormData();
            if (!this.validationController.isValid) {
                return;
            }
        }
        this.isLoadingInternal = true;
        const ticketForm = this.ticketFormInternal;
        const ticketFormDataPayload: ITicketFormDataPayload = {
            id: ticketForm.id,
            data: ticketForm.data,
            onSite: ticketForm.ticket.onSite
        };
        const prom = this.ticketData.submitFormData(ticketForm.guid, ticketFormDataPayload);
        handleResponseMessages(prom);
        prom
            .then( (response) => {
                if (response.errNo === 0 && response.data.data) {
                    // this.ticketForm.data = response.data.data;
                    this.resetTicketInternal();
                    if (this.autoScanNext) {
                        this.showSection = this.sections.CAMERA;
                        // this.showCamera = true;
                        this.startCamera = true;
                    } else {
                        // this.showSection = this.sections.SEARCH_BUTTONS;
                        this.showSection = this.lastSearchType.length > 0 ? this.lastSearchType : this.sections.SEARCH_BUTTONS;
                        if (this.showSection === "CAMERA") {
                            this.startCamera = true;
                        }
                    }
                }
            })
            .finally(()=> {
                this.isLoadingInternal = false;
            })
    }

    public setOnSite(isOnSite: boolean) {
        this.ticketFormInternal.ticket.onSite = isOnSite;
        // this.submitOnSite();
        this.isSiteFormDirty = true;
        this.submitData();
    }

    // public onSetSiteFromRow(ticketForm: ITicketForm) {
    //     this.ticketFormInternal = ticketForm;
    //     this.submitData();
    // }


    public doSearch(showIt: boolean) {
        this.ticketFormInternal = this.ticketService.factoryTicketForm();
        this.startCamera = false;
        this.ticketFormBaseTriggerSearch = ticketService.factoryTicketFormBase();
        if (showIt) {
            this.resetTicketInternal();
            this.showSection = this.sections.SEARCH;
        } else {
            this.showSection = this.sections.SEARCH_BUTTONS;
        }
    }

    public confirmCloseDirty() {
        this.isSiteFormDirty = true;
        this.showSiteFormDirtyConfirm = false;
        this.cancelEverything();
    }

    public cancelEverything() {
        if (!this.isSiteFormDirty) {
              this.showSiteFormDirtyConfirm = true;
              return;
        }
        this.resetTicketInternal();
        this.showSection = this.lastSearchType.length > 0 ? this.lastSearchType : this.sections.SEARCH_BUTTONS;
        if (this.showSection === "CAMERA") {
            this.startCamera = true;
        }

        // this.reset();
        // this.startCamera = false;
        // this.showSection = this.sections.SEARCH_BUTTONS;
    }

    public startScan() {
        this.ticketFormInternal = this.ticketService.factoryTicketForm();
        this.ticketFormBaseTriggerSearch = ticketService.factoryTicketFormBase();
        if (!this.startCamera) {
            this.startCamera = true;
        }
        // this.showCamera = true;
        this.searchTermTrigger = "";
        this.lastSearchType = this.sections.CAMERA;
        this.showSection = this.sections.CAMERA;

    }

    public cancelCamera() {
        this.showSection = this.sections.SEARCH_BUTTONS;
        this.startCamera = false;
    }

    public resetTicketInternal() {
        this.ticketFormInternal = this.ticketService.factoryTicketForm();
    }

    public get getHasSeat(): boolean {
        return this.ticketService.hasSeat(this.ticketFormInternal);
    }

    public get getSeatDescription() {
        return this.ticketService.getSeatDescription(this.ticketFormInternal);
    }

}
</script>

<style scoped>
.sticky {
    position: -webkit-sticky; /* Safari */
    position: sticky;
    top: 0;
}
</style>
