<template functional>
    <div>
        <div class="row">
            <div class="col s9">
                <span v-text="$options.methods.getAthleteName(props.checkinAthlete)"></span>
            </div>
            <div class="col s3">
                <span v-text="$options.methods.getDateOfBirth(props.checkinAthlete.dob)" class="right"></span>
            </div>
        </div>

        <div class="row">
            <div class="col s12">
                <span v-text="props.checkinAthlete.club"></span>
            </div>
        </div>

        <div class="row">
            <div class="col s12">
                <span v-text="$options.methods.getEventsDescription(props.checkinAthlete)"></span>
            </div>
        </div>

        <div class="row">

            <div class="col s12">
                <button class="btn waves-effect waves green right"
                        :disabled="props.isLoading"
                        v-on:click="listeners.onSelect(props.index)"
                >Select</button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {format, parse} from "date-fns"
import {ICheckinAthlete} from "../../../../competition/checkin/checkin-models"

export default {
    props: ["checkinAthlete", "isLoading", "onSelect", "index"],
    methods: {
        getAthleteName: (checkinAthlete: ICheckinAthlete) => {
            return checkinAthlete.bibNo + ": " + checkinAthlete.firstName + " " +
                checkinAthlete.surName + (checkinAthlete.urn && checkinAthlete.urn.toString().length > 0 ? " : " + checkinAthlete.urn :  "");
        },
        getDateOfBirth: (dob: string) => {
            const dobFormatted = format(parse(dob), "Do MMM YYYY" );
            return dobFormatted;
        },
        getEventsDescription: (checkinAthlete: ICheckinAthlete) => {
            const compEvents = checkinAthlete.entries.map( (entry) => {
                return entry.ageGroup + " - " + entry.name;
            });
            return compEvents.join(", ");
        }
    }
}
</script>
