<template>
  <TicketFormGatekeeper
    :comp-id="compId"
    :ticket-form="ticketForm"
    :is-loading="isLoading"
    v-on:submitData="submitData"
    v-on:ticketKeyChange="ticketKeyChange"
  ></TicketFormGatekeeper>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { TicketData } from "../ticket-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { TicketService } from "../ticket-service";
import {
  ITicketForm,
  ITicketFormBaseMeta,
  ITicketFormDataPayload,
} from "../ticket-models";
import TicketForm from "../ticket-form.vue";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import TicketFormGatekeeper from "./ticket-form-gatekeeper.vue";

@Component({
  name: "ticket-route-gatekeeper",
  components: { TicketFormGatekeeper, TicketForm },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class TicketRouteGatekeeper extends Vue {
  public readonly configApp: IConfigApp;

  public isLoading = false;
  public routeKey: string = "";
  public compId: number = 0;
  public ticketService: TicketService = new TicketService();
  public ticketForm: ITicketForm = this.ticketService.factoryTicketForm();
  public ticketFormBaseMeta: ITicketFormBaseMeta =
    this.ticketService.factoryTicketFormBaseMeta();
  public ticketData = new TicketData();

  public created() {
    console.log("TicketRouteGatekeeper.created()....this.$route", this.$route);
    const compId: string = this.$route.params.compId
      ? this.$route.params.compId
      : "0";
    this.compId = Number(compId);
  }

  public reset() {
    if (this.ticketForm.id === 0) {
      return;
    }
    this.routeKey = "";
    this.ticketForm = this.ticketService.factoryTicketForm();
  }

  public loadData(key: string) {
    this.isLoading = true;
    const prom = this.ticketData.getTicketData(this.compId, key);
    handleResponseMessages(prom);
    prom
      .then((response) => {
        if (response.errNo === 0) {
          const ticketFormBaseMeta = response.meta
            ? response.meta
            : this.ticketService.factoryTicketFormBaseMeta();
          this.ticketFormBaseMeta = ticketFormBaseMeta;
          const ticket: ITicketForm = {
            ...response.data,
            competition: ticketFormBaseMeta.competition,
          };
          this.ticketForm = ticket;
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public ticketKeyChange(key: string) {
    // if (key.toUpperCase() === this.routeKey.toUpperCase()) {
    //     return;
    // }
    this.routeKey = key;
    this.loadData(key);
  }

  public submitData(ticketForm: ITicketForm) {
    this.isLoading = true;
    const ticketFormDataPayload: ITicketFormDataPayload = {
      id: ticketForm.id,
      data: ticketForm.data,
      onSite: ticketForm.ticket.onSite,
    };
    const prom = this.ticketData.submitFormData(
      ticketForm.guid,
      ticketFormDataPayload
    );
    handleResponseMessages(prom);
    prom
      .then((response) => {
        if (response.errNo === 0 && response.data.data) {
          this.ticketForm.data = response.data.data;
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  // public submitOnSite(ticketForm: ITicketForm) {
  //     this.isLoading = true;
  //     const ticketFormDataPayload: ITicketFormDataPayload = {
  //         id: ticketForm.id,
  //         onSite: ticketForm.ticket.onSite
  //     };
  //     const prom = this.ticketData.submitFormData(ticketForm.guid, ticketFormDataPayload);
  //     handleResponseMessages(prom);
  //     prom
  //         .then( (response) => {
  //             if (response.errNo === 0 && response.data.data) {
  //                 this.ticketForm.data = response.data.data;
  //             }
  //         })
  //         .finally(()=> {
  //             this.isLoading = false;
  //         })
  // }
}
</script>
