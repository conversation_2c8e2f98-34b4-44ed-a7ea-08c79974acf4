<template>
  <div style="padding: var(--e4s-gap--standard)">
    <div class="e4s-vertical-spacer--standard"></div>
    <div class="e4s-flex-row" v-if="parentCompId > 0">
      <ButtonGenericV2
        text="Back to Comp"
        @click="gotToParentComp"
        class="e4s-button--auto e4s-flex-row--end"
      />
    </div>
    <div class="e4s-vertical-spacer--standard"></div>
    <SecondaryCustFormGrid :secondary-ref-obj="getSecondaryRefObj" />
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import SecondaryCustFormGrid from "./cust/secondary-cust-form-grid.vue";
import { ISecondaryRefObj } from "./secondary-models";
import { SecondaryService } from "./secondary-service";
import { CompetitionData } from "../competition/competition-data";
import { CompetitionService } from "../competition/competiton-service";
import { ICompetitionSummaryPublic } from "../competition/competition-models";
import { ENTRY_STORE_CONST } from "../entry/entry-store";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { LAUNCH_ROUTES_PATHS } from "../launch/launch-routes";

@Component({
  name: "secondary-public",
  components: {
    ButtonGenericV2,
    SecondaryCustFormGrid,
  },
})
export default class SecondaryPublic extends Vue {
  public id: number = 0;
  public parentCompId: number = 0;
  public isLoading = false;

  public competitionService = new CompetitionService();
  public competitionSummaryPublic: ICompetitionSummaryPublic =
    this.competitionService.factorySummaryPublic();

  public created() {
    const id: number = isNaN(Number(this.$route.params.id))
      ? 0
      : parseInt(this.$route.params.id, 0);
    this.id = id;

    this.parentCompId = isNaN(Number(this.$route.query.parentCompId))
      ? 0
      : Number(this.$route.query.parentCompId);

    this.isLoading = true;
    new CompetitionData()
      .getCompById(id)
      .then((resp) => {
        if (resp.errNo === 0) {
          this.competitionSummaryPublic = resp.data;
          this.$store.commit(
            ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
              "/" +
              ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_COMP_PUBLIC,
            resp.data
          );
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public get getSecondaryRefObj(): ISecondaryRefObj {
    const secondaryRefObj = new SecondaryService().factorySecondaryRefObj();
    if (this.competitionSummaryPublic.compId > 0) {
      secondaryRefObj.objId = this.competitionSummaryPublic.compId;
      secondaryRefObj.objType = "COMP";
      secondaryRefObj.compId = this.competitionSummaryPublic.compId;
      secondaryRefObj.objName = this.competitionSummaryPublic.compName;
    }
    return secondaryRefObj;
  }

  public gotToParentComp() {
    this.$router.push({
      path: "/" + LAUNCH_ROUTES_PATHS.SHOW_ENTRY + "/" + this.parentCompId,
    });
  }
}
</script>
