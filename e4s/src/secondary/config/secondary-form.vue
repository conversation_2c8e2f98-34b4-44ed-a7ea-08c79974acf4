<template>
  <div>
    <div v-show="showSection === sections.FORM">
      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-form-header">Store Item</div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="input-field col s12 m12 l6">
          <label class="active" :for="PREFIX + 'prod-name'">
            Name
            <span
              v-if="secondaryDefInternal.id > 0"
              v-text="' (' + secondaryDefInternal.prod.id + ')'"
            ></span>
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="prod.name"
            />
          </label>

          <input
            :id="PREFIX + 'prod-name'"
            :name="PREFIX + 'prod-name'"
            type="text"
            v-model="secondaryDefInternal.prod.name"
            v-on:change="changed"
            placeholder=""
          />
        </div>

        <div class="input-field col s12 m12 l6">
          <label class="active" :for="PREFIX + 'description'">
            Description
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="prod.description"
            />
          </label>

          <input
            :id="PREFIX + 'description'"
            :name="PREFIX + 'description'"
            type="text"
            v-model="secondaryDefInternal.prod.description"
            v-on:change="changed"
            placeholder=""
          />
        </div>

        <div class="input-field col s12 m6 l3">
          <label class="active" :for="PREFIX + 'initial-quantity'">
            Initial Quantity (greater than zero)
            <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="prod.stockQty"
            />
          </label>

          <input
            :id="PREFIX + 'initial-quantity'"
            :name="PREFIX + 'initial-quantity'"
            type="number"
            v-model.number="secondaryDefInternal.prod.stockQty"
            v-on:change="changed"
            placeholder=""
          />
        </div>

        <div class="input-field col s12 m6 l3">
          <label class="active" for="competition-active">
            Download
            <FieldHelp help-key="prod.download"></FieldHelp>
          </label>
          <p>
            <label>
              <input
                id="competition-active"
                class="e4s-checkbox"
                type="checkbox"
                v-model="secondaryDefInternal.prod.download"
              />
              <span class="e4s-bold"> Active </span>
            </label>
          </p>
        </div>

        <div class="input-field col s12 m6 l3">
          <label class="active" :for="PREFIX + 'initial-image'">
            Image
            <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="prod.image"
            />
            <a
              :href="getOpenImageLink"
              target="_blank"
              v-if="getOpenImageLink.length > 0"
            >
              <span>Open</span>
            </a>
          </label>

          <file-upload
            v-on:onUpload="onImageUpload"
            class="e4s-force-inline-block"
          >
          </file-upload>
          <AttachmentThumbnail
            :image-link="secondaryDefInternal.prod.image"
          ></AttachmentThumbnail>
        </div>
      </div>

      <!--     price       -->
      <div class="e4s-section-padding-separator"></div>

      <div>
        <div class="row">
          <div class="col s12 m12 l12">
            <div class="e4s-form-header">
              Price
              <a href="#" v-on:click.prevent="showPrice">Edit</a>
            </div>
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="input-field col s12 m6 l3">
          <label class="active" :for="PREFIX + 'max-purchase-count'">
            Price
            <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="prod.price.price"
            />
          </label>

          <input
            :id="PREFIX + 'price-price'"
            :name="PREFIX + 'price-price'"
            type="number"
            v-model.number="secondaryDefInternal.prod.price.price"
            v-on:change="changed"
            placeholder=""
          />
        </div>

        <div class="input-field col s12 m6 l3">
          <label class="active" :for="PREFIX + 'max-purchase-count'">
            Sale Price
            <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="prod.price.salePrice"
            />
          </label>

          <input
            :id="PREFIX + 'price-price'"
            :name="PREFIX + 'price-price'"
            type="number"
            v-model.number="secondaryDefInternal.prod.price.salePrice"
            v-on:change="changed"
            placeholder=""
          />
        </div>

        <div class="input-field col s12 m6 l3">
          <label class="active" :for="PREFIX + 'max-purchase-count'">
            Sale Price End Date
            <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="prod.price"
            />
          </label>

          <DateEntryMat
            :iso-date="secondaryDefInternal.prod.price.saleEndDate"
            v-on:onSelected="
              secondaryDefInternal.prod.price.saleEndDate = $event
            "
          >
          </DateEntryMat>
        </div>
      </div>
      <!--     /price       -->

      <!--     Restrictions       -->
      <div class="e4s-section-padding-separator"></div>

      <div>
        <div class="row">
          <div class="col s12 m12 l12">
            <div class="e4s-form-header">Restrictions</div>
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="input-field col s12 m6 l3">
          <label class="active" :for="PREFIX + 'max-purchase-count'">
            Maximum allowed to purchase (0 = unlimited)
            <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="maxAllowed"
            />
          </label>

          <input
            :id="PREFIX + 'max-purchase-count'"
            :name="PREFIX + 'max-purchase-count'"
            type="number"
            v-model.number="secondaryDefInternal.maxAllowed"
            v-on:change="changed"
            placeholder=""
          />
        </div>

        <div class="input-field col s12 m6 l3">
          <label class="active" :for="PREFIX + 'per-account'">
            Restricted to
            <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
            <FieldValidationLabel
              :validation-controller="validationController"
              prop-path="perAcc"
            />
          </label>

          <select
            v-model="secondaryDefInternal.perAcc"
            :id="PREFIX + 'per-account'"
            class="browser-default"
          >
            <option value=""></option>
            <option value="A">Athlete</option>
            <option value="U">User</option>
          </select>
        </div>
      </div>
      <!--     /Restrictions       -->

      <!--     Ticket      -->
      <div class="e4s-section-padding-separator"></div>

      <div>
        <div class="row">
          <div class="col s12 m12 l12">
            <div class="e4s-form-header">Ticket</div>
          </div>
        </div>
      </div>

      <div class="e4s-section-padding-separator"></div>

      <div class="row">
        <div class="col s12 m12 l12">
          <p>
            <label>
              <input
                :id="PREFIX + 'data-req'"
                class="e4s-checkbox"
                type="checkbox"
                v-model="secondaryDefInternal.prod.download"
              />
              <span class="e4s-bold"> Is a ticket </span>
            </label>
          </p>
        </div>
      </div>

      <SecondaryTicket
        v-if="secondaryDefInternal.prod.download"
        v-model="secondaryDefInternal.prod.ticket"
      ></SecondaryTicket>
      <!--     /Ticket      -->

      <!--     attributes       -->
      <div class="e4s-section-padding-separator"></div>

      <div>
        <div class="row">
          <div class="col s12 m12 l12">
            <div class="e4s-form-header">
              Attributes
              <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
              <ButtonGenericV2 text="Add" v-on:click="addAttribute" />
              <!--              <button-->
              <!--                class="btn waves-effect waves green"-->
              <!--                :disabled="isLoading"-->
              <!--                v-on:click.stop="addAttribute"-->
              <!--              >-->
              <!--                <span>Add</span>-->
              <!--              </button>-->
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col s12 m12 l12">
          <div
            v-for="(item, index) in secondaryDefInternal.prod.attributes"
            :key="index"
          >
            <SecondaryAttributes
              :index="index"
              :secondary-attributes="item"
              v-on:onChanged="onAttributesChanged"
            ></SecondaryAttributes>
          </div>
        </div>
      </div>
      <!--     /attributes       -->

      <!--     variants       -->
      <div v-if="getHasAttributes">
        <div class="e4s-section-padding-separator"></div>

        <div>
          <div class="row">
            <div class="col s12 m12 l12">
              <div class="e4s-form-header">
                Variants
                <FieldHelp
                  help-key="builderCompetition.options.live"
                ></FieldHelp>
                <button
                  class="btn waves-effect waves green"
                  :disabled="isLoading"
                  v-on:click.stop="createSecondaryVariant"
                >
                  <span>Add</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div
          v-for="(variation, index) in secondaryDefInternal.prod.variations"
          :key="index"
        >
          <SecondaryVariantRow
            :secondary-variant="variation"
            v-on:onEdit="onEditVariation"
            v-on:onDelete="onDeleteVariation"
          >
          </SecondaryVariantRow>
        </div>
      </div>
      <!--     /variants       -->

      <div class="row">
        <div class="col s12 m12 l12">
          <div class="right">
            <LoadingSpinner v-if="isLoading"></LoadingSpinner>
            <ButtonGenericV2
              text="Cancel"
              button-type="tertiary"
              :disabled="isLoading"
              v-on:click="cancel"
            />
            <ButtonGenericV2
              text="Save"
              :disabled="isLoading"
              v-on:click="submit(true)"
            />
            <!--            <button-->
            <!--              class="btn waves-effect waves red"-->
            <!--              :disabled="isLoading"-->
            <!--              v-on:click.stop="cancel"-->
            <!--            >-->
            <!--              <span>Cancel</span>-->
            <!--            </button>-->

            <!--            <button-->
            <!--              class="btn waves-effect waves green"-->
            <!--              :disabled="isLoading"-->
            <!--              v-on:click.stop="submit"-->
            <!--            >-->
            <!--              <span>Save</span>-->
            <!--            </button>-->
          </div>
        </div>
      </div>

      <!--            <div class="row">-->
      <!--                <div class="col s12 m12 l12">-->
      <!--                    {{secondaryDefInternal}}-->
      <!--                </div>-->
      <!--            </div>-->
    </div>

    <E4sModal
      v-if="showSection === sections.PRICE"
      :css-class="'e4s-modal-container--full-size'"
    >
      <div slot="header"></div>
      <div slot="body">
        <StandardForm title="Price">
          <div slot="form-content">
            <PriceForm
              :price-prop="secondaryPriceEdit"
              :is-admin="isAdmin"
              :suppress-save="true"
              :config-options="configApp.options"
              :is-national="getIsNational"
              v-on:onCancel="onPriceCancel"
              v-on:onSubmit="onPriceSubmit"
            >
            </PriceForm>
          </div>
        </StandardForm>
      </div>
      <div slot="footer"></div>
    </E4sModal>

    <E4sModal
      v-if="showSection === sections.VARIANT"
      :css-class="'e4s-modal-container--full-size'"
    >
      <div slot="header"></div>
      <div slot="body">
        <StandardForm title="Variant">
          <div slot="form-content">
            <SecondaryVariantForm
              :name="secondaryDefInternal.prod.name"
              :secondary-attributes="secondaryDefInternal.prod.attributes"
              :secondary-variant="secondaryVariantEdit"
              :is-loading="isLoading"
              is-national="getIsNational"
              v-on:onCancel="cancelSecondaryVariant"
              v-on:onSubmit="submitSecondaryVariant"
            >
            </SecondaryVariantForm>
          </div>
        </StandardForm>
      </div>
      <div slot="footer"></div>
    </E4sModal>

    <E4sModal
      headerMessage="Delete Variant"
      :body-message="
        'Are you sure you want to delete: ' + secondaryVariantEdit.name
      "
      v-if="showDeleteVariationConf"
      :is-loading="isLoading"
      v-on:closeSecondary="showDeleteVariationConf = false"
      v-on:closePrimary="deleteVariation"
    >
    </E4sModal>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";

import { Prop, Watch } from "vue-property-decorator";
import {
  ISecondaryDef,
  ISecondaryPrice,
  ISecondaryAttributes,
  ISecondaryVariant,
} from "../secondary-models";
import { SecondaryService } from "../secondary-service";
import { ValidationController } from "../../validation/validation-controller";
import FieldValidationLabel from "../../validation/validation-field-lable.vue";
import PriceForm from "../../price/price-form.vue";
import FieldHelp from "../../common/ui/field/field-help/field-help.vue";
import { SecondaryData } from "../secondary-data";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp } from "../../config/config-app-models";
import { IPrice } from "../../price/price-models";
import { PriceService } from "../../price/price-service";
import E4sModal from "../../common/ui/e4s-modal.vue";
import StandardForm from "../../common/ui/standard-form/standard-form.vue";
import DateEntryMat from "../../common/ui/datetime/date-entry-mat.vue";
import SecondaryAttributes from "./secondary-attributes.vue";
import SecondaryVariantForm from "./variation/secondary-variant-form.vue";
import SecondaryVariantRow from "./variation/secondary-variant-row.vue";
import { SecondaryVariantData } from "./variation/secondary-variant-data";
import { handleResponseMessages } from "../../common/handle-http-reponse";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import FileUpload from "../../common/ui/file-upload.vue";
import AttachmentThumbnail from "../../common/ui/attachments/attachment-thumbnail.vue";
import SecondaryTicket from "./secondary-ticket.vue";
import { simpleClone } from "../../common/common-service-utils";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
const secondaryService: SecondaryService = new SecondaryService();

@Component({
  name: "secondary-form",
  components: {
    ButtonGenericV2,
    SecondaryTicket,
    AttachmentThumbnail,
    FileUpload,
    SecondaryVariantRow,
    SecondaryVariantForm,
    SecondaryAttributes,
    DateEntryMat,
    StandardForm,
    E4sModal,
    FieldHelp,
    PriceForm,
    FieldValidationLabel,
  },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class SecondaryForm extends Vue {
  public configApp: IConfigApp;

  @Prop({
    default: () => {
      return secondaryService.factorySecondaryDef();
    },
  })
  public readonly secondaryDef: ISecondaryDef;

  public isLoading: boolean = false;
  public secondaryService = secondaryService;
  public secondaryDefInternal: ISecondaryDef =
    this.secondaryService.factorySecondaryDef();
  public requiresGrouping = false;
  public PREFIX = Math.random().toString(36).substring(2);

  public priceService = new PriceService();
  public secondaryPriceEdit: ISecondaryPrice =
    this.secondaryService.factorySecondaryPrice();
  public validationController: ValidationController =
    new ValidationController();

  public secondaryVariantEdit: ISecondaryVariant =
    this.secondaryService.factorySecondaryVariant();
  public showDeleteVariationConf = false;

  public secondaryData = new SecondaryData();

  public sections = {
    FORM: "FORM",
    PRICE: "PRICE",
    VARIANT: "VARIANT",
  };
  public showSection: string = this.sections.FORM;

  public created() {
    this.secondaryDefInternal = this.fixInboundServerModel(
      R.clone(this.secondaryDef)
    );
  }

  @Watch("secondaryDef")
  public onSecondaryChanged(newValue: ISecondaryDef, oldValue: ISecondaryDef) {
    if (!R.equals(newValue, oldValue)) {
      return;
    }
    this.secondaryDefInternal = this.fixInboundServerModel(R.clone(newValue));
  }

  public fixInboundServerModel(secondaryDef: ISecondaryDef): ISecondaryDef {
    if (!this.secondaryService.hasAttributes(secondaryDef)) {
      secondaryDef.prod.attributes = [];
    }
    return secondaryDef;
  }

  public get getIsNational() {
    return (
      this.secondaryDefInternal.refObj.objType === "COMP" &&
      this.secondaryDefInternal.refObj.objName.toUpperCase() === "NATIONAL"
    );
  }

  public addAttribute() {
    const secondaryDefInternal = R.clone(this.secondaryDefInternal);
    secondaryDefInternal.prod.attributes.push(
      this.secondaryService.factorySecondaryAttributes()
    );
    this.secondaryDefInternal = secondaryDefInternal;
  }

  public onAttributesChanged(payload: {
    index: number;
    secondaryAttributes: ISecondaryAttributes;
  }) {
    const secondaryDefInternal: ISecondaryDef = R.clone(
      this.secondaryDefInternal
    );
    secondaryDefInternal.prod.attributes[payload.index] =
      payload.secondaryAttributes;
    this.secondaryDefInternal = secondaryDefInternal;
    this.changed();
  }

  public createSecondaryVariant() {
    this.secondaryVariantEdit =
      this.secondaryService.createVariationFromSecondaryDef(
        this.secondaryDefInternal
      );
    this.showSection = this.sections.VARIANT;
  }

  public cancelSecondaryVariant() {
    this.showSection = this.sections.FORM;
  }

  public submitSecondaryVariant(secondaryVariant: ISecondaryVariant) {
    this.isLoading = true;
    const secondaryVariantData: SecondaryVariantData =
      new SecondaryVariantData();
    const prom =
      secondaryVariant.id === 0
        ? secondaryVariantData.create(secondaryVariant)
        : secondaryVariantData.update(secondaryVariant);
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        const secondaryDef = R.clone(this.secondaryDefInternal);
        if (secondaryVariant.id === 0) {
          secondaryDef.prod.variations.push(secondaryVariant);
        } else {
          secondaryDef.prod.variations = secondaryDef.prod.variations.map(
            (secondaryVar) => {
              return secondaryVar.id === secondaryVariant.id
                ? secondaryVariant
                : secondaryVar;
            }
          );
        }
        this.secondaryDefInternal = secondaryDef;
        this.showSection = this.sections.FORM;
        if (resp.errNo === 0) {
          this.$emit("submitted", resp.data);
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public onEditVariation(secondaryVariant: ISecondaryVariant) {
    this.secondaryVariantEdit = R.clone(secondaryVariant);
    this.showSection = this.sections.VARIANT;
  }

  public onDeleteVariation(secondaryVariant: ISecondaryVariant) {
    this.secondaryVariantEdit = R.clone(secondaryVariant);
    this.showDeleteVariationConf = true;
  }

  public deleteVariation() {
    this.isLoading = true;
    const prom = new SecondaryVariantData().delete(
      this.secondaryVariantEdit.id
    );
    handleResponseMessages(prom);
    prom
      .then((response) => {
        if (response.errNo > 0) {
          return;
        }
        messageDispatchHelper(
          "Variation Deleted.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        this.$emit("submitted", response.data);
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public showPrice() {
    const price = this.secondaryDefInternal.prod.price;
    this.secondaryPriceEdit = {
      ...this.priceService.factoryGetPrice(),
      price: price.price,
      salePrice: price.salePrice,
      saleEndDate: price.saleEndDate,
    };
    this.showSection = this.sections.PRICE;
  }

  public onPriceCancel() {
    this.showSection = this.sections.FORM;
  }

  public onPriceSubmit(price: IPrice) {
    const secondaryDef = R.clone(this.secondaryDefInternal);
    secondaryDef.prod.price = {
      ...this.secondaryService.factorySecondaryPrice(),
      price: price.price,
      salePrice: price.salePrice,
      saleEndDate: price.saleEndDate,
    };
    this.secondaryDefInternal = secondaryDef;
    this.showSection = this.sections.FORM;
    this.changed();
  }

  public get getHasAttributes() {
    return this.secondaryService.hasAttributes(this.secondaryDefInternal);
  }

  public onImageUpload(imagePath: string) {
    this.secondaryDefInternal.prod.image = imagePath;
    if (this.secondaryDefInternal.id > 0) {
      this.submit(false);
    }
  }

  public get getOpenImageLink() {
    return this.secondaryDefInternal.prod.image;
  }

  public changed() {
    // this.$emit("changed", R.clone(this.secondaryDefInternal));
  }

  public cancel() {
    this.$emit("cancelled");
  }

  public validate() {
    this.validationController.reset();
    this.validationController.setErrors(
      this.secondaryService.validateSecondary(this.secondaryDefInternal)
    );
  }

  public submit(closeAfterSave: boolean) {
    this.validate();
    if (!this.validationController.isValid) {
      return;
    }

    this.isLoading = true;
    const prom =
      this.secondaryDefInternal.id === 0
        ? this.secondaryData.create(this.secondaryDefInternal)
        : this.secondaryData.update(this.secondaryDefInternal);

    handleResponseMessages(prom);
    prom
      .then((response) => {
        if (response.errNo > 0) {
          return;
        }
        //  TODO returned model not correct.
        // this.secondaryDefInternal = response.data;
        if (closeAfterSave) {
          // this.$emit("submit", R.clone(this.secondaryDefInternal));
          this.$emit("submitted", simpleClone(this.secondaryDefInternal));
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public get getShowRequiresGrouping() {
    // return this.requiresGrouping || this.secondaryDefInternal.items.length > 1;
    return false;
  }
}
</script>
