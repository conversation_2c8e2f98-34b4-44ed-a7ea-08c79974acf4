<template>
    <div>
        <div class="row">

            <div class="input-field col s12 m6 l3">

                <label class="active" :for="PREFIX + 'secondary-variant--name'">
                    Name (E.g. <PERSON><PERSON>)
                    <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
                    <ValidationFieldLable :validation-controller="validationController" prop-path="name"/>
                </label>

                <input
                    :id="PREFIX + 'secondary-variant--name'"
                    :name="PREFIX + 'secondary-variant--name'"
                    type="text"
                    v-model="secondaryAttributesInternal.name"
                    v-on:change="changed"
                    placeholder=""/>
            </div>

            <div class="input-field col s12 m6 l3">

                <label class="active" :for="PREFIX + 'secondary-variant--values'">
                    Value(s) (E.g. S | M | L | XL | XXL)
                    <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
                    <ValidationFieldLable :validation-controller="validationController" prop-path="name"/>
                </label>

                <input
                    :id="PREFIX + 'secondary-variant--values'"
                    :name="PREFIX + 'secondary-variant--values'"
                    type="text"
                    v-model="secondaryAttributesInternal.values"
                    v-on:change="changed"
                    placeholder=""/>
            </div>

        </div>
    </div>
</template>

<script lang="ts">

import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {SecondaryService} from "../secondary-service"
import {ISecondaryAttributes} from "../secondary-models"
import * as R from "ramda";
import {ValidationController} from "../../validation/validation-controller"
import FieldHelp from "../../common/ui/field/field-help/field-help.vue"
import ValidationFieldLable from "../../validation/validation-field-lable.vue"
const secondaryService: SecondaryService = new SecondaryService();

@Component({
    name: "secondary-attributes",
    components: {
        ValidationFieldLable,
        FieldHelp
    }
})
export default class SecondaryAttributes extends Vue {
    @Prop({
        required: true
    })
    public readonly index: number;

    @Prop({
        default: () => {
            return secondaryService.factorySecondaryAttributes()
        }
    })
    public readonly secondaryAttributes: ISecondaryAttributes;

    public secondaryAttributesInternal: ISecondaryAttributes = secondaryService.factorySecondaryAttributes();
    public validationController: ValidationController = new ValidationController();

    public PREFIX = Math.random().toString(36).substring(2);

    public created() {
        this.secondaryAttributesInternal = R.clone(this.secondaryAttributes);
    }

    @Watch("secondaryAttributes")
    public onSecondaryVariantChanged(newValue: ISecondaryAttributes, oldValue: ISecondaryAttributes) {
        if (!R.equals(newValue, oldValue)) {
            this.secondaryAttributesInternal = R.clone(newValue);
        }
    }

    public changed() {
        this.$emit("onChanged", {
            index: this.index,
            secondaryAttributes: R.clone(this.secondaryAttributesInternal)
        })
    }

}
</script>
