<template>
    <div>
        <SecondaryForm :secondary="secondaryDefInternal"></SecondaryForm>




    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";

    import {Prop, Watch} from "vue-property-decorator"
    import { ISecondaryDef } from "../secondary-models";
    import { SecondaryService } from "../secondary-service";
    import {ValidationController} from "../../validation/validation-controller";
    import SecondaryForm from "./secondary-form.vue"
    // import {BuilderService} from "../../builder/builder-service"
    // import {IBuilderCompetition} from "../../builder/builder-models"
    // import { CompEventService } from "../../compevent/compevent-service";
    // import { ICompEvent } from "../../compevent/compevent-models";
    // import { SecondaryData } from "../secondary-data";
    // import { messageDispatchHelper } from "../../user-message/user-message-store";
    // import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";

    const secondaryService: SecondaryService = new SecondaryService();
    // const builderService: BuilderService = new BuilderService();
    // const compEventService: CompEventService = new CompEventService();

    @Component({
        name: "secondary-form-controller",
        components: {SecondaryForm}
    })
    export default class SecondaryFormController extends Vue {
        @Prop({
            default: () => {
                return secondaryService.factorySecondaryDef();
            }
        }) public readonly secondaryDef: ISecondaryDef;

        public secondaryService = secondaryService;

        public secondaryDefInternal: ISecondaryDef = this.secondaryService.factorySecondaryDef();

        public PREFIX = Math.random().toString(36).substring(2);
        public validationController: ValidationController = new ValidationController();

        public isLoading: boolean = false;

        public created() {
            this.secondaryDefInternal = R.clone(this.secondaryDef);
        }

        @Watch("secondaryDef")
        public onSecondaryChanged(newValue: ISecondaryDef, oldValue: ISecondaryDef) {

            if (!R.equals(newValue, oldValue)) {
                return;
            }
            this.secondaryDefInternal = R.clone(newValue);
        }


        public changed() {
            this.$emit("changed", R.clone(this.secondaryDefInternal));
        }


        public cancel() {
            this.$emit("cancelled");
        }

        public delete() {
            this.$emit("onDelete");
        }

    }

</script>
