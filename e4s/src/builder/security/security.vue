<template>
  <div class="e4s-flex-column e4s-gap--large">
    <!--user-club-->

    <div class="e4s-flex-row e4s-flex-row--end e4s-gap--standard">
      <ButtonGenericV2
        v-if="!clubAddShow"
        text="All Clubs"
        @click="quickSecurityChanged('clubs', true)"
      />
      <ButtonGenericV2
        v-if="!clubAddShow"
        text="Select Specific Club"
        @click="clubAdd"
        class="e4s-button--auto"
        style="width: 180px"
      />
    </div>

    <FormGenericFieldGridV2 v-if="clubAddShow">
      <template slot="content">
        <div class="e4s-flex-row e4s-gap--standard">
          <ClubTypeAheadV2 @input="clubSelectedCrud" />

          <ButtonGenericV2
            text="Cancel"
            class="e4s-button--auto"
            button-type="tertiary"
            @click="clubCancel"
          />
        </div>

        <span></span>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2 v-if="securityLocal.clubs.length > 0">
      <template slot="content">
        <div class="e4s-flex-column e4s-gap--standard">
          <div
            v-for="club in securityLocal.clubs"
            :key="'club-' + club.id"
            class="e4s-grid-3-columns"
          >
            <span v-text="club.id"></span>
            <span v-text="club.name"></span>

            <ButtonGenericV2
              text="X"
              @click="clubRemove(club.id)"
              button-type="destructive"
              style="width: 35px; justify-self: flex-end"
            />
          </div>
        </div>

        <span></span>
      </template>
    </FormGenericFieldGridV2>

    <!--/user-club-->

    <hr class="dat-e4s-hr dat-e4s-hr-only dat-e4s-hr--slightly-lighter" />

    <!--county-->
    <div class="e4s-flex-row e4s-flex-row--end e4s-gap--standard">
      <ButtonGenericV2
        v-if="!countyAddShow"
        text="All Counties"
        @click="quickSecurityChanged('counties', true)"
      />
      <ButtonGenericV2
        v-if="!countyAddShow"
        text="Select Specific County"
        @click="countyAdd"
        class="e4s-button--auto"
        style="width: 180px"
      />

      <ButtonGenericV2
        text="Cancel County Search"
        class="e4s-button--auto"
        button-type="tertiary"
        @click="countyCancel"
        v-if="countyAddShow"
      />
    </div>

    <FormGenericFieldGridV2 v-if="countyAddShow">
      <template slot="content">
        <AreaLookupV2 @input="countySelected" />
        <span></span>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2 v-if="securityLocal.counties.length > 0">
      <template slot="content">
        <div class="e4s-flex-column e4s-gap--standard">
          <div
            v-for="county in securityLocal.counties"
            :key="'county-' + county.id"
            class="e4s-grid-3-columns"
          >
            <span v-text="county.id"></span>
            <span v-text="county.name"></span>

            <ButtonGenericV2
              text="X"
              @click="countyRemove(county.id)"
              button-type="destructive"
              style="width: 35px; justify-self: flex-end"
            />
          </div>
        </div>

        <span></span>
      </template>
    </FormGenericFieldGridV2>

    <hr class="dat-e4s-hr dat-e4s-hr-only dat-e4s-hr--slightly-lighter" />

    <!--region-->
    <div class="e4s-flex-row e4s-flex-row--end e4s-gap--standard">
      <ButtonGenericV2
        v-if="!regionAddShow"
        text="All Regions"
        @click="quickSecurityChanged('regions', true)"
      />
      <ButtonGenericV2
        v-if="!regionAddShow"
        text="Select Specific Region"
        @click="regionAdd"
        class="e4s-button--auto"
        style="width: 180px"
      />

      <ButtonGenericV2
        text="Cancel Region Search"
        class="e4s-button--auto"
        button-type="tertiary"
        @click="regionCancel"
        v-if="regionAddShow"
      />
    </div>

    <FormGenericFieldGridV2 v-if="regionAddShow">
      <template slot="content">
        <AreaLookupV2 @input="regionSelected" />
        <span></span>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2 v-if="securityLocal.regions.length > 0">
      <template slot="content">
        <div class="e4s-flex-column e4s-gap--standard">
          <div
            v-for="region in securityLocal.regions"
            :key="'region-' + region.id"
            class="e4s-grid-3-columns"
          >
            <span v-text="region.id"></span>
            <span v-text="region.name"></span>

            <ButtonGenericV2
              text="X"
              @click="regionRemove(region.id)"
              button-type="destructive"
              style="width: 35px; justify-self: flex-end"
            />
          </div>
        </div>

        <span></span>
      </template>
    </FormGenericFieldGridV2>
    <!--/region-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { ISecurity } from "../../athletecompsched/athletecompsched-models";
import { Prop, Watch } from "vue-property-decorator";
import { SecurityService } from "./security-service";
import { IOrgConfig } from "../../config/config-app-models";
import { IBase, IBaseConcrete } from "../../common/common-models";
import AreaLookup from "../../area/area-lookup.vue";
import { CommonService } from "../../common/common-service";
import ClubSearch from "../../club/club-search.vue";
import ClubTypeAheadV2 from "../../club/v2/clubtypeahead/club-type-ahead-v2.vue";
import PrimaryLink from "../../common/ui/layoutV2/href/PrimaryLink.vue";
import InputCheckboxV2 from "../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { IClubCrud } from "../../club/club-models";
import FormGenericFieldGridV2 from "../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import AreaLookupV2 from "../../area/AreaLookupV2.vue";
import SectionThickLine from "../../common/ui/layoutV2/form/SectionThickLine.vue";
import { simpleClone } from "../../common/common-service-utils";

const securityService: SecurityService = new SecurityService();
const commonService: CommonService = new CommonService();

@Component({
  name: "security",
  components: {
    SectionThickLine,
    AreaLookupV2,
    FormGenericFieldGridV2,
    ButtonGenericV2,
    FormGenericInputTemplateV2,
    InputCheckboxV2,
    PrimaryLink,
    ClubTypeAheadV2,
    "club-search": ClubSearch,
    "area-lookup": AreaLookup,
  },
})
export default class Security extends Vue {
  @Prop({
    default: () => {
      return securityService.factory();
    },
  })
  public readonly security: ISecurity;

  public securityLocal: ISecurity = securityService.factory();

  public clubAddShow: boolean = false;
  public countyAddShow: boolean = false;
  public regionAddShow: boolean = false;

  public quickSecurityClubs: boolean = false;
  public quickSecurityCounties: boolean = false;
  public quickSecurityRegions: boolean = false;

  @Watch("security", { immediate: true })
  public onSecurityChanged(newValue: ISecurity) {
    if (!R.isNil(newValue)) {
      const data: ISecurity = R.mergeDeepRight(
        securityService.factory(),
        newValue
      ) as ISecurity;

      this.securityLocal = data;
    }
  }

  public clubAdd() {
    this.clubAddShow = true;
  }

  public clubSelected(org: IOrgConfig) {
    this.securityLocal.clubs!.push(org);
    this.clubAddShow = false;
    this.output();
  }

  public clubSelectedCrud(club: IClubCrud) {
    const org: IBaseConcrete = {
      id: club.id,
      name: club.clubname,
    };

    console.log("clubSelectedCrud() org", org);

    console.log(
      "clubSelectedCrud() clubs before",
      simpleClone(this.securityLocal.clubs)
    );

    if (securityService.isEmptyObject(org)) {
      return;
    }

    const clubs = securityService.addAndFilterOutEmptyAndDuplicateObjects(
      this.securityLocal.clubs! as IBaseConcrete[],
      org
    );

    console.log("clubSelectedCrud() clubs after", simpleClone(clubs));

    this.securityLocal.clubs = clubs;

    // this.securityLocal.clubs!.push(org);;
    this.clubAddShow = false;
    this.output();
  }

  public clubCancel() {
    this.clubAddShow = false;
  }

  public clubRemove(id: number) {
    this.securityLocal.clubs = commonService.removeById(
      this.securityLocal.clubs!,
      id
    );
    this.output();
  }

  public countyAdd() {
    this.countyAddShow = true;
  }

  public countySelected(county: IBase) {
    if (securityService.isEmptyObject(county as IBaseConcrete)) {
      return;
    }

    const objs = securityService.addAndFilterOutEmptyAndDuplicateObjects(
      this.securityLocal.counties! as IBaseConcrete[],
      county as IBaseConcrete
    );

    this.securityLocal.counties = objs;

    // this.securityLocal.counties!.push(county);
    this.countyAddShow = false;
    this.output();
  }

  public countyCancel() {
    this.countyAddShow = false;
  }

  public countyRemove(id: number) {
    this.securityLocal.counties = commonService.removeById(
      this.securityLocal.counties!,
      id
    );
    this.output();
  }

  public regionAdd() {
    this.regionAddShow = true;
  }

  public regionSelected(region: IBase) {
    if (securityService.isEmptyObject(region as IBaseConcrete)) {
      return;
    }

    const objs = securityService.addAndFilterOutEmptyAndDuplicateObjects(
      this.securityLocal.regions! as IBaseConcrete[],
      region as IBaseConcrete
    );

    this.securityLocal.regions = objs;

    // this.securityLocal.regions!.push(region);
    this.regionAddShow = false;
    this.output();
  }

  public regionCancel() {
    this.regionAddShow = false;
  }

  public regionRemove(id: number) {
    this.securityLocal.regions = commonService.removeById(
      this.securityLocal.regions!,
      id
    );
    this.output();
  }

  public quickSecurityChanged(which: keyof ISecurity, value: boolean) {
    // this.quickSecurity[which] = value;

    if (which === "clubs") {
      this.quickSecurityClubs = value;
    } else if (which === "counties") {
      this.quickSecurityCounties = value;
    } else if (which === "regions") {
      this.quickSecurityRegions = value;
    }

    if (value) {
      this.securityLocal[which] = [
        {
          id: 0,
          name: "ALL",
        },
      ];
    } else {
      this.securityLocal[which] = [];
    }
  }

  public output() {
    this.$emit("onChanged", R.clone(this.securityLocal));
  }
}
</script>
