import { reactive, UnwrapRef } from "@vue/composition-api";
import * as CompetitionDataV2 from "../../../competition/v2/competition-data-v2";
import { SuperCloneState } from "./super-clone-models";
import { factorySuperCloneState } from "./super-clone-service";
import { EventType } from "../../../athleteCompSched/athletecompsched-models";

export function factorySuperClone(
  stateInject?: SuperCloneState | UnwrapRef<SuperCloneState>
) {
  const state: SuperCloneState | UnwrapRef<SuperCloneState> = stateInject
    ? stateInject
    : factorySuperCloneState();

  function init(): Promise<void> {
    return getSuperCloneComps();
  }

  function getSuperCloneComps() {
    //  create a date 1 year ago from today
    const date1YearAgo = new Date();
    date1YearAgo.setFullYear(date1YearAgo.getFullYear() - 1);

    //  TODO needs to be National comps only.
    const compFilterParams = {
      fromDate: date1YearAgo.toISOString().split("T")[0],
      toDate: "",
      freeTextSearch: "",
      organiser: {
        id: 0,
        name: "",
      },
      location: {
        id: 0,
        name: "",
      },
      type: "ALL" as any as EventType,
      compOrg: {
        id: 0,
        name: "",
      },
      event: [],
      pagenumber: 1,
    };

    state.isLoading = true;
    return CompetitionDataV2.getFilteredComps(compFilterParams)
      .then((comps) => {
        state.compList = comps.data.map((comp) => {
          return {
            paymentCode: comp.competition.options.paymentCode,
            compName: comp.competition.name,
          };
        });
      })
      .finally(() => {
        state.isLoading = false;
      });

    //https://dev.entry4sports.co.uk/wp-json/e4s/v5/public/comps/filter?fromdate=2023-11-10T12:00&todate=&freetextsearch=&organiserid=0&locationid=0&type=ALL&comporgid=0&pagenumber=1&pagesize=25&compTypes=
  }

  return { state, init };
}

export function useSuperClone() {
  const state = reactive(factorySuperCloneState());
  return factorySuperClone(state);
}
