<template>
    <div>
        <AutoCompleteMatSimple  class="input-field"
                                :data="eventGroups"
                               :default-user-input="defaultValue"
                               :place-holder="'Enter group name'"
                               :max-length="30"
                               label-prop="name"
                               v-on:onUserInputChanged="onUserInputChanged"
                               v-on:onSelected="onSelected">
        </AutoCompleteMatSimple>
    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {ICompEvent} from "../../compevent/compevent-models";
    import * as R from "ramda";
    import {IAutoCompleteValue} from "../../common/ui/autocomplete/auto-complete-mat-models";
    import { IBase } from "../../common/common-models";
    import AutoCompleteMatSimple from "../../common/ui/autocomplete/auto-complete-mat-simple.vue";
    import {BuilderService} from "../builder-service"

    @Component({
        name: "event-group-type-ahead",
        components: {
            AutoCompleteMatSimple
        }
    })
    export default class EventGroupTypeAhead extends Vue {

        @Prop({default: ""}) public readonly defaultValue: string;
        @Prop({
            default: () => {
                return [];
            }
        }) public readonly compEvents: ICompEvent[];

        public builderService: BuilderService = new BuilderService();
        public eventGroups: IBase[] = [];

        public created() {
            this.setEventGroupNames();
        }

        @Watch("compEvents")
        public onCompEventsChanged(newValue: ICompEvent[]) {
            this.eventGroups = R.clone(this.getEventGroupUniqueNames(newValue));
        }

        public setEventGroupNames() {
            this.eventGroups = R.clone(this.getEventGroupUniqueNames(this.compEvents));
        }

        public getEventGroupUniqueNames(compEvents: ICompEvent[]): IBase[] {
            return this.builderService.getEventGroupUniqueNames(compEvents);
        }

        public searchTermChanged(searchTerm: string) {
            console.log("searchTermChanged: " + searchTerm);
        }

        public onUserInputChanged(searchTerm: string) {
            this.$emit("onUserInputChanged", searchTerm);
        }

        public onSelected(autoCompleteValue: IAutoCompleteValue): void {
            this.$emit("onSelected", autoCompleteValue);
        }
    }

</script>
