<template>
  <div class="e4s-flex-column">
    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2 form-label="Level">
          <select
            slot="field"
            v-model="entityLevelSelected"
            @change="selectedEntity"
            class="browser-default e4s-input-field e4s-input-field--primary"
          >
            <option :value="entityLevel" v-for="entityLevel in getEntityLevels">
              <span v-text="entityLevel.entityLevel"></span> -
              <span v-text="entityLevel.entityName"></span>
            </option>
          </select>
        </FormGenericInputTemplateV2>

        <FormGenericInputTemplateV2 form-label="Search Approvers">
          <UserTypeAheadV2
            slot="field"
            :reset-input-on-selected="true"
            @input="onUserSearchFound"
          />
        </FormGenericInputTemplateV2>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2
          form-label="Club\School"
          v-if="selfServiceInternal.entityLevel === 1"
        >
          <select
            slot="field"
            id="club-type"
            v-model="selfServiceInternal.clubType"
            class="browser-default e4s-input-field e4s-input-field--primary"
          >
            <option value="C">Club</option>
            <option value="S">School</option>
          </select>
        </FormGenericInputTemplateV2>

        <span></span>
      </template>
    </FormGenericFieldGridV2>

    <div
      v-if="selfServiceInternal.approvalUsers.length > 0"
      class="e4s-flex-column e4s-gap--large"
    >
      <SectionThickLine />
      <div
        id="approvers"
        v-for="approver in selfServiceInternal.approvalUsers"
        class="e4s-flex-row e4s-gap--large e4s-justify-flex-row-vert-center"
      >
        <ButtonGenericV2
          text="X"
          v-on:click="removeApprover(approver.id)"
          button-type="destructive"
          style="width: 35px"
        />
        <span v-text="approver.email"></span> -
        <span v-text="approver.displayName"></span>
      </div>
    </div>

    <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
      <ButtonGenericV2
        text="Cancel"
        button-type="tertiary"
        v-on:click="cancel"
      />
      <ButtonGenericV2 text="Finish" v-on:click="submit" />
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Component from "vue-class-component";
import Vue from "vue";
import { Prop, Watch } from "vue-property-decorator";
import { BuilderService } from "../../builder-service";
import { ISelfService } from "../../builder-models";
import UserSearchTypeAhead from "../../../admin/user/user-search-type-ahead.vue";
import { IUserSummary } from "../../../admin/user/user-models";
import { ENTITY_TABLE, ENTITY_TYPE } from "../../../config/config-app-models";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FormGenericFieldGridV2 from "../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import UserTypeAheadV2 from "../../../admin/user/v2/user-type-ahead-v2.vue";
import SectionThickLine from "../../../common/ui/layoutV2/form/SectionThickLine.vue";
import { simpleClone } from "../../../common/common-service-utils";

const builderService: BuilderService = new BuilderService();

@Component({
  name: "self-service-form",
  components: {
    SectionThickLine,
    UserTypeAheadV2,
    FormGenericInputTemplateV2,
    FormGenericFieldGridV2,
    ButtonGenericV2,
    UserSearchTypeAhead,
  },
})
export default class SelfServiceForm extends Vue {
  @Prop({
    default: () => {
      return builderService.factorySelfService();
    },
  })
  public readonly selfService: ISelfService;

  public selfServiceInternal: ISelfService =
    builderService.factorySelfService();
  public entityLevelSelected = {
    entityLevel: 0,
    entityName: "",
  };

  public mounted() {
    this.selfServiceInternal = simpleClone(this.selfService);
  }

  @Watch("selfService")
  public onSelfServiceChanged(newwValue: ISelfService) {
    this.selfServiceInternal = simpleClone(this.selfService);
  }

  public get getEntityLevels() {
    return ENTITY_TABLE;
  }

  public selectedEntity() {
    this.selfServiceInternal.entityLevel = this.entityLevelSelected.entityLevel;
    this.selfServiceInternal.entityName = this.entityLevelSelected
      .entityName as ENTITY_TYPE;
  }

  public onUserSearchFound(userSummary: IUserSummary) {
    if (R.isNil(userSummary) || userSummary.id === 0) {
      return;
    }
    this.selfServiceInternal.approvalUsers.push(userSummary);
  }

  public removeApprover(id: number) {
    this.selfServiceInternal.approvalUsers =
      this.selfServiceInternal.approvalUsers.filter((user) => user.id !== id);
  }

  public cancel() {
    this.$emit("onCancel");
  }

  public submit() {
    this.$emit("onSubmit", simpleClone(this.selfServiceInternal));
  }
}
</script>
