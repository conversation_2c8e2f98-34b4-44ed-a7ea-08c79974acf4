<template>
  <CommonTypeAhead
    slot="default"
    :default-value="valueInternal"
    :data-function="getEventGroupsForTypeAhead"
    :get-object-description="getOptionDisplayValue"
    :show-cancel-button="false"
    :is-modal="isModal"
    :is-disabled="isDisabled"
    :use-click-outside-reset="false"
    @searchTermChanged="onUserInputChanged"
    @selected="onSelected"
    @reset="reset"
  >
    <div slot-scope="{ result }">
      <div v-text="getOptionDisplayValue(result)"></div>
    </div>
  </CommonTypeAhead>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { ICompEvent } from "../../compevent/compevent-models";
import * as R from "ramda";
import { IBase, IServerResponseList } from "../../common/common-models";
import AutoCompleteMatSimple from "../../common/ui/autocomplete/auto-complete-mat-simple.vue";
import { BuilderService } from "../builder-service";
import CommonTypeAhead from "../../common/ui/type-ahead/common-type-ahead.vue";
@Component({
  name: "event-group-type-ahead-v2",
  components: {
    CommonTypeAhead,
    AutoCompleteMatSimple,
  },
})
export default class EventGroupTypeAhead extends Vue {
  @Prop({ default: "" })
  public readonly defaultValue: string;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly compEvents: ICompEvent[];

  @Prop({ default: false })
  public readonly isModal: boolean;

  @Prop({ default: false })
  public readonly isDisabled: boolean;

  public builderService: BuilderService = new BuilderService();
  public eventGroups: IBase[] = [];

  public valueInternal: string = this.defaultValue;

  public created() {
    this.setEventGroupNames();
  }

  @Watch("compEvents")
  public onCompEventsChanged(newValue: ICompEvent[]) {
    this.eventGroups = R.clone(this.getEventGroupUniqueNames(newValue));
  }

  @Watch("defaultValue")
  public onDefaultValueChanged(newValue: string) {
    if (newValue !== this.valueInternal) {
      this.valueInternal = newValue;
    }
  }

  public setEventGroupNames() {
    this.eventGroups = R.clone(this.getEventGroupUniqueNames(this.compEvents));
  }

  public getEventGroupUniqueNames(compEvents: ICompEvent[]): IBase[] {
    return this.builderService.getEventGroupUniqueNames(compEvents);
  }

  public searchTermChanged(searchTerm: string) {
    console.log("searchTermChanged: " + searchTerm);
  }

  public onUserInputChanged(searchTerm: string) {
    this.$emit("onUserInputChanged", searchTerm);
  }

  public getEventGroupsForTypeAhead(
    searchTerm: string
  ): Promise<IServerResponseList<IBase>> {
    function filterBySearchTerm(eventGroup: IBase): boolean {
      //  filter groups if the search term matches the characters at the start of the group name
      //  and use javascript slice.  Our project can't use startsWith, includes.
      return (
        eventGroup.name!.toLowerCase().slice(0, searchTerm.length) ===
        searchTerm.toLowerCase()
      );

      // return eventGroup.name!.toLowerCase().includes(searchTerm.toLowerCase());
    }

    const serverResponseList: IServerResponseList<IBase> = {
      errNo: 0,
      error: "",
      data:
        searchTerm.length === 0
          ? this.eventGroups
          : this.eventGroups.filter(filterBySearchTerm),
    };
    return Promise.resolve(serverResponseList);
  }

  public getOptionDisplayValue(base: IBase): string {
    return base.name!;
  }

  public onSelected(base: IBase): void {
    this.valueInternal = base.name!;
    this.$emit("onSelected", base.name);
  }

  public reset() {
    this.valueInternal = "";
    this.$emit("input", this.valueInternal);
  }
}
</script>
