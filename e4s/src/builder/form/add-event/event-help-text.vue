<template>
    <div>
        <div class="row">

            <div class="input-field col s10 m10 l10">
                <input
                        :id="PREFIX + 'help-text'"
                        :name="PREFIX + 'help-text'"
                        type="text"
                        v-model="compEventInternal.options.helpText"
                        v-on:keyup="formController.processChangesDebounce()"
                        placeholder="Enter any text that may further explain event">
                <label class="active" :for="PREFIX + 'help-text'">
                    <FormControllerLabelButtons labelText="Help text"
                                                propPath="options.helpText"
                                                :showForce="showInputForce"
                                                :showReset="showInputReset"
                                                :formController="formController">
                    </FormControllerLabelButtons>
                </label>
            </div>

            <div class="input-field col s12 m12 l2">
                <p>
                    <label>
                        <input class="e4s-checkbox"
                               type="checkbox"
                               v-on:change="onSelectRadioChanged"
                               v-model="compEventInternal.options.rowOptions.autoExpandHelpText" />
                        <span>
                            <FormControllerLabelButtons labelText="Auto Show"
                                                        propPath="options.rowOptions.autoExpandHelpText"
                                                        :showForce="showInputForce"
                                                        :showReset="showInputReset"
                                                        :formController="formController">
                            </FormControllerLabelButtons>
                        </span>
                    </label>
                </p>
            </div>

        </div>


        <div class="row">

            <div class="input-field col s6 m6 l6">
                <input
                        :id="PREFIX + 'xi-text'"
                        :name="PREFIX + 'xi-text'"
                        type="text"
                        v-model="compEventInternal.options.xiText"
                        v-on:keyup="formController.processChangesDebounce()"
                        placeholder="Enter any text">
                <label class="active" :for="PREFIX + 'xi-text'">
                    <FormControllerLabelButtons labelText="Extra event text schedule(xiText)"
                                                propPath="options.xiText"
                                                :showForce="showInputForce"
                                                :showReset="showInputReset"
                                                :formController="formController">
                    </FormControllerLabelButtons>
                </label>
            </div>

            <div class="input-field col s6 m6 l6">
                <input
                        :id="PREFIX + 'xr-text'"
                        :name="PREFIX + 'xr-text'"
                        type="text"
                        v-model="compEventInternal.options.xrText"
                        v-on:keyup="formController.processChangesDebounce()"
                        placeholder="Enter any text">
                <label class="active" :for="PREFIX + 'xe-text'">
                    <FormControllerLabelButtons labelText="Extra event text schedule(xrText)"
                                                propPath="options.xrText"
                                                :showForce="showInputForce"
                                                :showReset="showInputReset"
                                                :formController="formController">
                    </FormControllerLabelButtons>
                </label>
            </div>

        </div>

        <div class="row">

            <div class="input-field col s6 m6 l6">
                <input
                        :id="PREFIX + 'xb-text'"
                        :name="PREFIX + 'xb-text'"
                        type="text"
                        v-model="compEventInternal.options.xbText"
                        v-on:keyup="formController.processChangesDebounce()"
                        placeholder="Enter any text">
                <label class="active" :for="PREFIX + 'xb-text'">
                    <FormControllerLabelButtons labelText="Extra event text schedule(xbText)"
                                                propPath="options.xbText"
                                                :showForce="showInputForce"
                                                :showReset="showInputReset"
                                                :formController="formController">
                    </FormControllerLabelButtons>
                </label>
            </div>

            <div class="input-field col s6 m6 l6">
                <input
                        :id="PREFIX + 'xe-text'"
                        :name="PREFIX + 'xe-text'"
                        type="text"
                        v-model="compEventInternal.options.xeText"
                        v-on:keyup="formController.processChangesDebounce()"
                        placeholder="Enter any text">
                <label class="active" :for="PREFIX + 'xe-text'">
                    <FormControllerLabelButtons labelText="Extra event text basket(xeText)"
                                                propPath="options.xeText"
                                                :showForce="showInputForce"
                                                :showReset="showInputReset"
                                                :formController="formController">
                    </FormControllerLabelButtons>
                </label>
            </div>

        </div>
    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {ICompEvent} from "../../../compevent/compevent-models";
    import {CompEventService} from "../../../compevent/compevent-service";
    import { Prop, Watch } from "vue-property-decorator";
    import {FormController} from "../../../common/ui/form/form-controller/form-controller";
    import FormControllerLabelButtons from "../../../common/ui/form/form-controller/form-controller-label-buttons.vue";
    const compEventService: CompEventService = new CompEventService();

    @Component({
        name: "event-help-text",
        components: {
            FormControllerLabelButtons
        }
    })
    export default class EventHelpText extends Vue {
        @Prop({
            default: () => {
                return compEventService.factoryGetCompEvent();
            }
        }) public readonly compEvent: ICompEvent;
        @Prop({default: false}) public readonly showInputForce: boolean;
        @Prop({default: false}) public readonly showInputReset: boolean;


        public PREFIX = Math.random().toString(36).substring(2);
        public compEventInternal: ICompEvent = compEventService.factoryGetCompEvent();

        public formController: FormController = new FormController(this.compEvent, this.compEventInternal);

        public created() {
            this.compEventInternal = R.clone(this.compEvent);
            this.formController.setSources(this.compEvent, this.compEventInternal);
        }

        @Watch("compEvent")
        public onCompEventChanged(newValue: ICompEvent) {
            this.compEventInternal = R.clone(newValue);
            this.formController.setSources(this.compEvent, this.compEventInternal);
        }

        @Watch("formController.objDiffs")
        public formControllerObjDiffsChanged(newValue: any) {
            console.log("formControllerObjDiffsChanged", newValue);
            this.fieldChanged();
        }

        public onSelectRadioChanged() {
            this.formController.processChanges();
            this.fieldChanged();
        }

        public fieldChanged() {
            this.$emit("objDiffs", this.formController.objDiffs);
            this.$emit("onInputChange", R.clone(this.compEventInternal));

        }

        // public forcePropDiff() {
        //
        // }

    }
</script>
