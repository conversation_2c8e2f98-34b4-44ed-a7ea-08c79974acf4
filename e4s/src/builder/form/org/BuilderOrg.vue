<template>
  <div class="e4s-flex-column">
    <div
      v-if="builderOrgController.configController.isAdmin.value"
      class="e4s-admin--section"
    >
      <div class="e4s-flex-row e4s-gap--large">
        <FieldRadioV2
          option-value="select-org"
          v-model="builderOrgController.state.showSection"
          label="Select Organisation"
        />
        <FieldRadioV2
          option-value="create-new-org"
          v-model="builderOrgController.state.showSection"
          label="Create New Organisation"
        />
      </div>
    </div>

    <div v-if="builderOrgController.state.showSection === 'select-org'">
      <div
        class="e4s-flex-column e4s-admin--section"
        v-if="builderOrgController.configController.isAdmin.value"
      >
        <FormGenericInputTemplateV2 form-label="Select Organisation (Admin)">
          <template slot="field">
            <div
              class="
                e4s-flex-row e4s-justify-flex-row-vert-center
                e4s-gap--standard
              "
            >
              <!--              <OnboardingOrgPicker-->
              <!--                @input="builderOrgController.onOrgSelected"-->
              <!--              />-->
              <OnboardingOrgPicker
                @input="builderOrgController.onOrgSelectedAndClose"
              />
              <!--              <ButtonGenericV2-->
              <!--                text="XXX"-->
              <!--                class="e4s-flex-row&#45;&#45;end"-->
              <!--                v-on:click="builderOrgController.showCreateNewOrg"-->
              <!--              />-->
            </div>
          </template>
        </FormGenericInputTemplateV2>
      </div>

      <!--      <FormGenericInputTemplateV2-->
      <!--        :form-label="builderOrgController.getOrganisationTitle.value"-->
      <!--      >-->
      <!--        <template slot="field">-->
      <!--          <div-->
      <!--            class="-->
      <!--              e4s-flex-row-->
      <!--              e4s-gap&#45;&#45;standard-->
      <!--              e4s-justify-flex-row-vert-center-->
      <!--            "-->
      <!--          >-->
      <!--            <FieldSelectV2-->
      <!--              :disabled="builderOrgController.state.orgsAvailable.length === 0"-->
      <!--              class="e4s-flex-grow"-->
      <!--              :value="builderOrgController.state.orgsAvailableSelected"-->
      <!--              :data-array="builderOrgController.state.orgsAvailable"-->
      <!--              debug-console="BuilderOrg Org Select"-->
      <!--              @input="builderOrgController.onOrgSelected"-->
      <!--            />-->
      <!--            <ButtonGenericV2-->
      <!--              text="New"-->
      <!--              class="e4s-flex-row&#45;&#45;end"-->
      <!--              @click="builderOrgController.showCreateNewOrg()"-->
      <!--            />-->
      <!--          </div>-->
      <!--        </template>-->
      <!--      </FormGenericInputTemplateV2>-->

      <!--      v-if="builderOrgController.state.orgSelected.status === 'pending'"-->
      <div
        class="
          e4s-flex-column
          dat-info--error dat-info--pad-small
          builder-org--section
        "
        v-if="builderOrgController.showRequiresApprovalMessage.value"
      >
        This organisation is awaiting approval. Any competitions set up cannot
        be set to "Active" until approved.

        <div
          class="e4s-flex-column e4s-gap--large"
          v-if="
            builderOrgController.configController.getStore.value.configApp
              .options.useStripeConnect
          "
        >
          <div>
            Please proceed to <MyAccountLink /> to complete the Stripe Connect
            setup.
          </div>

          <div>
            <FieldHelp
              title="What is Stripe Connect?"
              :show-title-in-link="true"
              help-key="stripe-connect"
              :get-from-server="true"
            />
            Stripe Connect will transfer funds from your athlete entries direct
            to your bank account.
          </div>
        </div>
      </div>

      <!--      isStripeSetupRequired{{ isStripeSetupRequired }}-->
    </div>
    <!--    configController.getStore.value.configApp.options.useStripeConnect-->
    <div v-if="builderOrgController.state.showSection === 'create-new-org'">
      <OnboardingOrgEdit
        :use-stripe-connect="
          builderOrgController.configController.getStore.value.configApp.options
            .useStripeConnect
        "
        :value="builderOrgController.state.onboardingOrganisation"
        @input="builderOrgController.submitNewOrg"
        @cancel="builderOrgController.cancelCreateNewOrg"
      />
    </div>
    <LoadingSpinnerV2 v-if="builderOrgController.state.isLoading" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IOrg } from "../../../org/org-models";
import { IBaseConcrete } from "../../../common/common-models";
import { useBuilderOrgController } from "./useBuilderOrgController";
import { OrgService } from "../../../org/org-service";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import OnboardingOrgPicker from "../../../org/onboarding/org-picker/OnboardingOrgPicker.vue";
import FieldSelectV2 from "../../../common/ui/layoutV2/fields/field-select-v2.vue";
import OnboardingOrgEdit from "../../../org/onboarding/form/org-edit/OnboardingOrgEdit.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import FormGenericFieldGridV2 from "../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import MyAccountLink from "../../../common/ui/layoutV2/links/MyAccountLink.vue";
import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
import { BuilderOrgShowSection } from "./builder-org-models";
import FieldRadioV2 from "../../../common/ui/layoutV2/fields/field-radio-v2.vue";

export default defineComponent({
  name: "BuilderOrg",
  components: {
    FieldRadioV2,
    FieldHelp,
    MyAccountLink,
    FormGenericInputTemplateV2,
    FormGenericFieldGridV2,
    LoadingSpinnerV2,
    OnboardingOrgEdit,
    FieldSelectV2,
    OnboardingOrgPicker,
    ButtonGenericV2,
  },
  props: {
    value: {
      type: Object as PropType<IOrg>,
      default: () => {
        const org: IOrg = new OrgService().factory();
        return org;
      },
    },
    orgs: {
      type: Array as PropType<IBaseConcrete[]>,
      default: () => [],
    },
    showSectionDefault: {
      type: String as PropType<BuilderOrgShowSection>,
      default: "select-org",
    },
  },
  setup(
    props: {
      value: IOrg;
      orgs: IBaseConcrete[];
      showSectionDefault: BuilderOrgShowSection;
    },
    context: SetupContext
  ) {
    const builderOrgController = useBuilderOrgController(
      { showSection: props.showSectionDefault },
      context
    );

    watch(
      () => props.value,
      (newValue: IOrg) => {
        builderOrgController.initOrgSelected(newValue);
      },
      {
        immediate: true,
      }
    );

    watch(
      () => props.showSectionDefault,
      (newValue: BuilderOrgShowSection) => {
        builderOrgController.state.showSection = newValue;
      },
      {
        immediate: true,
      }
    );

    return { builderOrgController };
  },
});
</script>

<style scoped>
.builder-org--section {
  margin: var(--e4s-gap--large) 0;
  border-width: 2px;
  border-style: solid;
  border-radius: var(--e4s-gap--small);
}
</style>
