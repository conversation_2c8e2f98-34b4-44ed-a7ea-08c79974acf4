import { IOrg, IOrgWithLocations } from "../../../org/org-models";
import { ILocation } from "../../../location/location-models";
import { IOnboardingOrganisation } from "../../../org/onboarding/form/onboarding-form-models";

export type BuilderOrgShowSection = "select-org" | "create-new-org";

export interface IBuilderOrgConfig {
  showSection: BuilderOrgShowSection;
}

export interface IBuilderOrgState {
  setUpForUser: {
    userId: number;
  };
  showSection: BuilderOrgShowSection;
  isLoading: boolean;
  orgSelected: IOrg;
  orgSelectedLocations: ILocation[];
  orgsAvailable: IOrgWithLocations[];
  orgsAvailableSelected: IOrgWithLocations;
  onboardingOrganisation: IOnboardingOrganisation;
}
