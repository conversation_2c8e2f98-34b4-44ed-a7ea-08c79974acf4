import { IsoDate, IsoIshDateTime } from "../../common/common-models";

export interface IEventMasterAthlete {
  system: "IRL";
  status: "ACTIVE" | "INACTIVE";
  dob: IsoDate;
  clubId: number;
  clubName: string;
  county: string;
  region: string;
  club2Id: number;
  club2Name: string;
  firstName: string;
  surName: string;
  gender: "M" | "F";
  lastUpdated: IsoIshDateTime;
  urn: number;
  activeEndDate: IsoDate;
}
