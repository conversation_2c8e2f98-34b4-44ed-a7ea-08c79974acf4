import {messageDispatchHelper} from "../user-message/user-message-store"
import {USER_MESSAGE_LEVEL} from "../user-message/user-message-models"
import {IAthlete} from "./athlete-models"
import * as R from "ramda"
import {AthleteDataService} from "./athlete-data-service"
import {AthleteService} from "./athlete-service"

export class AthleteController {
    public isLoading: boolean = false;

    public athleteDataService: AthleteDataService = new AthleteDataService();
    public athleteService: AthleteService = new AthleteService();

    public loadAthleteById(id: number): Promise<IAthlete> {
        let athlete: IAthlete = this.athleteService.factoryGetAthlete();
        this.isLoading = true;
        return this.athleteDataService.read(id)
            .then((response) => {
                if (response.errNo > 0) {
                    messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                    return athlete;
                }
                athlete = response.data;
                athlete.activeEndDate = R.isNil(athlete.activeEndDate) || athlete.activeEndDate === "0000-00-00" ?
                    "" :
                    athlete.activeEndDate;
                return athlete;
            })
            .catch((error) => {
                messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                return athlete;
            })
            .finally(() => {
                this.isLoading = false;
                return athlete;
            });
    }
}
