<template>
  <div class="e4s-flex-row e4s-gap--standard e4s-flex-center">
    <DateInputDropDownV2
      :value="value"
      :default-year="defaultYear"
      v-on:input="onSelected"
      :allow-null-reset="true"
      :restrict-to-years="compAgeGroupYears"
      :is-disabled="isDisabled"
    />

    <a
      href="#"
      v-if="showYearToggle"
      v-on:click.prevent="toggleAgeGroupYears"
      v-text="compAgeGroupYears.length === 0 ? 'Comp Years' : 'All Years'"
    ></a>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IsoDate } from "../../../common/common-models";
import { ICompetitionBase } from "../../../competition/competition-models";
import { factoryCompetitionBase } from "../../../competition/v2/competiton-service-v2";
import { getYearsFromCompAgeGroup } from "../athlete-service-v2";
import DateInputDropDownV2 from "../../../common/ui/layoutV2/fields/date-input-drop-down-v2.vue";

export type YearsType = "ALL" | "COMP";

export default defineComponent({
  name: "DateOfBirthV2",
  components: { DateInputDropDownV2 },
  props: {
    value: {
      type: String as PropType<IsoDate>,
      required: true,
    },
    defaultYear: {
      type: [Number, String],
      default: "",
    },
    competitionBase: {
      type: Object as PropType<ICompetitionBase>,
      default: () => {
        return factoryCompetitionBase();
      },
    },
    showCompYears: {
      type: String as PropType<YearsType>,
      default: "ALL",
    },
    allowCompYearToggle: {
      type: Boolean,
      default: false,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: {
      dob: IsoDate;
      defaultYear: number | "";
      competitionBase: ICompetitionBase;
      showCompYears: YearsType;
      allowCompYearToggle: boolean;
      isDisabled: boolean;
    },
    context: SetupContext
  ) {
    const restrictToYears = ref<number[]>([]);
    const showYearsType = ref<YearsType>(props.showCompYears);
    const dobSelected = ref("");

    // toggleAgeGroupYears();

    function onSelected(dobIso: string) {
      dobSelected.value = dobIso;
      context.emit("input", dobIso);
      context.emit("change", dobIso);
    }

    function toggleAgeGroupYears() {
      if (props.competitionBase.compId > 0) {
        if (showYearsType.value === "ALL") {
          showYearsType.value = "COMP";
        } else {
          showYearsType.value = "ALL";
        }
        setYears();
      }
    }

    function setYears() {
      if (props.competitionBase.compId > 0) {
        if (showYearsType.value === "ALL") {
          restrictToYears.value = [];
        } else {
          restrictToYears.value = getYearsFromCompAgeGroup(
            props.competitionBase.compAgeGroups
          );
        }
      }
    }

    watch(
      () => props.competitionBase,
      (newValue: ICompetitionBase) => {
        toggleAgeGroupYears();
      },
      {
        immediate: true,
      }
    );

    watch(
      () => props.showCompYears,
      (newValue: YearsType) => {
        showYearsType.value = newValue;
        setYears();
      },
      {
        immediate: true,
      }
    );

    const hasCompetitionBase = computed(() => {
      return props.competitionBase.compId > 0;
    });

    const showYearToggle = computed(() => {
      return hasCompetitionBase.value && props.allowCompYearToggle;
    });

    return {
      compAgeGroupYears: restrictToYears,
      onSelected,
      toggleAgeGroupYears,
      hasCompetitionBase,
      showYearToggle,
    };
  },
});
</script>
