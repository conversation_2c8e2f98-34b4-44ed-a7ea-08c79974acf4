<template>
  <div class="e4s-flex-column e4s-gap--large">
    <!--    <AthleteUrnSearchSimpleV2 :default-ao="defaultAo"/>-->

    <AthleteFormSimple
      v-if="isSimpleRegistration"
      v-show="showAthleteForm"
      :athlete="athleteReturnedFromUrnSearch"
      :default-ao="defaultAo"
      :aos="aos"
      :is-admin="configController.isAdmin.value"
      :show-cancel-button="true"
      :competition-base="competitionBase"
      :initial-question="initialQuestion"
      @onSubmit="onClose"
      @onCancel="onClose"
    />

    <CardGenericV2
      class="e4s-card--well e4s-gap--standard"
      v-if="!isSimpleRegistration"
    >
      <div slot="all">
        <AthleteInitialQuestion
          :show-cancel-button="showCancelButton"
          @onChanged="setInitialQuestion"
          @onCancel="onClose"
        />
      </div>
    </CardGenericV2>

    <template v-if="hasQuestionBeenAnswered && !isSimpleRegistration">
      <AthleteUrnSearchV2
        v-if="showUrlSearch"
        :default-ao="defaultAo"
        :aos="aos"
        :show-cancel-button="false"
        :competition-base="competitionBase"
        @onResponse="onAthleteResponse"
        @onCancel="onClose"
      />

      <!--      <div class="e4s-vertical-spacer&#45;&#45;large"></div>-->

      <AthleteFormV2
        v-show="showAthleteForm"
        :athlete="athleteReturnedFromUrnSearch"
        :default-ao="defaultAo"
        :aos="aos"
        :is-admin="configController.isAdmin.value"
        :show-cancel-button="isSimpleRegistration"
        :competition-base="competitionBase"
        :initial-question="initialQuestion"
        @onSubmit="onClose"
        @onCancel="onClose"
      >
        <!--        <span-->
        <!--          slot="cancel-button"-->
        <!--          v-if="!showCancelButton && !isSimpleRegistration"-->
        <!--        ></span>-->
      </AthleteFormV2>
    </template>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
} from "@vue/composition-api";
import {
  useConfigController,
  useConfigStore,
} from "../../../config/useConfigStore";
import { AthleteService } from "../../athlete-service";
import { IAthlete } from "../../athlete-models";
import AthleteUrnSearchV2 from "./athlete-urn-search-v2.vue";
import AthleteFormV2 from "./athlete-form-v2.vue";
import {
  ICompetition,
  ICompetitionBase,
} from "../../../competition/competition-models";
import { factoryCompetitionBase } from "../../../competition/v2/competiton-service-v2";
import AthleteInitialQuestion, {
  InitialQuestion,
} from "./AthleteInitialQuestion.vue";
import CardGenericV2 from "../../../common/ui/layoutV2/card-generic-v2.vue";
import AthleteFormSimple from "./athlete-form-simple.vue";

type ShowSectionType = "INITIAL_QUESTION" | "MAIN_FORM";

export default defineComponent({
  name: "athlete-create-form-v2",
  components: {
    AthleteFormSimple,
    CardGenericV2,
    AthleteInitialQuestion,
    AthleteFormV2,
    AthleteUrnSearchV2,
  },
  props: {
    showCancelButton: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    competitionBase: {
      type: Object as PropType<ICompetitionBase | ICompetition>,
      default: () => {
        return factoryCompetitionBase();
      },
    },
  },
  setup(
    props: {
      showCancelButton: boolean;
      competitionBase: ICompetitionBase | ICompetition;
    },
    context: SetupContext
  ) {
    const configStore = useConfigStore();
    const configController = useConfigController();

    const showSection = ref<ShowSectionType>("INITIAL_QUESTION");

    const initialQuestion = ref<InitialQuestion | "">("");
    const showUrlSearch = ref(true);

    const athleteService: AthleteService = new AthleteService();
    const athleteReturnedFromUrnSearch = ref(
      athleteService.factoryGetAthlete()
    );

    if ((props.competitionBase as ICompetition).options) {
      const competition = props.competitionBase as ICompetition;
      if (competition.options.simpleRegistration.enabled) {
        setInitialQuestion("UNREGISTERED");
      }
    }

    function onSubmitted() {
      console.warn("athlete-create-form onReload()");
    }

    function onClose(athlete: IAthlete | undefined) {
      console.warn("athlete-create-form onClose()");
      if (!athlete) {
        athlete = athleteService.factoryGetAthlete();
      }
      if (athlete.id === 0) {
        console.warn("athlete-create-form onClose() emit cancelled");
        context.emit("cancelled");
        return;
      }
      console.warn("athlete-create-form onClose() emit submitted");
      context.emit("submitted", athlete);
    }

    const defaultAo = computed(() => {
      return configStore.configApp.defaultao;
    });

    const aos = computed(() => {
      return configStore.configApp.aos;
    });

    function onAthleteResponse(athlete: IAthlete) {
      console.warn("athlete-create-form onAthleteResponse()");
      athleteReturnedFromUrnSearch.value = R.clone(athlete);
    }

    function setInitialQuestion(question: InitialQuestion) {
      console.log("athlete-create-form setInitialQuestion", initialQuestion);
      athleteReturnedFromUrnSearch.value = athleteService.factoryGetAthlete();
      initialQuestion.value = question;
      showSection.value = "MAIN_FORM";
      showUrlSearch.value = question === "REGISTERED";
    }

    const hasQuestionBeenAnswered = computed(() => {
      return initialQuestion.value !== "";
    });

    const showAthleteForm = computed(() => {
      if (initialQuestion.value !== "REGISTERED") {
        return true;
      }
      return athleteReturnedFromUrnSearch.value.id > 0;
    });

    const isSimpleRegistration = computed(() => {
      if ((props.competitionBase as ICompetition).options) {
        const competition = props.competitionBase as ICompetition;
        return competition.options.simpleRegistration.enabled;
      }

      return false;
    });

    return {
      configController,
      defaultAo,
      aos,
      athleteReturnedFromUrnSearch,
      onSubmitted,
      onClose,
      onAthleteResponse,
      showSection,
      setInitialQuestion,
      showUrlSearch,
      hasQuestionBeenAnswered,
      initialQuestion,
      showAthleteForm,
      isSimpleRegistration,
    };
  },
});
</script>
