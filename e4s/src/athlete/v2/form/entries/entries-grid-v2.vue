<template>
  <CardGenericV2>
    <template slot="all">
      <div class="e4s-flex-column e4s-gap--small">
        <div class="e4s-flex-row e4s-justify-flex-space-between e4s-flex-start">
          <div class="e4s-flex-column e4s-gap--small">
            <div v-text="getEventDate" class="e4s-subheader--200"></div>
            <div class="e4s-subheader--200">
              <EntryHrefLink
                :comp-id="athleteEntriesComp.compId"
                :link-text="
                  athleteEntriesComp.compId + ': ' + athleteEntriesComp.compName
                "
              />
            </div>
          </div>

          <PillV2
            :text="(isEntryOpen ? 'Open' : 'Closed') + ' Competition'"
            :pill-type="isEntryOpen ? 'success' : 'error'"
          />
        </div>

        <details>
          <summary>
            <span
              v-text="
                athleteEntriesComp.athleteEvents.length +
                ' entr' +
                (athleteEntriesComp.athleteEvents.length === 1 ? 'y' : 'ies') +
                '.'
              "
              class="e4s-subheader--200"
            ></span>
          </summary>

          <div class="e4s-flex-column">
            <EntriesGridRowV2
              style="padding: 4px 0"
              class="e4s-repeatable-grid--top"
              v-for="athleteEvent in athleteEntriesComp.athleteEvents"
              :key="athleteEvent.entryid"
              :athlete-event="athleteEvent"
              :is-admin="isAdmin"
              v-on:selectedOptions="selectedOptions"
            />
          </div>
        </details>
      </div>
    </template>
  </CardGenericV2>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue";
import { useConfigStore } from "../../../../config/useConfigStore";
import ButtonGroupSectionRightV2 from "../../../../common/ui/layoutV2/buttons/button-group-section-right-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FormGenericSectionTitleV2 from "../../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import { IAthlete } from "../../../athlete-models";
import { AthleteService } from "../../../athlete-service";
import EntriesGridRowV2 from "./entries-grid-row-v2.vue";
import { IAthleteEntriesComp } from "./entries-models";
import { eventDateDisplay } from "../../../../common/common-service-utils";
import { IAthleteEvent } from "../../../athlete-event/athlete-event-models";
import { factoryAthleteEntriesComp } from "./entries-grid-service";
import EntryHrefLink from "../../../../common/ui/layoutV2/href/entry-href-link.vue";
import PillV2 from "../../../../common/ui/layoutV2/pills/pill-v2.vue";
import { isBefore } from "date-fns";

const athleteService = new AthleteService();

export default defineComponent({
  name: "entries-grid-v2",
  components: {
    PillV2,
    EntryHrefLink,
    EntriesGridRowV2,
    FormGenericSectionTitleV2,
    ButtonGenericV2,
    ButtonGroupSectionRightV2,
    CardGenericV2,
  },
  props: {
    athlete: {
      type: Object as PropType<IAthlete>,
      default: () => {
        return athleteService.factoryGetAthlete();
      },
    },
    athleteEntriesComp: {
      type: Object as PropType<IAthleteEntriesComp>,
      default: () => {
        return factoryAthleteEntriesComp();
      },
    },
    isAdmin: {
      type: Boolean,
      default: function () {
        return false;
      },
    },
  },
  setup(
    props: {
      athlete: IAthlete;
      athleteEntriesComp: IAthleteEntriesComp;
      isAdmin: boolean;
    },
    context: SetupContext
  ) {
    const configStore = useConfigStore();

    const getEventDate = computed(() => {
      return eventDateDisplay(props.athleteEntriesComp.isoDatetime);
    });

    function cancel() {
      context.emit("cancel");
    }

    function selectedOptions(athleteEvent: IAthleteEvent) {
      context.emit("selectedOptions", athleteEvent);
    }

    const isEntryOpen = computed(() => {
      return isBefore(new Date(), props.athleteEntriesComp.isoDatetime);
    });

    return { cancel, configStore, getEventDate, selectedOptions, isEntryOpen };
  },
});
</script>
