<template>
  <div class="e4s-flex-column e4s-gap--large">
    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputNumberV2
          form-label="Minimum Athletes"
          help-text="Min to form a team"
          v-model="eventTeam.min"
          @change="onInputChange"
        />
        <FormGenericInputNumberV2
          form-label="Maximum Athletes"
          help-text="Max in a team"
          v-model="eventTeam.max"
          @change="onInputChange"
        />
        <FormGenericInputNumberV2
          form-label="Minimum Target Age Group"
          help-text="If mixed age groups, min athletes from target age group"
          v-model="eventTeam.minTargetAgeGroupCount"
          @change="onInputChange"
        />
        <FormGenericInputNumberV2
          form-label="Maximum OtherAge Group"
          help-text="If mixed age groups, max athletes from other age groups"
          v-model="eventTeam.maxOtherAgeGroupCount"
          @change="onInputChange"
        />
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputNumberV2
          form-label="Maximum Event Teams"
          help-text="Max teams allowed in this event"
          v-model="eventTeam.maxEventTeams"
          @change="onInputChange"
        />
        <FormGenericInputTextV2
          form-label="Athlete Position Label"
          v-model="eventTeam.teamPositionLabel"
          @change="onInputChange"
        />

        <FormGenericInputTextV2
          form-label="Athlete Substitute Label"
          v-model="eventTeam.teamSubstituteLabel"
          @change="onInputChange"
        />

        <FormGenericInputTemplateV2 form-label="Athletes Same Club Only">
          <InputCheckboxV2
            slot="field"
            class="e4s-align-self-flex-start"
            v-model="eventTeam.singleClub"
            value-label="Enabled"
            @onChange="onInputChange"
          />
        </FormGenericInputTemplateV2>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2 form-label="Team Name Format">
          <FieldTextV2
            slot="field"
            v-model="eventTeam.teamNameFormat"
            @change="teamNameFormatChanged"
          />
          <p slot="below-field" class="e4s-subheader--general">
            Placeholders: {{ getTeamNameFormatTypes }}
          </p>
        </FormGenericInputTemplateV2>

        <FormGenericInputTemplateV2 form-label="Form Type">
          <select
            slot="field"
            class="browser-default e4s-input-field e4s-input-field--primary"
            v-model="eventTeam.formType"
            v-on:change="onSelectRadioChanged"
          >
            <option
              v-for="(option, index) in formSelectOptions"
              :id="PREFIX + 'form-type'"
              :key="index"
              :value="option.value"
            >
              <span v-text="option.label"></span>
            </option>
          </select>
        </FormGenericInputTemplateV2>
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2 form-label="Price per Team\Athlete">
          <select
            slot="field"
            class="browser-default e4s-input-field e4s-input-field--primary"
            v-model="eventTeam.price"
            v-on:change="onSelectRadioChanged"
          >
            <option
              v-for="(option, index) in getFormPriceOptions"
              :id="PREFIX + 'form-price'"
              :key="index"
              :value="option"
            >
              <span v-text="option"></span>
            </option>
          </select>
          <p slot="below-field" class="e4s-subheader--general">
            Leave blank for team price
          </p>
        </FormGenericInputTemplateV2>

        <div class="e4s-flex-column e4s-gap--standard e4s-justify-flex-start">
          <InputCheckboxV2
            v-model="eventTeam.disableTeamNameEdit"
            class="e4s-align-self-flex-start"
            value-label="Disable team name edit"
          />

          <InputCheckboxV2
            v-model="eventTeam.mustBeIndivEntered"
            class="e4s-align-self-flex-start"
            value-label="Athlete must be entered individually first"
          />
        </div>
      </template>
    </FormGenericFieldGridV2>

    <FormRowsForm
      v-if="getShowLeagueForm"
      :builder-competition="builderCompetition"
      :form-rows="getEventTeamRows"
      v-on:onChanged="onFormRowsChanged"
    >
    </FormRowsForm>

    <div class="e4s-flex-column" v-if="getShowLeagueForm">
      <div class="e4s-flex-row e4s-gap--standard">
        <FormGenericInputTextV2
          class="e4s-full-width"
          form-label="Other 1 Label"
          v-model="eventTeam.options.others.other1.label"
          @change="onInputChange"
        >
        </FormGenericInputTextV2>
        <FormGenericInputTextV2
          class="e4s-full-width"
          form-label="Other 2 Label"
          v-model="eventTeam.options.others.other2.label"
          @change="onInputChange"
        >
        </FormGenericInputTextV2>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { BUILDER_STORE_CONST } from "../builder/builder-store-constants";
import { mapState } from "vuex";
import {
  IEventTeam,
  IFormRow,
  ShowFormType,
  TEAM_PRICE_TYPE,
} from "../athletecompsched/athletecompsched-models";
import { ISelectOption } from "../common/common-models";
import { AthleteCompSchedService } from "../athletecompsched/athletecompsched-service";
import FormRowsForm from "../builder/form/form-rows-event/form-rows-form.vue";
import { IBuilderStoreState } from "../builder/builder-store";
import { FormController } from "../common/ui/form/form-controller/form-controller";
import FormControllerLabelButtons from "../common/ui/form/form-controller/form-controller-label-buttons.vue";
import { IBuilderCompetition } from "../builder/builder-models";
import FormGenericInputNumberV2 from "../common/ui/layoutV2/form/form-generic--input-number-v2.vue";
import FormGenericFieldGridV2 from "../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTextV2 from "../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import InputCheckboxV2 from "../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import FormGenericInputTemplateV2 from "../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldTextV2 from "../common/ui/layoutV2/fields/field-text-v2.vue";
import { simpleClone } from "../common/common-service-utils";

const athleteCompSchedService: AthleteCompSchedService =
  new AthleteCompSchedService();

@Component({
  name: "event-team-options",
  components: {
    FieldTextV2,
    FormGenericInputTemplateV2,
    InputCheckboxV2,
    FormGenericInputTextV2,
    FormGenericFieldGridV2,
    FormGenericInputNumberV2,
    FormRowsForm,
    FormControllerLabelButtons,
  },
  computed: {
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      builderCompetition: (state: IBuilderStoreState) =>
        state.builderCompetition,
    }),
  },
})
export default class EventTeamOptions extends Vue {
  public readonly builderCompetition: IBuilderCompetition;

  @Prop({
    default: () => {
      return athleteCompSchedService.factoryEventTeamCe();
    },
  })
  public eventTeamProp: IEventTeam;
  @Prop({ default: false }) public readonly showInputForce: boolean;
  @Prop({ default: false }) public readonly showInputReset: boolean;

  public PREFIX = Math.random().toString(36).substring(2);
  public eventTeam: IEventTeam = athleteCompSchedService.factoryEventTeamCe();

  public formController: FormController = new FormController(
    this.eventTeamProp,
    this.eventTeam
  );

  public formSelectOptions: Array<ISelectOption<ShowFormType>> = [
    {
      label: "Default",
      value: "DEFAULT",
    },
    {
      label: "School",
      value: "SCHOOL",
    },
    {
      label: "League",
      value: "LEAGUE",
    },
  ];

  // public teamNameFormatType: TEAM_NAME_FORMAT_TYPES;

  public created() {
    this.eventTeam = R.clone(this.eventTeamProp);
    this.formController.setSources(this.eventTeamProp, this.eventTeam);
  }

  @Watch("eventTeamProp")
  public onEventTeamPropChanged(newValue: IEventTeam, oldValue: IEventTeam) {
    if (R.equals(newValue, oldValue)) {
      console.log(
        "onEventTeamPropChanged - same value, returning",
        newValue,
        oldValue
      );
      return;
    }

    console.log("onEventTeamPropChanged", newValue, oldValue);

    const eventTeamInternal = R.clone(newValue);

    if (!eventTeamInternal.options) {
      eventTeamInternal.options =
        athleteCompSchedService.factoryEventTeamOptions();
    }

    this.eventTeam = eventTeamInternal;

    this.formController.setSources(this.eventTeamProp, this.eventTeam);
  }

  // @Watch("eventTeam", {deep: true})
  // public eventTeamChanged(newValue: any) {
  //     console.log("!!!!!!!!!!!eventTeamChanged", newValue);
  //     this.formController.processChangesDebounce();
  //     this.$emit("onInputChange", R.clone(this.eventTeam));
  // }

  @Watch("formController.liveObject")
  public formControllerLiveObjectChanged(newValue: IEventTeam) {
    console.log("formControllerLiveObjectChanged", newValue);
    this.eventTeam = newValue;
  }

  public onSelectRadioChanged() {
    this.formController.processChangesDebounce();
    this.onInputChange();
  }

  public onDisableTeamNameEditChanged() {
    if (this.eventTeam.disableTeamNameEdit) {
      this.eventTeam.teamNameFormat = this.getTeamNameFormatTypes;
    }
    this.onInputChange();
  }

  public get getTeamNameFormatTypes() {
    return athleteCompSchedService.getDefaultTeamNameFormat();
  }

  public resetTeamNameDefault() {
    this.eventTeam.teamNameFormat = this.getTeamNameFormatTypes;
    this.formController.processChangesDebounce();
    this.onInputChange();
  }

  public get getShowLeagueForm(): boolean {
    return this.eventTeam.formType === "LEAGUE";
  }

  public get getEventTeamRows() {
    return this.eventTeam && this.eventTeam.formRows
      ? this.eventTeam.formRows
      : [];
  }

  public get getFormPriceOptions(): TEAM_PRICE_TYPE[] {
    return ["", "ATHLETE"];
    // return [
    //     {
    //         label: "Team",
    //         value: ""
    //     },
    //     {
    //         label: "Athlete",
    //         value: "ATHLETE"
    //     }
    // ];
  }

  public teamNameFormatChanged() {
    if (
      this.eventTeam.teamNameFormat === "" &&
      this.eventTeam.disableTeamNameEdit
    ) {
      // the format field is blank, the user won't be able to enter anything...team name will be blank!
      this.eventTeam.disableTeamNameEdit = false;
    }
    this.formController.processChangesDebounce();
    this.onInputChange();
  }

  public onFormRowsChanged(formRows: IFormRow[]) {
    this.eventTeam.formRows = R.clone(formRows);
    this.formController.processChangesDebounce();
    this.onInputChange();
  }

  @Watch("formController.objDiffs")
  public formControllerObjDiffsChanged(newValue: any) {
    console.log("formControllerObjDiffsChanged", newValue);
    this.$emit("objectDelta", this.formController.objDiffs);
    this.$emit("objDiffs", this.formController.objDiffs);
    this.onInputChange();
  }

  public onInputChange() {
    const eventTeam = simpleClone(this.eventTeam);
    console.log("event-team-options.onInputChange", eventTeam);
    this.$emit("onInputChange", eventTeam);
  }
}
</script>
