import { IEventE4S } from "../event-models";
import { IListParams } from "../../common/resource/resource-service";
import {
  GenderType,
  IBase,
  IServerGenericResponse,
  IServerPagingResponseList,
} from "../../common/common-models";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import { EventData } from "../event-data";
import { reactive } from "@vue/composition-api";
import { IEventGender } from "../../builder/form/event-selector-builder/event-selector-builder-models";
import { EventSelectorBuilderService } from "../../builder/form/event-selector-builder/event-selector-builder-service";
import { simpleClone } from "../../common/common-service-utils";

export type EventGenderBase = IEventGender & IBase;

export interface IEventGridSelectV2ControllerInput {
  genderFilters: GenderType[];
}

export interface IEventGridSelectV2ControllerState {
  searchKey: string;
  events: IEventE4S[];
  eventSelected: EventGenderBase;
  eventGenders: EventGenderBase[];
  isLoading: boolean;
}

const eventSelectorBuilderService = new EventSelectorBuilderService();

export function useEventGridSelectV2Controller(
  eventGridSelectV2ControllerInput: IEventGridSelectV2ControllerInput
) {
  const state = reactive<IEventGridSelectV2ControllerState>({
    searchKey: "",
    events: [],
    eventSelected: {
      id: 0,
      eventName: "",
      eventNameDisplay: "",
      events: {},
    },
    isLoading: false,
    eventGenders: [],
  });

  const eventData: EventData = new EventData();

  function search(
    searchKey: string
  ): Promise<IServerPagingResponseList<EventGenderBase>> {
    return new Promise((resolve) => {
      const defaultResponse: IServerPagingResponseList<EventGenderBase> = {
        errNo: 0,
        error: "",
        data: [],
        meta: {
          page: 1,
          pageSize: 0,
          totalCount: 0,
        },
      };

      if (searchKey.length === 0) {
        state.events = [];
        resolve(defaultResponse);
      }
      const listParams: IListParams = {
        startswith: searchKey,
        pagenumber: 1,
        pagesize: 20,
        sortkey: "name",
      };

      state.isLoading = true;
      return eventData
        .list(listParams)
        .then((response: IServerGenericResponse) => {
          if (response.errNo > 0) {
            messageDispatchHelper(
              response.error,
              USER_MESSAGE_LEVEL.ERROR.toString()
            );
            return;
          }

          let events: IEventE4S[] = response.data;
          if (eventGridSelectV2ControllerInput.genderFilters.length > 0) {
            events = events.filter((eventE4s) => {
              return (
                eventGridSelectV2ControllerInput.genderFilters.indexOf(
                  eventE4s.gender
                ) > -1
              );
            });
          }

          const eventGenders: EventGenderBase[] = eventSelectorBuilderService
            .getEventsForDropDown(events)
            .map((eventGender, index) => {
              const eventGenderBase: EventGenderBase = {
                ...eventGender,
                id: index,
              };
              return eventGenderBase;
            });

          state.eventGenders = eventGenders;
          state.isLoading = false;

          const responseNew: IServerPagingResponseList<EventGenderBase> = {
            errNo: 0,
            error: "",
            data: state.eventGenders,
            meta: {
              page: 1,
              pageSize: state.eventGenders.length,
              totalCount: state.eventGenders.length,
            },
          };

          resolve(responseNew);
        })
        .catch((error) => {
          messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
          state.events = [];
          state.isLoading = false;
          resolve(defaultResponse);
        });
    });
  }

  function getLabel(eventGender: IEventGender) {
    return eventGender.eventNameDisplay;
  }

  function selected(eventGenderBase: EventGenderBase) {
    state.eventSelected = simpleClone(eventGenderBase);
    reset();
  }
  function reset() {
    state.eventSelected = {
      id: 0,
      eventName: "",
      eventNameDisplay: "",
      events: {},
    };
  }

  return { state, search, getLabel, selected, reset };
}
