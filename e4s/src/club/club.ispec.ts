// import {IServerGenericResponse} from "../common/common-models";
// import {IClub} from "./club-models";
import {IServerGenericResponse} from "../common/common-models";
import {doJwtAuth, HTTP_TIMEOUT_MS} from "../common/common.ispec";
import {ClubData} from "./club-data";

const clubData: ClubData = new ClubData();

jest.setTimeout(9000);

beforeAll((done) => {
    doJwtAuth(done);
});

describe("Clubs", () => {

    test("getCompOrgs", async () => {

        const response: IServerGenericResponse = await clubData.getCompOrgs(117);
        //  console.log("...................getCompOrgs data", data);
        expect(response.data.length > 0).toBe(true);
    }, HTTP_TIMEOUT_MS);


    // test("searchClubs", async () => {
    //
    //     const data: IServerGenericResponse = await clubData.searchClubs(0, "nun");
    //     console.log("...................searchClubs data", data);
    //     expect(data.errNo === 0).toBe(true);
    //     return;
    // }, HTTP_TIMEOUT_MS);
});
