import { ResourceData } from "../../common/resource/resource-service";
import { IClub<PERSON>rud } from "../club-models";
import { IServerResponse } from "../../common/common-models";
import https from "../../common/https";

export type ClubType = "C" | "S" | "I";

export class ClubData<PERSON><PERSON> extends ResourceData<IClubCrud> {
  constructor() {
    super("/v5/clubCrud");
  }

  public searchClubs(
    areaId: number,
    key: string,
    isSchool: boolean,
    pageSize: number = 20,
    isInternational: boolean = false
  ): Promise<IServerResponse<IClubCrud[]>> {
    let clubType: ClubType = "C";
    if (isSchool) {
      clubType = "S";
    }
    if (isInternational) {
      clubType = "I";
    }
    return https.get(
      this.getEndPoint() +
        "?areaid=" +
        areaId +
        "&pagesize=" +
        pageSize +
        "&startswith=" +
        key +
        "&type=" +
        clubType
    ) as any as Promise<IServerResponse<IClubCrud[]>>;
  }

  public searchClubsByType(
    areaId: number,
    key: string,
    clubType: ClubType,
    option: string,
    pageSize: number = 20
  ): Promise<IServerResponse<IClubCrud[]>> {
    return https.get(
      this.getEndPoint() +
        "?areaid=" +
        areaId +
        "&pagesize=" +
        pageSize +
        "&startswith=" +
        key +
        "&type=" +
        clubType +
        "&option=" +
        option.toLowerCase()
    ) as any as Promise<IServerResponse<IClubCrud[]>>;
  }
}
