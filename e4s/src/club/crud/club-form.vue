<template>
    <div>
        <div class="row">
            <div class="col s12 m12 l12">

            </div>
        </div>
        <div class="row">
<!--            <div class="col s12 m6 l6">-->
<!--                <label>-->
<!--                    <input type="radio"-->
<!--                           id="club-type-club"-->
<!--                           :key="'C'"-->
<!--                           class="browser-default"-->
<!--                           :value="'C'"-->
<!--                           v-model="clubInternal.clubtype"/>-->
<!--                    <span>Club</span>-->
<!--                </label>-->
<!--                <label>-->
<!--                    <input type="radio"-->
<!--                           id="club-type-school"-->
<!--                           :key="'S'"-->
<!--                           class="browser-default"-->
<!--                           :value="'S'"-->
<!--                           v-model="clubInternal.clubtype"/>-->
<!--                    <span>School</span>-->
<!--                </label>-->
<!--                <label class="active" for="club-type-club">Type</label>-->
<!--            </div>-->

            <div class="col s12 m12 l12">
                <label class="active" for="club-name">
                    <span v-text="clubType === 'S' ? 'School' : 'Club'"></span> Name
                    <span v-text="clubInternal.id > 0 ? ' (' + clubInternal.id + ')' : ''"></span>
                    <ValidationFieldLable
                            :validation-controller="validationController"
                            prop-path="clubname"
                    ></ValidationFieldLable>
                </label>
                <input
                        id="club-name"
                        name="club-name"
                        v-model="clubInternal.clubname">
            </div>
            <div class="col s12 m12 l12">
                <ValidationFieldLable
                        :validation-controller="validationController"
                        prop-path="areaid"
                ></ValidationFieldLable>
                <AreaLookup
                        field-label="Area"
                        id="club-area"
                        v-on:onSelected="areaSelected"
                >

                </AreaLookup>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="right">
                    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
                    <slot name="cancel-button">
                        <button class="btn xxx-btn-small btn-flat red-text e4s-bold"
                                v-on:click.stop="cancel()">
                            <span v-text="$t('buttons.close')"></span>
                        </button>
                    </slot>
                    <button :disabled="isLoading"
                            class="btn waves-effect waves green"
                            v-on:click.stop="submitClub">
                        <span>Save</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
  import * as R from "ramda";
  import Vue from "vue";
  import Component from "vue-class-component";
  import { Prop, Watch } from "vue-property-decorator";
  import AreaLookup from "../../area/area-lookup.vue";
  import {IClubCrud} from "../club-models";
  import {ClubService} from "../club-service";
  import {IArea} from "../../area/area-models";
  import {ClubDataCrud} from "./club-data-crud";
  import {handleResponseMessages} from "../../common/handle-http-reponse";
  import {CLUB_TYPE} from "../../config/config-app-models";
  import {ValidationController} from "../../validation/validation-controller"
  import ValidationFieldLable from "../../validation/validation-field-lable.vue"

  const clubService: ClubService = new ClubService();


  @Component({
    name: "club-form",
    components: {ValidationFieldLable, AreaLookup}
  })
  export default class SchoolForm extends Vue {
    @Prop({
      default: () => {
        return clubService.factoryClubCrud();
      }
    })
    public readonly club!: IClubCrud;

    @Prop({
      default: "C"
    })
    public readonly clubType!: CLUB_TYPE;

    public clubService = clubService;
    public clubInternal: IClubCrud = this.clubService.factoryClubCrud();
    public isLoading = false;

    public validationController: ValidationController = new ValidationController();

    public created() {
      this.clubInternal = R.clone(this.club);
      this.clubInternal.clubtype = this.clubType;
    }

    @Watch("club")
    public onClubChanged(newValue: IClubCrud) {
      this.clubInternal = R.clone(newValue);
    }

    @Watch("clubType")
    public onClubTypeChanged(newValue: CLUB_TYPE) {
      this.clubInternal.clubtype = newValue;
    }

    public areaSelected(area: IArea) {
        this.clubInternal.areaid = area.id;
    }

    public validate() {
          this.validationController.reset();
          if (this.clubInternal.clubname.length === 0) {
              this.validationController.addError("clubname", ["Please enter a name"]);
          }
          if (this.clubInternal.areaid === 0) {
              this.validationController.addError("areaid", ["Please enter an area."]);
          }
    }

    public submitClub() {

        this.validate();

        if (!this.validationController.isValid) {
            return;
        }

        let prom;
        this.isLoading = true;
        if (this.clubInternal.id === 0) {
          prom = new ClubDataCrud().create(this.clubInternal);
        } else {
          prom = new ClubDataCrud().update(this.clubInternal);
        }
        handleResponseMessages(prom);
        prom.then( (resp) => {
          if (resp.errNo === 0) {
            this.clubInternal = resp.data;
            this.submit();
          }
        })
      .finally( () => {
        this.isLoading = false;
      })

    }

    public cancel() {
      this.$emit("cancel");
    }

    public submit() {
      this.$emit("submit", R.clone(this.clubInternal));
    }
  }
</script>
