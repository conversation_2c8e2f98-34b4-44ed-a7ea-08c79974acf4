import { IBase, IBaseRaw } from "../common/common-models";
import { CLUB_TYPE } from "../config/config-app-models";

export interface IClub extends IBase {
  clubid: number;
  club: string;
}

export interface IClubLookup {
  id: number;
  areaId: number;
  clubName: string;
  country: string;
  county: string;
  region: string;
}

export interface IClubCrud extends IBaseRaw {
  clubname: string;
  clubtype: CLUB_TYPE;
  areaid: number;
  areaname: string;
  region: string;
  country: string;
  active: boolean;
}
