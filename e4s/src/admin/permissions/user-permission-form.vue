<template>
  <div>
    <div v-if="!showPermissionForm">
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-header--400">User Permissions</div>
        </div>
      </div>

      <div v-for="permission in permissions">
        <div class="row">
          <div class="col s12 m12 l12" v-text="getPermText(permission)"></div>
        </div>
        <div class="row">
          <div
            class="col s12 m12 l12"
            v-text="getPermLevelText(permission.permLevels)"
          ></div>
        </div>
        <div class="row">
          <div class="col s12 m12 l12">
            <div class="right">
              <a href="#" v-on:click.prevent="editPermission(permission)">
                <i class="material-icons">edit</i>
              </a>
              <a
                href="#"
                v-on:click.prevent="getDeletePermissionConf(permission)"
              >
                <i class="material-icons red-text">delete_forever</i>
              </a>
            </div>
          </div>
        </div>

        <div class="e4s-card-standard-sep"></div>
      </div>

      <div class="row">
        <div class="col s12 m12 l12">
          <!--          <button-->
          <!--            class="btn waves-effect waves green right"-->
          <!--            v-on:click="setShowPermissionForm(true)"-->
          <!--          >-->
          <!--            <span v-text="$t('buttons.add')"></span>-->
          <!--          </button>-->
          <div class="e4s-flex-row">
            <ButtonGenericV2
              text="Add"
              @click="setShowPermissionForm(true)"
              class="e4s-flex-row--end"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="row" v-if="showPermissionForm">
      <div class="col s12 m12 l12">
        <permission-form
          :permission-prop="permission"
          :is-loading="isLoading"
          :roles="roles"
          v-on:onCancel="setShowPermissionForm(false)"
          v-on:onSubmit="onSubmitPermission"
        >
        </permission-form>
      </div>
    </div>

    <e4s-modal
      v-if="showPermRemoveConf"
      :header-message="'Permission Delete'"
      :body-message="'Are you sure you would like to delete permission?'"
      :button-primary-text="'Continue'"
      :isLoading="isLoading"
      v-on:closeSecondary="showPermRemoveConf = false"
      v-on:closePrimary="deletePermission"
    >
    </e4s-modal>

    <!--        Perms-->
    <!--        userPermissionsProp{{userPermissionsProp}}<br><br>-->
    <!--            roles{{roles}}-->
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IPermission, IPermLevel } from "./permission-models";
import { PermissionsService } from "./permissions-service";
import { IUser } from "../user/user-models";
import PermissionForm from "./permission-form.vue";
import {
  PERMISSION_STORE_CONST,
  IPermissionStoreState,
} from "./permission-store";
import { PermissionData } from "./permission-data";
import { mapState } from "vuex";
import E4sModal from "../../common/ui/e4s-modal.vue";
import { IServerResponse } from "../../common/common-models";
import { messageDispatchHelper } from "../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../user-message/user-message-models";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

@Component({
  name: "user-permissions-form",
  components: {
    ButtonGenericV2,
    "permission-form": PermissionForm,
    "e4s-modal": E4sModal,
  },
  computed: {
    ...mapState(PERMISSION_STORE_CONST.PERMISSION_CONST_MODULE_NAME, {
      roles: (state: IPermissionStoreState) => state.roles,
    }),
  },
})
export default class UserPermissionsForm extends Vue {
  @Prop({
    required: true,
  })
  public userProp: IUser;

  public permissionsService: PermissionsService = new PermissionsService();
  public permissions: IPermission[] = [];
  public permissionData: PermissionData = new PermissionData();

  public permission: IPermission = this.permissionsService.factoryPermission();
  public isLoading: boolean = false;

  public showPermissionForm: boolean = false;
  public showPermRemoveConf: boolean = false;

  public created() {
    this.permissions = R.clone(this.userProp.permissions);
  }

  @Watch("userProp")
  public onUserPropChange(newValue: IUser) {
    this.permissions = R.clone(newValue.permissions);
  }

  public getPermLevelText(permLevels: IPermLevel[]): string {
    if (R.isNil(permLevels) || permLevels.length === 0) {
      return "";
    }
    return permLevels
      .map((permLevel) => {
        return permLevel.name;
      })
      .join(", ");
  }

  public getPermText(permission: IPermission) {
    return (
      "Role: " +
      permission.role.name +
      ", Org: " +
      (permission.org.name.length > 0 ? permission.org.name : "") +
      (permission.comp.id > 0 ? ", Comp: " + permission.comp.name : "")
    );
  }

  public setShowPermissionForm(show: boolean) {
    this.showPermissionForm = show;
  }

  public editPermission(permission: IPermission) {
    this.permission = R.clone(permission);
    this.setShowPermissionForm(true);
  }

  public getDeletePermissionConf(permission: IPermission) {
    this.permission = R.clone(permission);
    this.showPermRemoveConf = true;
  }

  public onSubmitPermission(permission: IPermission) {
    this.setShowPermissionForm(false);
    this.isLoading = true;
    this.permissionData
      .submitPermission(this.userProp.id, permission)
      .then((response: IServerResponse<IPermission[]>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        messageDispatchHelper(
          "Permission saved.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        // this.permissions = R.clone(response.data);

        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      })
      .finally(() => {
        this.isLoading = false;
        this.reloadUserPermissions();
      });
  }

  public deletePermission() {
    this.isLoading = true;
    this.permissionData
      .deletePermission(this.userProp.id, this.permission)
      .then((response: IServerResponse<IPermission[]>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        messageDispatchHelper(
          "Permission deleted.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        // this.permissions = R.clone(response.data);
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      })
      .finally(() => {
        this.isLoading = false;
        this.showPermRemoveConf = false;
        this.reloadUserPermissions();
      });
  }

  public reloadUserPermissions() {
    this.permissionData
      .getUserPermissions(this.userProp.id)
      .then((response: IServerResponse<IPermission[]>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        messageDispatchHelper(
          "Permission reloaded.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        this.permissions = R.clone(response.data);
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return;
      })
      .finally(() => {
        this.isLoading = false;
        this.showPermRemoveConf = false;
      });
  }
}
</script>
