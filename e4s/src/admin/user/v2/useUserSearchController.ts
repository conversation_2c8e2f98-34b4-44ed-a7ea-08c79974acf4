import { UserService } from "../user-service";
import { IUserProfile, IUserSummary } from "../user-models";
import { reactive } from "@vue/composition-api";
import {UserProfileData} from "../user-data"
import {IServerResponse} from "../../../common/common-models"
import {messageDispatchHelper} from "../../../user-message/user-message-store"
import {USER_MESSAGE_LEVEL} from "../../../user-message/user-message-models"
import {PERMISSION_STORE_CONST} from "../../permissions/permission-store"
import {simpleClone} from "../../../common/common-service-utils"
import {useStore} from "../../../app.store"

export interface IUserSearchControllerState {
  userSummary: IUserSummary;
  userProfile: IUserProfile;
  isLoading: boolean;
}

export function useUserSearchController() {
  const userService: UserService = new UserService();
  const userProfileData: UserProfileData = new UserProfileData();
  const myStore = useStore();

  const state: IUserSearchControllerState = reactive({
    userSummary: userService.factoryUserSummary(),
    userProfile: userService.factoryUserProfile(),
    isLoading: false,
  });

  function getUserProfile(userSummary: IUserSummary) {
    state.userSummary = userSummary;
    if (!(userSummary && userSummary.id > 0)) {
      state.userProfile = userService.factoryUserProfile();
      return;
    }
    state.isLoading = true;
    userProfileData.getUserProfileById(userSummary.id)
      .then( (response: IServerResponse<IUserProfile>) => {
        if (response.errNo > 0) {
          messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
          return;
        }
        state.userProfile = simpleClone(response.data);
        myStore.commit(PERMISSION_STORE_CONST.PERMISSION_CONST_MODULE_NAME + "/"
          + PERMISSION_STORE_CONST.PERMISSION_MUTATIONS_STORE_SET_ROLES, response.meta);
        return;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        return {};
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  return {
    state,
    getUserProfile,
  };
}
