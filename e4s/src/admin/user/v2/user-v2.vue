<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-header--200">User Profile</div>

    <div class="e4s-flex-row e4s-justify-flex-space-between" v-if="false">
      <div class="e4s-flex-column">
        <h2 class="e4s-header--400">
          <div class="e4s-flex-row e4s-gap--standard">
            <div v-text="userController.userProfile.user.displayName"></div>
            <AdminIdV2 :obj="userController.userProfile.user"/>
          </div>
        </h2>
        <p
          class="e4s-subheader--general e4s-subheader--500"
          v-text="userController.registered.value"
        ></p>
      </div>
      <!--            <slot name="right-content">-->
      <!--              <ButtonGenericBackV2 v-if="showCancelButton" v-on:click="cancel" />-->
      <!--            </slot>-->
    </div>

    <User :user-profile-prop="userProfileForOldComponent"/>

  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  SetupContext,
  PropType, watch, ref,
} from "@vue/composition-api"
import { IUserProfile } from "../user-models";
import { UserService } from "../user-service";
import {userProfileController} from "./userProfileController"
import AdminIdV2 from "../../../common/ui/layoutV2/admin-id-v2.vue"
import User from "../user.vue";
import {simpleClone} from "../../../common/common-service-utils"

const userService: UserService = new UserService();

export default defineComponent({
  name: "user-v2",
  components: {AdminIdV2, User},
  props: {
    userProfile: {
      type: Object as PropType<IUserProfile>,
      default: () => {
        return userService.factoryUserProfile();
      },
    },
  },
  setup(props: { userProfile: IUserProfile }, context: SetupContext) {

    // const userProfileInternal = reactive(userService.factoryUserProfile())

    const userController = userProfileController();
    const userProfileForOldComponent = ref(userService.factoryUserProfile());

    watch(
      () => props.userProfile,
      (newValue: IUserProfile) => {
        userController.init(newValue);
        userProfileForOldComponent.value = simpleClone(newValue);
      },
      {
        immediate: true,
      }
    );

    return { userController, userProfileForOldComponent };
  },
});
</script>

<!--<template>-->
<!--  <div>-->
<!--  </div>-->
<!--</template>-->

<!--<script lang="ts">-->
<!--import {-->
<!--  defineComponent,-->
<!--  watch,-->
<!--  SetupContext,-->
<!--} from "@vue/composition-api";-->

<!--export default defineComponent({-->
<!--  name: "test-composition",-->
<!--  components: {},-->
<!--  props: {-->
<!--    consult: {-->
<!--      default: () => {-->
<!--        return {}-->
<!--      }-->
<!--    }-->
<!--  },-->
<!--  setup(props: { consult: any }, context: SetupContext) {-->

<!--If using newValue: any, oldValue: any AND immediate: true, only use newValue: any-->
<!--    watch(-->
<!--      () => props.consult,-->
<!--      (newValue: any, oldValue: any) => {-->
<!--        Object.assign(consultInternal, newValue);-->
<!--      }-->
<!--      ,-->
<!--      {immediate: true}-->
<!--    );-->

<!--    return {-->
<!--    };-->
<!--  }-->
<!--});-->
<!--</script>-->
