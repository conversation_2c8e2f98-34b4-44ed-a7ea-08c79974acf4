import { getUserProfileCompClubs } from "./user-service-v2";
import { IUserClubComp, IUserProfile } from "../user-models";

describe("user-service-v2", () => {
  test("getUserProfileCompClubs with object clubComps", () => {
    const mockUserProfile: IUserProfile = {
      user: { id: 1, name: "Test User" },
      clubComps: {
        "617": {
          clubCompId: 117,
          comp: {
            id: 617,
            name: "English Schools 2025 test",
          },
          club: {
            id: 10583,
            name: "Essex",
          },
        },
      },
    } as any as IUserProfile;

    const result: IUserClubComp[] = getUserProfileCompClubs(mockUserProfile);

    expect(result).toHaveLength(1);
    expect(result[0].clubCompId).toBe(117);
    expect(result[0].comp.id).toBe(617);
    expect(result[0].comp.name).toBe("English Schools 2025 test");
    expect(result[0].club.id).toBe(10583);
    expect(result[0].club.name).toBe("Essex");
  });

  test("getUserProfileCompClubs with empty clubComps", () => {
    const mockUserProfile: IUserProfile = {
      user: { id: 1, name: "Test User" },
    } as any as IUserProfile;

    const result = getUserProfileCompClubs(mockUserProfile);
    expect(result).toHaveLength(0);
  });
});
