<template>
    <div class="e4s-flex-column e4s-gap--standard">
        <div class="e4s-card e4s-flex-column e4s-card--generic">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="e4s-header--400">
                        Cart
                    </div>
                </div>
            </div>
            <cart-card-grid
                    :cartEvents="userProfile.cart"
                    v-on:togglePaid="togglePaidConf"
                    v-on:onRemoveEvent="removeEventConf">
            </cart-card-grid>
        </div>

        <div class="e4s-card e4s-flex-column e4s-card--generic">
            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="e4s-header--400">
                        Orders
                    </div>
                </div>
            </div>
            <cart-card-grid
                    :cartEvents="userProfile.orders"
                    v-on:togglePaid="togglePaidConf"
                    v-on:onRemoveEvent="removeEventConf">
            </cart-card-grid>
        </div>

        <TogglePaid :cart-event="togglePaidEvent"
                    :show-toggle-paid-conf="showTogglePaidConf"
                    v-on:eventPaidToggle="togglePaidComplete"
                    v-on:onCancel="togglePaidComplete">
        </TogglePaid>

        <e4s-modal v-if="showEventRemoveConf"
                   :header-message="'Event Delete'"
                   :body-message="'Are you sure you would like to delete event, continue?'"
                   :button-primary-text="'Continue'"
                   :isLoading="isLoading"
                   v-on:closeSecondary="showEventRemoveConf = false"
                   v-on:closePrimary="removeEvent">
        </e4s-modal>

    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {IUserProfile} from "./user-models";
    import {Prop} from "vue-property-decorator";
    import { CONFIG_STORE_CONST } from "../../config/config-store";
    import CartCardGrid from "../../cart/cart-card-grid.vue";
    import E4sModal from "../../common/ui/e4s-modal.vue";
    import { mapGetters } from "vuex";
    import { ICartEvent } from "../../cart/cart-models";
    import { ATH_COMP_SCHED_STORE_CONST } from "../../athletecompsched/store/athletecompsched-store";
    import TogglePaid from "../../compevent/toggle-paid/toggle-paid.vue";

    @Component({
        name: "user-events",
        components: {
            "e4s-modal": E4sModal,
            "cart-card-grid": CartCardGrid,
            TogglePaid
        },
        computed: {
            ...mapGetters(
                {
                    isAdmin: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN
                }
            )
        }
    })
    export default class UserEvents extends Vue {
        @Prop({
            default: () => {
                return {
                    id: 0
                };
            }
        }) public readonly userProfile: IUserProfile;

        public isLoading: boolean = false;

        public togglePaidEvent: ICartEvent | null = null;
        public showTogglePaidConf: boolean = false;
        public paidMessage: string  = "";

        public showEventRemoveConf: boolean = false;
        public removeEventObject: ICartEvent | null = null;

        public removeEventConf(cartEvent: ICartEvent) {
            this.removeEventObject = R.clone(cartEvent);
            this.showEventRemoveConf = true;
        }

        public removeEvent() {
            const removeEventObject = this.removeEventObject;
            if (!removeEventObject) {
                return;
            }
            this.$store.dispatch(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_REMOVE_FROM_SELECTED,
                { event: removeEventObject }
            )
                .then(() => {
                    this.showEventRemoveConf = false;
                    this.$emit("eventRemoved", R.clone(removeEventObject));
                });
        }

        public togglePaidConf(cartEvent: ICartEvent) {
            this.togglePaidEvent = R.clone(cartEvent);
            this.showTogglePaidConf = true;
        }

        public togglePaidComplete() {
            this.showTogglePaidConf = false;
        }


    }
</script>
