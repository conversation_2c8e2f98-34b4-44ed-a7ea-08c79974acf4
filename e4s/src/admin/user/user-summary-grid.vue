<template functional>
    <div>
        <div v-for="(user, index) in props.users"
             class="e4s-card-std"
             :class="index % 2 === 0 ? '' : 'e4s-card-std__row-odd'"
             :key="user.id">

            <div class="row">
                <div class="col s2 m2 l2">
                    <span v-text="user.id"></span>
                </div>

                <div class="col s8 m8 l8">
                    <span v-text="user.name"></span>
                </div>

                <div class="col s2 m2 l2">
                    <div class="right">
                        <button class="btn xxx-btn-small btn-flat red-text e4s-bold"
                                v-on:click.stop="listeners.onRemove(user.id)">
                            <span>X</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script lang="ts">
    export default {
        props: ["users", "onRemove", "onSelect"]
    };
</script>
