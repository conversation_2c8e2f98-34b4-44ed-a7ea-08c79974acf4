import { IBase, IBaseConcrete } from "../../common/common-models";
import { IClub } from "../../club/club-models";
import { IAthleteSummary } from "../../athlete/athlete-models";
import { IPermission } from "../permissions/permission-models";
import { ICartEvent } from "../../cart/cart-models";
import { IAthleteCompSchedRuleEvent } from "../../athletecompsched/athletecompsched-models";
import { UiVersion } from "../../config/config-app-models";

export interface IUserVersion {
  current: UiVersion;
  toggle: boolean; //  false: user can NOT toggle, stay on current version
}

export interface IUserSummary extends IBase {
  login: string;
  niceName: string;
  email: string;
  displayName: string;
}

export interface IUser extends IUserSummary {
  registered: string;
  permissions: IPermission[];
  version: IUserVersion;
}

export interface IUserArea extends IBase {
  name: string;
  shortName: string;
  parentName: string;
  entityLevel: string;
  entityName: string;
}

//  extends Omit<IBase, "id">
export interface IUserProfile extends IBase {
  user: IUser;
  clubs: IClub[];
  areas: IUserArea[];
  athletes: IAthleteSummary[];
  cart: ICartEvent[];
  orders: IAthleteCompSchedRuleEvent[];
  e4sCredit: number;
  clubComps: UserCompClubs;
}

export type UserCompClubs = Record<number, IUserClubComp>;

export interface IUserClubComp {
  club: IBaseConcrete;
  clubCompId: number;
  comp: IBaseConcrete;
}

export type UserProfileShowSection =
  | "GENERAL"
  | "SECURITY"
  | "ATHLETES"
  | "ENTRIES";
