<template>
    <AutoCompleteMat
            :field-label="''"
            :custom="getCustomForAutoComplete"
            :data = "userProfiles"
            iconClassName=""
            :placeholder="getPlaceHolder"
            :is-loading="isLoading"
            :user-input-preload="''"
            v-on:searchTermChanged="searchTermChanged"
            v-on:autoSelectionMade="onSelected">
    </AutoCompleteMat>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {debounce} from "../../common/debounce";
    import AutoCompleteMat from "../../common/ui/autocomplete/auto-complete-mat.vue";
    import {IAutoCompleteValue, ICustom} from "../../common/ui/autocomplete/auto-complete-mat-models";
    import {IUserSummary} from "./user-models";
    import {UserProfileData} from "./user-data";
    import { UserService } from "./user-service";
    import {IServerResponse} from "../../common/common-models";
    import {messageDispatchHelper} from "../../user-message/user-message-store";
    import {USER_MESSAGE_LEVEL} from "../../user-message/user-message-models";

    const userProfileData: UserProfileData = new UserProfileData();
    const userService: UserService = new UserService();

    @Component({
        name: "user-search-type-ahead",
        components: {
            AutoCompleteMat
        }
    })
    export default class UserSearchTypeAhead extends Vue {

        public isLoading: boolean = false;
        public selectedUser: IUserSummary = userService.factoryUserSummary();
        public searchTextEntered: string = "";

        public debounceSearch: any;

        public userProfiles: IUserSummary[] = [];

        public created() {
            console.log("UserSearchTypeAhead.created()...");
        }


        public mounted() {
            console.log("UserSearchTypeAhead.mounted()...");
            this.debounceSearch =  debounce(( key: string, pageNumber: number, pageSize: number ) => {

                this.isLoading = true;
                this.$emit("onSelected", userService.factoryUserSummary());

                userProfileData.search(key, pageNumber, pageSize, "")
                    .then( (response: IServerResponse<IUserSummary[]>) => {
                        if (response.errNo > 0) {
                            messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                            return;
                        }
                        this.userProfiles = R.clone(response.data);
                        return;
                    })
                    .catch((error) => {
                        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                        return {};
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });

            }, 100);
        }


        public searchTermChanged(searchKey: string) {
            console.log("UserSearchTypeAhead.searchTermChanged()...searchKey: >" + searchKey + "<");
            if (searchKey.length === 0) {
                console.log("UserSearchTypeAhead.searchTermChanged()...searchKey: >" + searchKey + "<  DO NOT SEARCH");
                return;
            }

            this.searchTextEntered = searchKey;
            this.isLoading = true;
            const pageNumber = 1;
            const pageSize = 10;
            const orderByProperty = "surname";

            this.debounceSearch(this.searchTextEntered, pageNumber, pageSize, orderByProperty);
        }

        public getLabelForAthleteAutoComplete(userSummary: IUserSummary): string {
            return "[" + userSummary.id + "] " + userSummary.displayName + " : " + userSummary.email + " : " + userSummary.login;
        }

        public get getCustomForAutoComplete(): ICustom {
            return {
                dropDownLabelFunc: this.getLabelForAthleteAutoComplete
            } as ICustom;
        }

        public get getPlaceHolder(): string {
            return "Enter search term...";
        }

        public onSelected(autoCompleteValue: IAutoCompleteValue) {
            console.log("UserSearchTypeAhead.onSelected", R.clone(autoCompleteValue));
            this.$emit("onSelected", R.clone(autoCompleteValue.value));
        }
    }

</script>

<style scoped>
    .IZ-select__input {
        box-shadow: 0 0px 0px 0px rgba(0,0,0,.2), 0 0px 0px 0 rgba(0,0,0,.14), 0 0px 0px 0 rgba(0,0,0,.12) !important;
    }
</style>
