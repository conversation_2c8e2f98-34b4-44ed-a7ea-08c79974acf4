<template>
  <FormGenericFieldGridV2>
    <template slot="content">
      <FormGenericInputTemplateV2 form-label="Credit \ Reason">
        <template slot="field">
          <InputWithButton>
            <FieldNumberV2
              v-model="creditAmount"
              :is-disabled="isLoading"
              field-size="small"
              class="e4s-full-width e4s-square--right"
              style="width: 100px"
              slot="before"
            />

            <FieldTextV2
              v-model="reason"
              class="e4s-full-width e4s-square--right e4s-square--left"
              slot="field"
            />
            <ButtonGenericV2
              text="Save"
              slot="after"
              class="e4s-square--left e4s-button--auto"
              :disabled="isLoading"
              with-input="right"
              @click="setCredit"
            />
          </InputWithButton>
        </template>
      </FormGenericInputTemplateV2>
    </template>
  </FormGenericFieldGridV2>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { UserService } from "../user-service";
import { IUserProfile } from "../user-models";
import * as CommonServiceUtils from "../../../common/common-service-utils";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { UserProfileData } from "../user-data";
import { useConfigController } from "../../../config/useConfigStore";
import FormGenericFieldGridV2 from "../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import InputWithButton from "../../../common/ui/layoutV2/fields/InputWithButton.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldNumberV2 from "../../../common/ui/layoutV2/fields/field-number-v2.vue";

const userService = new UserService();
const userProfileData = new UserProfileData();

export default defineComponent({
  name: "credit",
  components: {
    FieldNumberV2,
    FormGenericInputTemplateV2,
    FieldTextV2,
    InputWithButton,
    ButtonGenericV2,
    FormGenericFieldGridV2,
  },
  props: {
    userProfile: {
      type: Object as PropType<IUserProfile>,
      default: () => {
        return userService.factoryUserProfile();
      },
    },
  },
  setup(props: { userProfile: IUserProfile }, context: SetupContext) {
    const PREFIX = Math.random().toString(36).substring(2);
    const creditAmount = ref(props.userProfile.e4sCredit);
    const reason = ref("");
    const isLoading = ref(false);
    const configController = useConfigController();

    watch(
      () => props.userProfile,
      (newValue: IUserProfile) => {
        if (newValue.user.id > 0) {
          creditAmount.value = newValue.e4sCredit;
        }
      }
    );

    function setCredit() {
      if (!CommonServiceUtils.hasAtLeastOneCharacter(reason.value)) {
        messageDispatchHelper("Please enter a credit reason");
        return;
      }

      isLoading.value = true;
      const prom = userProfileData.setUserCredit(
        props.userProfile.user.id,
        creditAmount.value,
        reason.value
      );
      handleResponseMessages(prom);
      prom.finally(() => {
        isLoading.value = false;
      });
    }

    return {
      PREFIX,
      creditAmount,
      reason,
      setCredit,
      configController,
      isLoading,
    };
  },
});
</script>
