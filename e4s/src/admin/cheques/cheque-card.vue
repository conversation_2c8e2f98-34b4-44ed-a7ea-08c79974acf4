<template>

    <div>
        <div class="row">
            <div class="col s2 m2 l2">
                <div v-text="getOrderDateTime"></div>
            </div>
            <div class="col s2 m2 l2">
                <div v-text="chequeCard.cheque.id"></div>
            </div>
            <div class="col s2 m2 l2">
                <div v-text="configApp.currency + chequeCard.cheque.total"></div>
            </div>
            <div class="col s2 m2 l2">
                <div v-text="chequeCard.cheque.userEmail"></div>
            </div>
            <div class="col s2 m2 l2">
                <div v-text="chequeCard.cheque.status"></div>
            </div>
            <div class="col s2 m2 l2">
                <div class="event-card-checkbox right"
                     v-if="!isLoading"
                     v-on:click.stop="onSelect">
                    <input type="checkbox"
                           :checked="chequeCard.selected"/>
                    <span></span>
                </div>
            </div>
        </div>
    </div>



</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {IChequeCard} from "./cheque-models";
    import {Prop} from "vue-property-decorator";
    import {CommonService} from "../../common/common-service";
    import {mapState} from "vuex";
    import {IConfigStoreState, CONFIG_STORE_CONST} from "../../config/config-store";

    @Component({
        name: "cheque-card",
        components: {
        },
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: IConfigStoreState) => state.configApp
            })
        }
    })
    export default class ChequeCard extends Vue {
        @Prop({required: true}) public chequeCard: IChequeCard;
        @Prop({default: false}) public isLoading: boolean;

        public commonService: CommonService = new CommonService();

        public onSelect() {
            this.$emit("onSelected", R.clone(this.chequeCard));
        }

        public get getOrderDateTime() {
            //  test comment
            return this.commonService.getE4sStandardDateTimeOutPut(this.chequeCard.cheque.orderDate);
        }

    }

</script>
