import {IStripeUser, StripeUsersData} from "./stripe-users-data"
import { handleResponseMessages } from "../../common/handle-http-reponse";
import { IListParams } from "../../common/resource/resource-service";
import { factoryStripeUserState } from "./stripe-users-service";
import { reactive } from "@vue/composition-api";

export interface IStripeUserState {
  users: Record<number, IStripeUser>;
  listParams: IListParams;
  ui: {
    isLoading: boolean;
  };
}

export function factoryStripeUsersController() {
  const stripeUsersData = new StripeUsersData();
  const state = factoryStripeUserState();

  function getUsers() {
    state.ui.isLoading = true;
    const prom = stripeUsersData.getUsers();
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          state.users = resp.data;
        }
      })
      .finally(() => {
        state.ui.isLoading = false;
      });
  }

  function doApproval(stripeUser: IStripeUser) {
    state.ui.isLoading = true;
    const prom = stripeUsersData.doApproval(stripeUser);
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          console.log("xxxx");
          getUsers();
        }
      })
      .finally(() => {
        state.ui.isLoading = false;
      });
  }

  return {
    state,
    getUsers,
    doApproval
  };
}

export function useStripeUsers() {
  const controller = factoryStripeUsersController();
  const state = reactive(controller.state);

  return {
    state,
    controller,
  };
}
