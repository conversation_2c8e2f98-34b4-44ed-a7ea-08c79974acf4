<template>
    <div>
        <div class="e4s-section-padding-separator"></div>
        <reports :reports="reports"></reports>
    </div>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import { mapState } from "vuex";
    import { CONFIG_STORE_CONST, IConfigStoreState } from "../../config/config-store";
    import {IConfigApp, IReport} from "../../config/config-app-models";
    import { Watch } from "vue-property-decorator";
    import Reports from "./reports.vue";

    @Component({
        name: "reports-container",
        components: {
            Reports
        },
        computed: {
            ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
                configApp: (state: IConfigStoreState) => state.configApp
            })
        }
    })
    export default class ReportsContainer extends Vue {
        public readonly configApp: IConfigApp;
        public reports: IReport[] = [];

        public created() {
            if (this.configApp.menus && this.configApp.menus.reports) {
                this.reports = R.clone(this.configApp.menus.reports);
            }
        }


        @Watch("configApp")
        public onConfigAppChanged(configApp: IConfigApp) {
            if (configApp.menus && configApp.menus.reports) {
                this.reports = R.clone(configApp.menus.reports);
            }
        }
    }
</script>
