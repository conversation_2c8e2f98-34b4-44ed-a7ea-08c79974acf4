// import {reactive} from "@vue/composition-api"
import {simpleClone} from "../common/common-service-utils"
import {computed, reactive} from "@vue/composition-api"
import {LaunchRouteValueV2} from "../launch/v2/launch-routes-v2"
import {useRoute} from "./migrateRouterVue3"

/**
 * I have no idea how to watch from some component if the router has errored.
 * E.g. you are on "home" route and router to it again, what you want is to reload or
 * run some logic to change state.
 *
 *        routerInternal.push({
            name: LAUNCH_ROUTES_PATHS_V2.HOME_V2,
          } as RawLocation)
 .catch( (error: any) => {
            console.log("launch-header goHome error");
          })
 You can catch this error...but from another component, tried everything...
 const xxx = useRouter();
 const appErrors = computed( () => {
      // const items = xxx.app.errors.items;
      return [];
    })

 xxx.beforeResolve( (to: any, from: any, next: any) => {
      console.log("rrrrrrrrrrrrrrrrrrr");
      next();
    })

 xxx.onError( (error: any)=>{
      console.log("eeeeeeeeeeeeeeeeee");
    })

 xxx.beforeEach( (to: any, from: any, next: any) => {
      console.log("bbbbbbbbbbbbbbbbbbbbbb");
      next();
    })

 xxx.afterEach( (data: any) => {
      console.log("xxxxxxxxxxxxxxxxxxxxxxxx");
    })
 */
export interface IE4sRouteControllerError {
  id: number;
  name: string | "NAVIGATIONDUPLICATED";
  message: string;
}

export interface IE4sRouteControllerState {
  counter: number;
  currentRouterError: IE4sRouteControllerError;
  routeErrors: IE4sRouteControllerError[];
}

const e4sRouteControllerState = reactive<IE4sRouteControllerState>({
  counter: 0,
  currentRouterError: {
    id: 0,
    name: "",
    message: "",
  },
  routeErrors: [],
});

// const e4sRouteControllerState: IE4sRouteControllerState = {
//   counter: 0,
//   currentRouterError: {
//     id: 0,
//     name: "",
//     message: "",
//   },
//   routeErrors: [],
// };

export function useE4sRouteController() {
  const MAX_ERRORS = 10;

  const routeX = useRoute();

  function addError(error: IE4sRouteControllerError) {
    e4sRouteControllerState.counter++;
    error = simpleClone(error);
    error.id = e4sRouteControllerState.counter;
    error.name = error.name.toUpperCase();
    e4sRouteControllerState.currentRouterError = error;
    if (e4sRouteControllerState.routeErrors.length >= MAX_ERRORS) {
      e4sRouteControllerState.routeErrors.shift();
    }
    e4sRouteControllerState.routeErrors.push(error);
  }

  const getContentWidth = computed(() => {

    // const wideRoutes: LaunchRouteValueV2[] = ["home-v2", "builder-v2"];
    const widerRoutesClass: Record<LaunchRouteValueV2, string> = {
      "home-v2": "e4s-width-controller-entry",
      "builder-v2": "e4s-width-controller--builder "
    };

    const routeName: LaunchRouteValueV2 = routeX.name ? routeX.name : "" as any as LaunchRouteValueV2;
    // if (routeX.name && routeX.name === "home-v2") {
    if (widerRoutesClass[routeName]) {
      // return "e4s-width-controller-entry";
      return widerRoutesClass[routeName];
    }
    return "e4s-width-controller";
  });

  return {
    state: e4sRouteControllerState,
    addError,
    getContentWidth
  };
}
