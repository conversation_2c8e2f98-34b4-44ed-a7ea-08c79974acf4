<template>
    <!--<div>-->
        <!--<iframe :src="url"  style="border: none; height: 48vh; width: 100%;"></iframe>-->
    <!--</div>-->
    <iframe :src="url"></iframe>
</template>


<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {CONFIG} from "../common/config";
    import {ICompetition} from "../competition/competition-models";

    @Component({})
    export default class Schedule extends Vue {

        @Prop() public competition: ICompetition;

        public url: string = "";

        public created() {
            this.url = CONFIG.E4S_HOST + "/entry/v5/competition/schedule.php?compid=" + this.competition.id;
        }

        @Watch("competition")
        public onCompetitionChanged(newValue: ICompetition, oldValue: ICompetition) {
            if (newValue && newValue.id) {
                this.url = CONFIG.E4S_HOST + "/entry/v5/competition/schedule.php?compid=" + newValue.id;
            } else {
                this.url = "";
            }
        }

    }
</script>
