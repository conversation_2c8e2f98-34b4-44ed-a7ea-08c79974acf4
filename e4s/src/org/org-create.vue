<template>
  <div>
    <div class="input-field col s8 m8 l8">
      <auto-complete-mat
        field-label="Organisation"
        label-prop="name"
        :data="compOrgs"
        iconClassName=""
        :is-loading="compOrgsLoading"
        v-on:searchTermChanged="compOrgSearchTermChanged"
        v-on:autoSelectionMade="compOrgSelected"
      >
      </auto-complete-mat>
    </div>
    <div class="input-field col s4 m4 l4">
      <div class="right">
        <ButtonGenericV2
          button-type="tertiary"
          :disabled="compOrgsLoading"
          @click="cancel()"
        />
        <!--                <button :disabled="compOrgsLoading"-->
        <!--                        class="btn waves-effect waves grey"-->
        <!--                        v-on:click.stop="cancel()">Cancel</button>-->

        <!--                <button :disabled="compOrgsLoading"-->
        <!--                        class="btn waves-effect waves green"-->
        <!--                        v-on:click.stop="createOrgcreateOrg()">Save</button>-->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import { mapState } from "vuex";
import { IBuilderStoreState } from "../builder/builder-store";
import { BUILDER_STORE_CONST } from "../builder/builder-store-constants";
import { BuilderService } from "../builder/builder-service";
import { IOrg } from "./org-models";
import AutoCompleteMat from "../common/ui/autocomplete/auto-complete-mat.vue";
import { IAutoCompleteValue } from "../common/ui/autocomplete/auto-complete-mat-models";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";

@Component({
  name: "org-create",
  components: {
    ButtonGenericV2,
    "auto-complete-mat": AutoCompleteMat,
  },
  computed: {
    ...mapState(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME, {
      compOrgs: (state: IBuilderStoreState) => state.compOrgs,
      compOrgsLoading: (state: IBuilderStoreState) => state.compOrgsLoading,
    }),
  },
})
export default class OrgCreate extends Vue {
  @Prop() public isoDate: string;

  public readonly compOrgsLoading: boolean;

  public builderService: BuilderService = new BuilderService();
  public orgTypeAheadUserInput: string = "";

  // public createOrg() {
  //     if (this.orgTypeAheadUserInput.length === 0) {
  //         return;
  //     }
  //     this.$store.dispatch(BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME + "/" +
  //         BUILDER_STORE_CONST.BUILDER_ACTIONS_CREATE_ORG, {
  //         id: 0, name: this.orgTypeAheadUserInput
  //     } as IOrg);
  // }

  public compOrgSearchTermChanged(searchKey: string) {
    if (searchKey.length === 0) {
      return;
    }
    this.orgTypeAheadUserInput = searchKey;
    const listParams = this.builderService.getListParamsDefault(searchKey);
    this.$store.dispatch(
      BUILDER_STORE_CONST.BUILDER_CONST_MODULE_NAME +
        "/" +
        BUILDER_STORE_CONST.BUILDER_ACTIONS_GET_COMP_ORGS,
      { listParams }
    );
  }

  public compOrgSelected(autoComplete: IAutoCompleteValue) {
    const compOrg: IOrg = autoComplete.value;
    if (compOrg) {
      this.$emit("onSelected", R.clone(autoComplete.value));
    }
  }

  public cancel() {
    this.$emit("onCancel");
  }
}
</script>
