<template>
  <div style="padding: var(--e4s-gap--standard)">
    <OnboardingForm v-if="isReady" :value="onboardingFormInput" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, SetupContext } from "@vue/composition-api";
import OnboardingForm from "./OnboardingForm.vue";
import {
  useConfigController,
  useConfigStore,
} from "../../../config/useConfigStore";
import { convertOrgToUserOrganisationSimple } from "./onboarding-service";
import { IOnboardingFormInput } from "./onboarding-form-models";

export default defineComponent({
  name: "OnboardingFormRoute",
  components: { OnboardingForm },
  props: {},
  setup(props: any, context: SetupContext) {
    const isReady = ref(false);

    const configStore = useConfigStore();
    const configController = useConfigController();

    const onboardingFormInput: IOnboardingFormInput = {
      theme: configController.getStore.value.configApp.theme,
      isAdmin: configController.isAdmin.value,
      user: {
        id: configStore.configApp.userInfo.user.id,
        name: configStore.configApp.userInfo.user.display_name,
      },
      userOrgs: configStore.configApp.userInfo.orgs.map((org) => {
        return convertOrgToUserOrganisationSimple(org);
      }),
    };

    isReady.value = true;

    return {
      isReady,
      onboardingFormInput,
    };
  },
});
</script>
