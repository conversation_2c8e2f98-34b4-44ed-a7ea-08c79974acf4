<template>
  <div class="e4s-flex-column">
    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2 form-label="Organisation Name">
          <template slot="field">
            <FieldTextV2 v-model="valueInternal.name" />
          </template>
        </FormGenericInputTemplateV2>

        <FormGenericInputTemplateV2 form-label="Organisation Logo (Optional)">
          <template slot="field">
            <div class="e4s-flex-column" style="margin-top: 4px">
              <FileUploadV2
                @onUpload="onLogoUpload"
                ref="fileUploader"
                :do-immediately="false"
              />
            </div>
          </template>
        </FormGenericInputTemplateV2>

        <!--<button @click="$refs.fileUploader.doUploadForm()">Click me</button>-->
      </template>
    </FormGenericFieldGridV2>

    <div class="e4s-vertical-spacer--large"></div>

    <div class="e4s-flex-row">
      <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
        <ButtonGenericV2 text="Cancel" button-type="tertiary" @click="cancel" />
        <ButtonGenericV2 text="Submit" @click="submit" />
      </div>
    </div>

    <!--    {{ valueInternal }}-->

    <LoadingSpinnerV2 v-if="isLoading" />

    <!--    {{ valueInternal }}-->
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";

import { IOnboardingOrganisation } from "../../onboarding/form/onboarding-form-models";
import { IUserSummary } from "../../../admin/user/user-models";
import {
  factoryOnboardingOrganisation,
  validateOnboardingOrganisation,
} from "../../onboarding/form/onboarding-service";
import {
  useConfigController,
  useConfigStore,
} from "../../../config/useConfigStore";
import { simpleClone } from "../../../common/common-service-utils";
import { IClubCrud } from "../../../club/club-models";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import { IOrg, IOrgCreate } from "../../org-models";
import { OrgData } from "../../org-data";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import ClubTypeAheadV2 from "../../../club/v2/clubtypeahead/club-type-ahead-v2.vue";
import FileUploadV2 from "../../../common/ui/layoutV2/file-upload-v2/FileUploadV2.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import AdminIdV2 from "../../../common/ui/layoutV2/admin-id-v2.vue";
import UserTypeAheadV2 from "../../../admin/user/v2/user-type-ahead-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FormGenericFieldGridV2 from "../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";

export default defineComponent({
  name: "OrgFormV2",
  components: {
    LoadingSpinnerV2,
    ButtonGenericV2,
    ClubTypeAheadV2,
    FileUploadV2,
    FieldTextV2,
    PrimaryLink,
    AdminIdV2,
    UserTypeAheadV2,
    FormGenericInputTemplateV2,
    FormGenericInputTextV2,
    FormGenericFieldGridV2,
  },
  props: {
    value: {
      type: Object as PropType<IOnboardingOrganisation>,
      default: function () {
        return factoryOnboardingOrganisation();
      },
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    useStripeConnect: {
      type: Boolean,
      required: true,
    },
  },
  setup(
    props: { value: IOnboardingOrganisation; isDisabled: boolean },
    context: SetupContext
  ) {
    const valueInternal = ref<IOnboardingOrganisation>(props.value);
    const configStore = useConfigStore();
    const configController = useConfigController();
    const showStripeUserSelect = ref(false);
    const fileUploader = ref();
    const isLoading = ref(false);
    const createForUser = ref<IUserSummary>({
      id: 0,
      niceName: "",
      displayName: "",
      email: "",
      login: "",
    });

    watch(
      () => props.value,
      (newValue: IOnboardingOrganisation) => {
        valueInternal.value = simpleClone(newValue);
      },
      {
        immediate: true,
      }
    );

    function onLogoUpload(logoPath: string) {
      //  Being triggered in child by submit button
      // valueInternal.value.logo = logoPath;
      // if (valueInternal.value.id > 0) {
      //   submit();
      // }
    }

    const validationResult = computed(() => {
      return validateOnboardingOrganisation(
        valueInternal.value,
        configStore.configApp.theme
      );
    });

    function onStripeUserSearchSelect(userSummary: IUserSummary): void {
      if (!userSummary || userSummary.id === 0) {
        return;
      }

      valueInternal.value.stripeUser = {
        id: userSummary.id,
        name: userSummary.niceName,
      };
      showStripeUserSelect.value = false;
    }

    function onStripeUserSearchSelectReset() {
      valueInternal.value.stripeUser = {
        id: 0,
        name: "",
      };
    }

    function clubSearchTermChanged(searchTerm: string) {
      valueInternal.value.name = searchTerm;
    }

    function onClubSelected(clubCrud: IClubCrud) {
      valueInternal.value.name = clubCrud.clubname;
    }

    // function submit() {
    //   console.log("OnboardingOrgEdit submitting", valueInternal.value);
    //   context.emit("input", valueInternal.value);
    // }

    /**
     * Not ideal, but thisd reaches into the FileUploadV2 component to trigger the upload
     */
    function submitOrgLogo(): Promise<void> {
      console.log("submitOrgLogo()...fileUploader: ", fileUploader);

      //  E.g. of how to trigger from ui.
      // <button @click="$refs.fileUploader.doUploadForm()">Click me</button>

      // @ts-ignore
      return fileUploader.value.doUploadForm().then((resp) => {
        console.log("submitOrgLogo()...resp: ", resp);
        valueInternal.value.logo = resp;
      });
    }

    function submit(): Promise<void> {
      if (valueInternal.value.name.length === 0) {
        messageDispatchHelper(
          "Organisation name is required",
          USER_MESSAGE_LEVEL.ERROR.toString()
        );
        return Promise.resolve();
      }

      isLoading.value = true;
      return submitOrgLogo().then(() => {
        let orgToCreate = simpleClone(valueInternal.value) as IOrg;
        isLoading.value = true;

        //  E4S Admin can create for another user
        if (createForUser.value.id > 0) {
          const orgCreate: IOrgCreate = {
            ...orgToCreate,
            linkUserId: createForUser.value.id,
          } as IOrgCreate;

          orgToCreate = orgCreate as IOrg;
        }

        const orgData = new OrgData();

        const prom =
          orgToCreate.id > 0
            ? orgData.update(orgToCreate)
            : orgData.create(orgToCreate);

        return prom
          .then((response) => {
            valueInternal.value = response.data as IOnboardingOrganisation;
            context.emit("input", valueInternal.value);
          })
          .finally(() => {
            isLoading.value = false;
          });
      });
    }

    function cancel() {
      console.log("OnboardingOrgEdit submitting", valueInternal.value);
      context.emit("onCancel");
    }

    return {
      isLoading,
      configController,
      valueInternal,
      validationResult,
      onLogoUpload,
      onStripeUserSearchSelect,
      showStripeUserSelect,
      onStripeUserSearchSelectReset,
      onClubSelected,
      clubSearchTermChanged,
      submit,
      cancel,
      fileUploader,
      createForUser,
    };
  },
});
</script>
