<template>
    <div>
        <div class="row">
<!--            <FieldHelpDisplayContent :help-data="helpData"></FieldHelpDisplayContent>-->
            <iframe class="field-help--iframe" :src="helpData.data">
            </iframe>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                <div class="right">
                    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
                    <button
                        class="e4s-button e4s-button--red e4s-button--10-wide"
                        :disabled="isLoading"
                        v-on:click="answer(false)"
                    >
                        Decline
                    </button>
                    <button
                        class="e4s-button e4s-button--green e4s-button--10-wide"
                        :disabled="isLoading"
                        v-on:click="answer(true)"
                    >
                        Accept
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {IUserApplication} from "../../config/config-app-models"
import {IOrg} from "../org-models"
import {OrgData} from "../org-data"
import {handleResponseMessages} from "../../common/handle-http-reponse"
import FieldHelpDisplayContent from "../../common/ui/field/field-help/field-help-display-content.vue"
import {IHelpData} from "../../common/ui/field/field-help/field-help-store"

@Component({
    name: "tandc-organiser-accept",
    components: {FieldHelpDisplayContent}
})
export default class TandcOrganiserAccept extends Vue {
    @Prop({
        required: true
    })
    public readonly org: IOrg;

    @Prop({
        required: true
    })
    public readonly userApplication: IUserApplication;

    public isLoading = false;
    public helpData: IHelpData = {
        id: 111,
        title: "Competition Organiser Terms and Conditions",
        data: "https://support.entry4sports.com/onlinehelp/builder/organiser-terms-and-conditions",
        key: "athlete-cancellations",
        preload: false,
        type: "U"
    }

    public created() {

    }

    public answer(answer: boolean) {
        const payload = {
            userApplication: this.userApplication,
            answer
        };

        if (!answer) {
            this.$emit("answer", payload);
            return;
        }

        const org = R.clone(this.org);
        org.options.tAndCUsers.push( {
            user: {
                id: this.userApplication.id,
                name: this.userApplication.name
            },
            time: new Date().toISOString()
        })
        this.isLoading = true;
        const prom = new OrgData().update(org);
        handleResponseMessages(prom);
        prom.then( () => {
            this.$emit("answer", payload);
        })
        .finally( () => {
            this.isLoading = false;
        })
    }
}
</script>
